<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nspace.group</groupId>
        <artifactId>data-works</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>data-works-framework</artifactId>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <module>data-common</module>
        <module>data-spring-boot-starter-mybatis</module>
        <module>data-spring-boot-starter-redis</module>
        <module>data-spring-boot-starter-job</module>
        <module>data-spring-boot-starter-banner</module>
        <module>data-spring-boot-starter-file</module>
    </modules>

</project>