package com.nspace.group.framework.redis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Cache 配置项
 * 用于配置和管理缓存相关属性，例如 Redis 扫描批量大小。
 * <AUTHOR>
 */
@ConfigurationProperties("spring.cache")
@Data
public class DataWorksCacheProperties {
    /**
     * {@link #redisScanBatchSize} 默认值
     */
    private static final Integer REDIS_SCAN_BATCH_SIZE_DEFAULT = 30;

    /**
     * redis scan 一次返回数量
     */
    private Integer redisScanBatchSize = REDIS_SCAN_BATCH_SIZE_DEFAULT;
}
