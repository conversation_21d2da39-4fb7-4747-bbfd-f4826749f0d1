package com.nspace.group.framework.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 地域枚举
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/1
 * @time：17:57
 */
public enum RegionEnum {
    CN("cn", "中国大陆", "China Mainland", "cn"),
    AP1("ap1", "亚太一区", "Asia Pacific I", "ncm"),
    AP2("ap2", "亚太二区", "Asia Pacific II", "ncm"),
    AP3("ap3", "亚太三区", "Asia Pacific III", "ncm"),
    EU("eu", "欧洲", "Europe", "ncm"),
    NA("na", "北美", "North America", "ncm"),
    SA("sa", "南美", "South America", "ncm"),
    MEAA("meaa", "中东非洲", "Middle East", "ncm"),
    ;

    @Getter
    private final String region;

    @Getter
    private final String regionDesc;

    @Getter
    private final String txRegion;

    @Getter
    private final String billRegion;

    private static Map<String, RegionEnum> statusEnumMap;

    RegionEnum(String region, String regionDesc, String txRegion, String billRegion) {
        this.region = region;
        this.regionDesc = regionDesc;
        this.txRegion = txRegion;
        this.billRegion = billRegion;
    }

    static {
        statusEnumMap = Collections.unmodifiableMap(Arrays.stream(RegionEnum.values())
                .collect(Collectors.toMap(RegionEnum::getRegion, regionEnum -> regionEnum)));
    }

    public static String getBillRegionByRegion(String region) {
        RegionEnum regionEnum = statusEnumMap.get(region);
        if (regionEnum != null) {
            return regionEnum.getBillRegion();
        }
        return null;
    }

    public static String getTxRegionByRegion(String region) {
        RegionEnum regionEnum = statusEnumMap.get(region);
        if (regionEnum != null) {
            return regionEnum.getTxRegion();
        }
        return null;
    }
}