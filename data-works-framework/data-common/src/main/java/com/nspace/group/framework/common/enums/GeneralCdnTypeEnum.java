package com.nspace.group.framework.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 通用CDN类型
 */
@Getter
@AllArgsConstructor
public enum GeneralCdnTypeEnum {
    GENERAL("GENERAL", "通用");

    /**
     * 类型code
     */
    private final String code;
    /**
     * 类型名称
     */
    private final String name;

    public boolean isSelf(String targetType) {
        return isGeneralCdnType(targetType) && this.getCode().equals(targetType);
    }

    public static boolean isGeneralCdnType(String targetType) {
        return Arrays.stream(values()).map(GeneralCdnTypeEnum::getName).anyMatch(value -> value.equals(targetType));
    }

}
