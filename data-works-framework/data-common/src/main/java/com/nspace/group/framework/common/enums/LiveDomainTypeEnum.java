package com.nspace.group.framework.common.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 域名类型
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/6
 * @time：13:56
 */
public enum LiveDomainTypeEnum {
    DOMAIN_TYPE_PULL("PULL", "拉流"),
    DOMAIN_TYPE_PUSH("PUSH", "推流"),

    ;

    /**
     * 类型code
     */
    @Getter
    private final String code;
    /**
     * 类型名称
     */
    @Getter
    private final String name;

    LiveDomainTypeEnum(String code,String name) {
        this.code = code;
        this.name = name;
    }

    public boolean isSelf(String targetType) {
        return isLiveDomainType(targetType) && this.getCode().equals(targetType);
    }

    public static boolean isLiveDomainType(String targetType) {
        return Arrays.stream(values()).map(LiveDomainTypeEnum::getCode).anyMatch(value -> value.equals(targetType));
    }

    public static String getCode(String code) {
        for (LiveDomainTypeEnum item : LiveDomainTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item.getCode();
            }
        }
        return null;
    }
}
