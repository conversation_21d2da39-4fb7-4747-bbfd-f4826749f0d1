package com.nspace.group.framework.common.enums;

import cn.hutool.core.util.StrUtil;
import com.nspace.group.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;
import java.util.regex.Pattern;

@Getter
@AllArgsConstructor
public enum StreamProtocolTypeEnum implements IntArrayValuable {
    RTMP(0, "rtmp", Pattern.compile("^rtmp://.+$")),
    HLS(1, "hls", Pattern.compile("^(((https?://\\S+)?)|(/\\S+))\\.m3u8((\\?\\S*)|(\\??))$")),
    HTTP_FLV(2, "http_flv", Pattern.compile("^(((https?://\\S+)?)|(/\\S+))\\.flv((\\?\\S*)|(\\??))$")),
    DASH(3, "dash", Pattern.compile("^(((https?://\\S+)?)|(/\\S+))\\.mpd((\\?\\S*)|(\\??))$")),
    RTSP(4, "rtsp", Pattern.compile("^rtsp://.+$"));

    /**
     *
     */
    private final Integer type;

    /**
     * 名称
     */
    private final String name;

    /**
     * 正则表达式
     */
    private final Pattern regex;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(StreamProtocolTypeEnum::getType).toArray();

    /**
     * 返回匹配到的流协议
     * @param url 流地址
     * @return StreamProtocolTypeEnum or ""
     */
    public static String getProtocolType(String url) {
        if (StrUtil.isBlank(url)) return "";
        String tempUrl = url.toLowerCase();
        Optional<String> firstMatched = Arrays.stream(values())
                .filter(protocolType -> protocolType.getRegex().matcher(tempUrl).matches())
                .map(StreamProtocolTypeEnum::getName)
                .findFirst();
        return firstMatched.orElse("");
    }


    @Override
    public int[] array() {
        return ARRAYS;
    }

}
