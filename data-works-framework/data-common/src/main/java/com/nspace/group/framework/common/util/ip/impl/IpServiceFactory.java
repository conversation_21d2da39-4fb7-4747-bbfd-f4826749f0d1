package com.nspace.group.framework.common.util.ip.impl;

import java.io.File;

public class IpServiceFactory {
    public static IpService createIpService(File database){
        String fileName = database.getName().toLowerCase();
        try{
            if (fileName.endsWith(".mmdb")) {
                return new MaxMindIpService(database);
            } else if (fileName.endsWith(".ipdb")) {
                return new IpipdbService(database);
            }
        }catch (Exception e){
            throw new IllegalArgumentException("Unsupported file extension: " + fileName);
        }
        return null;
    }
}
