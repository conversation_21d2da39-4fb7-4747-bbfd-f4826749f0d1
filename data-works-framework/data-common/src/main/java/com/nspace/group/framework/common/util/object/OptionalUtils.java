package com.nspace.group.framework.common.util.object;

import com.nspace.group.framework.common.pojo.IpCityInfo;

import java.util.Optional;
import java.util.function.Function;

/**
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/7
 * @time：20:59
 */
public class OptionalUtils {

    public static <T, R> R mapOrDefault(T obj, Function<T, R> mapper) {
        return mapOrDefault(obj, mapper, (R) "");
    }
    public static <T, R> R mapOrDefault(T obj, Function<T, R> mapper, R defaultValue) {
        return Optional.ofNullable(obj).map(mapper).orElse(defaultValue);
    }
}
