package com.nspace.group.framework.common.util.ip.impl;

import com.maxmind.db.Reader;
import com.maxmind.db.MaxMindDbConstructor;
import com.maxmind.db.MaxMindDbParameter;
import net.ipip.ipdb.CityInfo;

import java.io.File;
import java.io.IOException;
import java.net.InetAddress;

public class MaxMindIpService implements IpService {

    private final Reader reader;

    public MaxMindIpService(File database) throws IOException {
        if (!database.exists()) {
            throw new IllegalArgumentException("Database file does not exist: " + database.getAbsolutePath());
        }
        this.reader = new Reader(database);
    }

    @Override
    public CityInfo lookupIp(String ipAddress) {
        try {
            InetAddress address = InetAddress.getByName(ipAddress);
            LookupResult result = reader.get(address, LookupResult.class);
            /**
             * 将result 值转换为 string[] data
             */
            String data[] = new String[]{result.getCountry(), result.getAsn(), result.getOrganization()};
            if (result != null) {
                return new CityInfo(data);
            } else {
                System.out.println("No data found for IP: " + address.getHostAddress());
            }
        } catch (IOException e) {
            System.err.println("IOException occurred: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    public static class LookupResult {
        private final String country;
        private final String asn;
        private final String organization;

        @MaxMindDbConstructor
        public LookupResult(
                @MaxMindDbParameter(name = "country") String country,
                @MaxMindDbParameter(name = "asn") String asn,
                @MaxMindDbParameter(name = "organization") String organization
        ) {
            this.country = country;
            this.asn = asn;
            this.organization = organization;
        }
        public String getCountry() {
            return this.country;
        }
        public String getAsn() {
            return this.asn;
        }
        public String getOrganization() {
            return this.organization;
        }
    }
}