package com.nspace.group.framework.common.util.number;

/**
 * 流量带宽常用
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/1
 * @time：20:44
 */
public class NetworkUtils {
    /**
     * 带宽转换
     */

    /**
     * 带宽转换 Mbps -> bps
     */
    public static long bandwidthMbpsToBps(float bandwidth) {
        return (long) (bandwidth * 1024 * 1024);
    }


    /**
     * 计算带宽在指定时间内传输的流量
     *
     * @param bandwidthInBps 带宽，以 bps 为单位（比特每秒）
     * @return 传输的流量，单位为字节
     */
    public static long calculateTrafficInBytes(long bandwidthInBps) {
        return bandwidthInBps / 8;
    }

    /**
     * 流量转换 MB -> B
     *
     * @param traffic
     * @return
     */
    public static long trafficMBToB(float traffic) {
        return (long) (traffic * 1024 * 1024);
    }
}
