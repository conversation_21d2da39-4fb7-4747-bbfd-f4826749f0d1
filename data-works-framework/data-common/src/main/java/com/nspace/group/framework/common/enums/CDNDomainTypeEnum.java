package com.nspace.group.framework.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :CDNDomainTypeEnum.java, v0.1 2025年02月21日 13:33 luke Exp
 */
@Getter
public enum CDNDomainTypeEnum {
    WEB_RESOURCE("WEB_RESOURCE", "网页小文件"),
    BIG_FILE("BIG_FILE", "大文件下载"),
    VOD("VOD", "音视频点播"),

    ;
    /**
     * 类型
     */
    private final String code;
    /**
     * 类型名
     */
    private final String desc;

    /**
     * 转化
     */
    private static final Map<String, CDNDomainTypeEnum> enumMap;

    CDNDomainTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    static {
        enumMap = Collections.unmodifiableMap(Arrays.stream(CDNDomainTypeEnum.values()).collect(Collectors.toMap(s -> s.code, s -> s)));
    }

    public boolean isSelf(String targetType) {
        return isCdnDomainType(targetType) && this.getCode().equals(targetType);
    }

    public static boolean isCdnDomainType(String targetType) {
        return Arrays.stream(values()).map(CDNDomainTypeEnum::getCode).anyMatch(value -> value.equals(targetType));
    }

    public static CDNDomainTypeEnum findByCode(String code) {
        if (StrUtil.isBlank(code)) {
            return null;
        }
        return enumMap.get(code);
    }
}
