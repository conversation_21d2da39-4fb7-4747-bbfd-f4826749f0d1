package com.nspace.group.framework.common.core;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * KeyValue键值对
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/30
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class KeyValue<K, V> implements Serializable {
    private K key;
    private V value;
}
