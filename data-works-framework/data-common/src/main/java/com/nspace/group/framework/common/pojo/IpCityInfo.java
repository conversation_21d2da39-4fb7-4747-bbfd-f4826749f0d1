package com.nspace.group.framework.common.pojo;

import com.nspace.group.framework.common.enums.IspEnum;
import com.nspace.group.framework.common.enums.ProvinceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Ip地址对应的城市信息
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/1
 * @time：13:51
 */

@Data
@AllArgsConstructor
@NoArgsConstructor

public class IpCityInfo {
    public String country;
    public String city;
    public String ispName;
    public Object cityJson;
    public String countryCode;
    public String cityCode;
    public String ispCode;
}
