package com.nspace.group.framework.common.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 环境枚举
 * <AUTHOR>
 * @version :EvnEnum.java, v0.1 2023年08月22日 19:20 luke Exp
 */
public enum EvnEnum {
    DEV("DEV", "开发环境"),
    PRE("PRE", "预发环境"),
    PROD("PROD", "线上环境"),

    ;

    @Getter
    private final String code;

    @Getter
    private final String desc;

    private static Map<String, EvnEnum> statusEnumMap;

    EvnEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    static {
        statusEnumMap = Collections.unmodifiableMap(Arrays.stream(EvnEnum.values())
                .collect(Collectors.toMap(s -> s.getCode(), s -> s)));
    }

    public static EvnEnum getByCode(String code) {
        return statusEnumMap.get(code);
    }
}