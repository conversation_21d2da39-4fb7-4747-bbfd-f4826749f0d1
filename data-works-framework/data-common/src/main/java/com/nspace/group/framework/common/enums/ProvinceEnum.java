package com.nspace.group.framework.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 全国省份enum
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/12
 * @time：11:29
 */
public enum ProvinceEnum {
    // 东北地区
    HEILONGJIANG("黑龙江", "heilongjiang"),
    JILIN("吉林", "jilin"),
    LIAONING("辽宁", "liaoning"),
    // 华北地区
    BEIJING("北京", "beijing"),
    TIANJIN("天津", "tianjin"),
    HEBEI("河北", "hebei"),
    SHANXI("山西", "shanxi"),
    INNER_MONGOLIA("内蒙古", "neimenggu"),
    // 华东地区
    SHANGHAI("上海", "shanghai"),
    JIANGSU("江苏", "jiangsu"),
    ZHEJIANG("浙江", "zhejiang"),
    ANHUI("安徽", "anhui"),
    FUJIAN("福建", "fujian"),
    JIANGXI("江西", "jiangxi"),
    SHANDONG("山东", "shandong"),
    // 华中地区
    HENAN("河南", "henan"),
    HUBEI("湖北", "hubei"),
    HUNAN("湖南", "hunan"),
    // 华南地区
    GUANGDONG("广东", "guangdong"),
    GUANGXI("广西", "guangxi"),
    HAINAN("海南", "hainan"),
    // 西南地区
    CHONGQING("重庆", "chongqing"),
    SICHUAN("四川", "sichuan"),
    GUIZHOU("贵州", "guizhou"),
    YUNNAN("云南", "yunnan"),
    TIBET("西藏", "xizang"),
    // 西北地区
    SHAANXI("陕西", "shanxi"),
    GANSU("甘肃", "gansu"),
    QINGHAI("青海", "qinghai"),
    NINGXIA("宁夏", "ningxia"),
    XINJIANG("新疆", "xinjiang"),
    // 港澳台
    HONG_KONG("香港", "hongkong"),
    MACAO("澳门", "macao"),
    TAIWAN("台湾", "taiwan");

    private final String chineseName;
    private final String englishName;

    private static final Map<String, ProvinceEnum> CHINESE_NAME_MAP = new HashMap<>();
    private static final Map<String, ProvinceEnum> ENGLISH_NAME_MAP = new HashMap<>();

    static {
        for (ProvinceEnum province : ProvinceEnum.values()) {
            CHINESE_NAME_MAP.put(province.getChineseName(), province);
            ENGLISH_NAME_MAP.put(province.getEnglishName().toLowerCase(), province); // 转小写避免大小写问题
        }
    }

    ProvinceEnum(String chineseName, String englishName) {
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public static String fromChineseName(String chineseName) {
        ProvinceEnum provinceEnum = CHINESE_NAME_MAP.get(chineseName);
        if (provinceEnum != null) {
            return provinceEnum.getEnglishName().toLowerCase();
        }
        for (ProvinceEnum province : ProvinceEnum.values()) {
            if (chineseName.contains(province.getChineseName())) {
                return province.getEnglishName().toLowerCase();
            }
        }
        return null;
    }
}