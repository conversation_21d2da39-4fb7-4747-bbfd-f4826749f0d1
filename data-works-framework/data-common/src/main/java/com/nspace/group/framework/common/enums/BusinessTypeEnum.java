package com.nspace.group.framework.common.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 业务类型枚举类
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/6
 * @time：11:45
 */
public enum BusinessTypeEnum {

    BUSINESS_TYPE_LSS("LSS", "云直播"),
    BUSINESS_TYPE_CDN("CDN", "通用CDN"),
    ;

    /**
     * 业务code
     */
    @Getter
    private final String code;
    /**
     * 业务描述
     */
    @Getter
    private final String desc;

    BusinessTypeEnum( String code,String desc) {
        this.code = code;
        this.desc = desc;
    }

    public boolean isSelf(String code) {
        return isBusinessType(code) && this.getCode().equals(code);
    }

    public static boolean isBusinessType(String bizType) {
        return Arrays.stream(values()).map(BusinessTypeEnum::getCode).anyMatch(value -> value.equals(bizType));
    }
}
