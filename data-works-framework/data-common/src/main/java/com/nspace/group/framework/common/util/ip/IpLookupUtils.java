package com.nspace.group.framework.common.util.ip;

import com.nspace.group.framework.common.pojo.IpCityInfo;
import com.nspace.group.framework.common.util.ip.impl.IpService;
import com.nspace.group.framework.common.util.ip.impl.IpServiceFactory;
import net.ipip.ipdb.CityInfo;

import java.io.File;
import java.io.IOException;

public class IpLookupUtils {

    /**
     * Perform IP lookup.
     * Example: IpLookupUtils.lookupIp("**************", database);
     *
     * @param ipAddress IP address
     * @param database  IP database file
     * @return IpCityInfoBean with IP data or empty bean if lookup fails
     */
    public static IpCityInfo ipCityInfo(String ipAddress, File database) {
        if (ipAddress == null) {
            return createEmptyIpCityInfoBean();
        }
        int colonIndex = ipAddress.indexOf(':');
        if (colonIndex != -1) {
            ipAddress = ipAddress.substring(0, colonIndex);
        }
        try {
            IpService ipService = IpServiceFactory.createIpService(database);
            CityInfo cityInfo = ipService.lookupIp(ipAddress);
            return cityInfo != null ? buildIpCityInfoBean(cityInfo) : createEmptyIpCityInfoBean();
        } catch (IOException e) {
            return createEmptyIpCityInfoBean();
        }
    }

    private static IpCityInfo buildIpCityInfoBean(CityInfo cityInfo) {
        IpCityInfo ipCityBean = new IpCityInfo();
        String[] countrySplit = cityInfo.getCountryName().split("–", 5);
        ipCityBean.setCountry(countrySplit.length > 0 ? countrySplit[0] : "");
        String city = (countrySplit.length > 1 && (cityInfo.getCityName() == null || cityInfo.getCityName().isEmpty()))
                ? countrySplit[1]
                : cityInfo.getCityName();
        ipCityBean.setCity(city != null ? city : "");
        ipCityBean.setIspName(cityInfo.getIspDomain() != null ? cityInfo.getIspDomain() : "");
        ipCityBean.setCityJson(cityInfo);
        return ipCityBean;
    }

    private static IpCityInfo createEmptyIpCityInfoBean() {
        return new IpCityInfo("", "", "", null, null, null, null);
    }
}