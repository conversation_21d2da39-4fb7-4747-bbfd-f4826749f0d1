package com.nspace.group.framework.common.util.region;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :TencentCodeMappingHandler.java, v0.1 2024年11月18日 14:33 zhangxin Exp
 */
public class RegionUtil {

    //区域、计费大区映射
    private static final JSONObject billingRegionJson = JSONUtil.parseObj("{\"cn\":\"cn\",\"ap1\":\"ncm\",\"ap2\":\"ncm\",\"ap3\":\"ncm\"," +
            "\"eu\":\"ncm\",\"na\":\"ncm\",\"sa\":\"ncm\",\"me\":\"ncm\",\"af\":\"ncm\"}");

    //省份映射
    private static final Map<String, String> billingRegionMapping;


    static {
        TypeReference<Map<String, String>> billingRegionTypeReference = new TypeReference<>() {};
        billingRegionMapping = billingRegionJson.toBean(billingRegionTypeReference).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 获取天翼abroad字段对应耕耘计费区域
     *
     * @param regionCode 耕耘区域编码
     * @return 编码对应值
     */
    public static String regionToBillingRegion(String regionCode) {
        return billingRegionMapping.get(regionCode);
    }
}
