package com.nspace.group.framework.common.util.ip.impl;

import net.ipip.ipdb.City;
import net.ipip.ipdb.CityInfo;
import net.ipip.ipdb.IPFormatException;

import java.io.File;
import java.io.IOException;

public class IpipdbService implements IpService {

    private final City city;

    public IpipdbService(File database) throws IOException {
        if (database == null) {
            throw new IllegalArgumentException("Database file cannot be null");
        }
        if (!database.exists()) {
            throw new IllegalArgumentException("Database file does not exist: " + database.getAbsolutePath());
        }
        try {
            this.city = new City(database.getAbsolutePath());
        } catch (IOException e) {
            throw new IOException("Failed to initialize City with database file: " + database.getAbsolutePath(), e);
        }
    }

    @Override
    public CityInfo lookupIp(String ipAddress) throws IOException {
        if (ipAddress == null || ipAddress.isEmpty()) {
            throw new IllegalArgumentException("IP address cannot be null or empty");
        }
        try {
            return city.findInfo(ipAddress, "CN");
        } catch (IPFormatException e) {
            throw new IOException("Invalid IP format: " + ipAddress, e);
        }
    }
}
