package com.nspace.group.framework.common.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 运营商信息定义
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/12
 * @time：11:19
 */
public enum IspEnum {
    HUASHU("华数", "huashu"),
    PBS("鹏博士", "pbs"),
    OC("广电", "oc"),
    CM("移动", "cm"),
    UN("联通", "un"),
    CT("电信", "ct");

    private final String label;
    private final String value;

    private static final Map<String, IspEnum> VALUE_MAP = new HashMap<>();
    private static final Map<String, String> LABEL_TO_VALUE_MAP = new HashMap<>();

    static {
        for (IspEnum code : IspEnum.values()) {
            VALUE_MAP.put(code.value, code);
            LABEL_TO_VALUE_MAP.put(code.label, code.value);
        }
    }

    IspEnum(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    public static String fromValue(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        IspEnum result = VALUE_MAP.get(value);
        if (result != null) {
            return result.getValue().toLowerCase();
        }
        for (IspEnum ispEnum : IspEnum.values()) {
            if (value.contains(ispEnum.getLabel())) {
                return ispEnum.getValue().toLowerCase();
            }
        }
        return null;
    }
}
