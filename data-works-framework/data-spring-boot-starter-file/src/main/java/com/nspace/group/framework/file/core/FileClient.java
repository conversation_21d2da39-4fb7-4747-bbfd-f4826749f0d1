package com.nspace.group.framework.file.core;

import com.nspace.group.framework.file.core.dto.FilePresignedUrlRespDTO;
import com.nspace.group.framework.file.core.dto.MetaDTO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :FileClient.java, v0.1 2024年06月27日 17:35 Exp
 */
public interface FileClient {
    /**
     * 上传文件
     *
     * @param content 文件流
     * @param path    相对路径
     * @param tagMap  文件tag
     * @return 完整路径，即 HTTP 访问地址
     * @throws Exception 上传文件时，抛出 Exception 异常
     */
    String upload(byte[] content, String bucket, String path, String type, Map<String, String> tagMap) throws Exception;


    /**
     * 上传文件
     *
     * @param bucket      bucket 名称
     * @param objectKey   objectKey
     * @param filePath    本地文件地址
     * @param contentType 文件格式
     * @param tagMap      文件tag
     * @return 完整路径，即 HTTP 访问地址
     */
    String upload(String bucket, String objectKey, String filePath, String contentType, Map<String, String> tagMap);


    /**
     * 下载文件到本地
     *
     * @param bucket    bucket 名称
     * @param objectKey objectKey
     * @param path      保存到本的路径
     */
    void download(String bucket, String objectKey, String path);


    /**
     * 查看对象信息
     *
     * @param bucket    bucket 名称
     * @param objectKey objectKey
     * @return
     */
    MetaDTO statObject(String bucket, String objectKey);

    /**
     * 删除文件
     *
     * @param path 相对路径
     * @throws Exception 删除文件时，抛出 Exception 异常
     */
    void delete(String bucket, String path);


    /**
     * 批量删除文件
     *
     * @param bucket
     * @param objectKey
     */
    void batchDelete(String bucket, List<String> objectKey);

    /**
     * 获得文件的内容
     *
     * @param path 相对路径
     * @return 文件的内容
     */
    byte[] getContent(String bucket, String path) throws Exception;

    /**
     * 校验桶是否存在，不存在则创建
     *
     * @param bucket 桶
     * @param policy 策略配置Json串
     */
    void checkCreateBucket(String bucket, String policy) throws Exception;

    /**
     * @param bucket 桶
     * @param days   过期天数
     */
    void setBucketExpiration(String bucket, List<Integer> days) throws Exception;

    /**
     * 获得文件预签名上传地址
     *
     * @param path 相对路径
     * @return 文件预签名地址
     */
    default FilePresignedUrlRespDTO getPresignedObjectUploadUrl(String bucket, String path) throws Exception {
        throw new UnsupportedOperationException("不支持的操作");
    }

    /**
     * 获得文件预签名下载地址
     *
     * @param path 相对路径
     * @return 文件预签名地址
     */
    default FilePresignedUrlRespDTO getPresignedObjectDownloadUrl(String bucket, String path) throws Exception {
        throw new UnsupportedOperationException("不支持的操作");
    }


    /**
     * 合并文件
     *
     * @param bucket         文件桶
     * @param objectKey      相对路径
     * @param tagMap         文件tag
     * @param sourceInfoList 源文件列表
     */
    void composeObject(String bucket, String objectKey, Map<String, String> tagMap, Collection<String[]> sourceInfoList);


}
