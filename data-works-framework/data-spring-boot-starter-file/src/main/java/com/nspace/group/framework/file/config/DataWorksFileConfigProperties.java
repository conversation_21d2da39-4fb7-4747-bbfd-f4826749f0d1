package com.nspace.group.framework.file.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @version :DataWorksFileConfigProperties.java, v0.1 2024年12月17日 17:44 Exp
 */
@ConfigurationProperties(prefix = "spring.minio")
@Data
public class DataWorksFileConfigProperties {
    /**
     * 节点地址
     */
    private String endpoint;
    /**
     * 自定义域名
     * 1. MinIO：通过 Nginx 配置
     */
    private String domain;

    /**
     * 访问 Key
     */
    private String accessKey;
    /**
     * 访问 Secret
     */
    private String secretKey;
}
