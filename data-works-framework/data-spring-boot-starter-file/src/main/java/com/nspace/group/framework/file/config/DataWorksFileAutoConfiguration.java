package com.nspace.group.framework.file.config;

import com.nspace.group.framework.file.core.FileClient;
import com.nspace.group.framework.file.core.impl.FileClientImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @version :DataWorksFileAutoConfiguration.java, v0.1 2024年12月17日 17:34 Exp
 */
@AutoConfiguration
@EnableConfigurationProperties(DataWorksFileConfigProperties.class)
public class DataWorksFileAutoConfiguration {
    @Bean
    public FileClient fileClient(DataWorksFileConfigProperties properties) {
        return new FileClientImpl(properties);
    }
}
