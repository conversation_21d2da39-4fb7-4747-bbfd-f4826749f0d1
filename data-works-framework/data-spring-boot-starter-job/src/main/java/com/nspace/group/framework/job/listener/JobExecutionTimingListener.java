package com.nspace.group.framework.job.listener;

import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.listeners.JobListenerSupport;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class JobExecutionTimingListener extends JobListenerSupport {

    private final Map<JobKey, Long> jobStartTimes = new ConcurrentHashMap<>();
    private final Map<JobKey, Long> jobStartMemory = new ConcurrentHashMap<>();

    private static final long MAX_EXECUTION_TIME_MS = 5000; // 5 seconds
    private static final long MAX_USED_MEMORY_MB = 5120;    // 5 GB

    @Override
    public String getName() {
        return "jobExecutionTimingListener";
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext context) {
        JobKey jobKey = context.getJobDetail().getKey();
        long startTime = System.currentTimeMillis();
        long usedMemoryBefore = getUsedMemoryMB();

        jobStartTimes.put(jobKey, startTime);
        jobStartMemory.put(jobKey, usedMemoryBefore);

        log.info("Quartz-Job [{}] is about to start. StartTime={}, Memory={} MB", jobKey, startTime, usedMemoryBefore);
    }

    @Override
    public void jobExecutionVetoed(JobExecutionContext context) {
        JobKey jobKey = context.getJobDetail().getKey();
        log.warn("Quartz-Job [{}] was vetoed and will not be executed.", jobKey);
    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
        JobKey jobKey = context.getJobDetail().getKey();
        Long startTime = jobStartTimes.remove(jobKey);
        Long memoryBefore = jobStartMemory.remove(jobKey);

        long memoryAfter = getUsedMemoryMB();
        long memoryDelta = (memoryBefore != null) ? memoryAfter - memoryBefore : -1;

        long duration = (startTime != null) ? System.currentTimeMillis() - startTime : -1;
        boolean success = (jobException == null);

        String status = success ? "SUCCESS" : "FAILED";

        log.info("Quartz-Job [{}] completed. Status: {}, Duration: {} ms, Memory Delta: {} MB (Before: {}, After: {}), Thread: {} (ID: {}), System Load: {}",
                jobKey, status, duration, memoryDelta, memoryBefore, memoryAfter,
                Thread.currentThread().getName(), Thread.currentThread().getId(),
                getSystemLoad());

        if (duration > MAX_EXECUTION_TIME_MS) {
            log.warn("Quartz-Job [{}] execution time exceeded threshold! Duration: {} ms (Threshold: {} ms)", jobKey, duration, MAX_EXECUTION_TIME_MS);
        }

        if (memoryAfter > MAX_USED_MEMORY_MB) {
            log.warn("Quartz-Job [{}] memory usage exceeded threshold! Current: {} MB (Threshold: {} MB)", jobKey, memoryAfter, MAX_USED_MEMORY_MB);
        }

        if (!success) {
            log.error("Quartz-Job [{}] execution threw an exception", jobKey, jobException);
        }
    }

    private long getUsedMemoryMB() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        long heapUsed = memoryBean.getHeapMemoryUsage().getUsed();
        return heapUsed / 1024 / 1024;
    }

    private double getSystemLoad() {
        OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
        return osBean.getSystemLoadAverage();
    }
}