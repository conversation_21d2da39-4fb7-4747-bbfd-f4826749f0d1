package com.nspace.group.framework.job.config;


import lombok.extern.slf4j.Slf4j;
import org.quartz.JobListener;
import org.springframework.boot.autoconfigure.quartz.SchedulerFactoryBeanCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableScheduling
@Slf4j
public class QuartzCustomizerConfig {

    @Bean
    public SchedulerFactoryBeanCustomizer schedulerFactoryBeanCustomizer(JobListener jobExecutionTimingListener) {
        return factoryBean -> factoryBean.setGlobalJobListeners(jobExecutionTimingListener);
    }
}