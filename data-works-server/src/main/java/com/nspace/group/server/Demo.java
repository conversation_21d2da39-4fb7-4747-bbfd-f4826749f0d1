package com.nspace.group.server;

import io.minio.*;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class Demo {

    public static void main(String[] args) throws Exception {
        MinioClient client = MinioClient.builder()
                .endpoint("http://hznspaceminioapi1.gycloud.com") // Endpoint URL
                .credentials("nspace_test", "test123456") // 认证密钥
                .build();
        ListObjectsArgs listObjectsArgs = ListObjectsArgs.builder().bucket("live").build();
        Iterable<Result<Item>> results = client.listObjects(listObjectsArgs);
        List<String> objectKeys = new ArrayList<>();
        long totalSize = 0;
        for (Result<Item> result : results) {
            Item item = result.get();
            long size = item.size();
            if (size > ObjectWriteArgs.MIN_MULTIPART_SIZE) {
                objectKeys.add(item.objectName());
                totalSize += size;
            }
        }
        log.info("item count: {},totalSize:{}", objectKeys.size(), totalSize);
        List<ComposeSource> composeSources = objectKeys.stream().map(objectKey -> {
            return ComposeSource.builder().bucket("live").object(objectKey).build();
        }).collect(Collectors.toList());
        ComposeObjectArgs composeObjectArgs = ComposeObjectArgs.builder()
                .sources(composeSources)
                .bucket("live")
                .object("final-object.gz")
                .build();
        client.composeObject(composeObjectArgs);
    }
}
