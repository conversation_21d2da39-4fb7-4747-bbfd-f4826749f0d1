spring:
  application:
    name: data-works-server
  profiles:
    active: local
  main:
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER

  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 Date 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 1611460870.401，而是直接 1611460870401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

# 引入子配置文件目录 config/
spring.config.import:
  - optional:classpath:middleware/application-mybatis.yml

--- #################### 业务相关配置 ####################
dataworks:
  info:
    version: 1.0.0
    base-package: com.nspace.group
  env: DEV
  threadpool:
    corePoolSize: 10
    maxPoolSize: 20
    queueCapacity: 30
    keepAliveSeconds: 60
  logs:
    offline:
      path: /tmp/logs/offline
      total-batch: 12
      command: ${GZIP_COMMAND:gzip}
      fetcher-spec: ${OFFLINE_FETCHER_SPEC:3,3,10000}
      compressor-spec: ${COMPRESSOR_FETCHER_SPEC:3,3,10000}
      io-executor-spec: ${OFFLINE_IO_EXECUTOR_SPEC:2,4,10000}
      buffer-size: 10485760
logging:
  level:
    com.nspace.group.module.logs.utils: ${LOGS_MODULE_LEVEL:info}
--- #################### 消息队列相关 ####################
spring:
  kafka:
    producer:
      acks: all # 0-不应答。1-leader 应答。all-所有 leader 和 follower 应答。
      retries: 10 # 发送失败时，重试发送的次数
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer # 消息的 value 的序列化
      compression-type: lz4 #压缩方式，默认发送不进行压缩（none）
      batch-size: 1638400 #每一批次消息发送量的字节数上限
      # 超时和重试配置
      properties:
        delivery.timeout.ms: 30000   # 设置消息发送的最大超时时间为30秒
        request.timeout.ms: 15000    # 设置单个请求的响应超时为15秒
        linger.ms: 10                 # 设置消息批量发送的延迟时间为10毫秒
        retries: 3                   # 设置消息发送失败时重试次数为3次
        retry.backoff.ms: 500       # 设置重试之间的间隔时间为500ms
    # Kafka Consumer 配置项
    consumer:
      enable-auto-commit: false
      auto-offset-reset: earliest # 设置消费者分组最初的消费进度为 earliest 。可参考博客 https://blog.csdn.net/lishuangzhe7047/article/details/74530417 理解
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    # Kafka Consumer Listener 监听器配置
    listener:
      missing-topics-fatal: false # 消费监听接口监听的主题不存在时，默认会报错。所以通过设置为 false ，解决报错