name: build
run-name: ${{ gitea.actor }} building ${{ gitea.repository }} 🚀
on:
  push:
    branches:
      - main
    tags-ignore:
      - '**'

jobs:
  Build:
    runs-on: ubuntu-latest
    container:
      image: dockerimgs.gycloud.com/nspace_builder:latest
      credentials:
        username: ${{ secrets.GYC_REGISTRY_USER }}
        password: ${{ secrets.GYC_REGISTRY_PASSWD }}
    steps:
      - run: echo "🎉 The job was automatically triggered by a ${{ gitea.event_name }} event."
      - run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by Gite<PERSON>!"
      - run: echo "🔎 The name of your branch is ${{ gitea.ref }} and your repository is ${{ gitea.repository }}."
      - name: Check out repository code
        uses: ops/checkout@v4
        with:
          submodules: true
          token: ${{ secrets.GIT_REPO_TOKEN }}
      - run: echo "💡 The ${{ gitea.repository }} repository has been cloned to the runner."
      - name: Cache local Maven repository
        uses: ops/cache@v3
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
      - name: build
        run: |
          cd ${{ gitea.workspace }}
          make
      - run: echo "🍏 This job's status is ${{ job.status }}."
