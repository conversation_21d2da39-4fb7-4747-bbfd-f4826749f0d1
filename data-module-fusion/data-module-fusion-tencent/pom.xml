<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nspace.group</groupId>
        <artifactId>data-module-fusion</artifactId>
        <version>${revision}</version>
    </parent>
    <artifactId>data-module-fusion-tencent</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>
        腾讯云数据融合模块
        1.实时监控
            1.0 查询某条流上行推流质量数据-推流帧率码率信息带宽信息
            1.1 获取在线流的推流数据-推流帧率码率信息
        2.费用统计
            2.1 直播推流带宽和流量数据查询
            2.2 直播播放带宽和流量数据查询
    </description>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-spring-boot-starter-job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-module-infra</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jodd</groupId>
            <artifactId>jodd-core</artifactId>
            <version>5.1.6</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>