package com.nspace.group.module.fusion.tencent.service.stat.realtime;

import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;

/**
 * 实时监控-推流相关
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：11:22
 */
public interface LiveStreamPushService {
    /**
     * 获取推流信息和推流质量
     *
     * @param account
     * @param startTime
     * @param endTime
     * @throws TencentCloudSDKException
     */
    void getStreamPushQualityList(VendorAccountWithDomainsDTO account, String startTime, String endTime) throws TencentCloudSDKException;
}
