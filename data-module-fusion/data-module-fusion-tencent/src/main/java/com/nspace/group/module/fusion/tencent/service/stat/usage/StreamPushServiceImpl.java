package com.nspace.group.module.fusion.tencent.service.stat.usage;

import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.framework.common.enums.RegionEnum;
import com.nspace.group.framework.common.util.date.DateUtils;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.framework.common.util.number.NetworkUtils;
import com.nspace.group.module.fusion.tencent.common.TencentClientService;
import com.nspace.group.module.infra.dal.dataobject.analysis.usage.UsagePushBandwidhTrafficDO;
import com.nspace.group.module.infra.service.analysis.usage.UsagePushService;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.redis.RedisUsageService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.live.v20180801.LiveClient;
import com.tencentcloudapi.live.v20180801.models.BillDataInfo;
import com.tencentcloudapi.live.v20180801.models.DescribePushBandwidthAndFluxListRequest;
import com.tencentcloudapi.live.v20180801.models.DescribePushBandwidthAndFluxListResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 直播推流带宽和流量数据查询
 */
@Slf4j
@Service
public class StreamPushServiceImpl implements StreamPushService {

    private static final Long GRANULARITY = 5L;

    private final TencentClientService tencentClientService;

    @Resource
    private UsagePushService usagePushService;

    @Resource
    private RedisUsageService redisUsageService;

    public StreamPushServiceImpl(TencentClientService tencentClientService) {
        this.tencentClientService = tencentClientService;
    }

    @Override
    public void getPushBandwidthAndFluxList(VendorAccountWithDomainsDTO account, String startTime, String endTime) throws TencentCloudSDKException {
        tencentClientService.validateParameters(account, startTime, endTime);
        String domain = tencentClientService.getDomainOrThrow(account);
        Long tenantId = account.getBindTenantId();
        LiveClient liveClient = tencentClientService.createLiveClient(account);

        for (RegionEnum region : RegionEnum.values()) {
            try {
                List<UsagePushBandwidhTrafficDO> trafficData = fetchAndMapTrafficData(liveClient, startTime, endTime, domain, tenantId, region.getRegion());
                if (!trafficData.isEmpty()) {
                    usagePushService.insertBatchPushStream(trafficData);
                } else {
                    log.info("计费用量-推流流量数据-empty domain: {}, tenantId: {}", domain, tenantId);
                }
            } catch (TencentCloudSDKException e) {
                log.error("计费用量-推流流量数据-error tenantId: {}, domain: {}", tenantId, domain, e);
            }
        }
    }

    private List<UsagePushBandwidhTrafficDO> fetchAndMapTrafficData(LiveClient liveClient, String startTime, String endTime, String domain, Long tenantId, String region) throws TencentCloudSDKException {
        String txRegion = RegionEnum.getTxRegionByRegion(region);
        DescribePushBandwidthAndFluxListRequest request = buildRequest(txRegion, startTime, endTime, domain);
        DescribePushBandwidthAndFluxListResponse response = liveClient.DescribePushBandwidthAndFluxList(request);
        log.info("fetchAndMapTrafficData.DescribePushBandwidthAndFluxList: {}", JsonUtils.toJsonString(response.getDataInfoList()));
        BillDataInfo[] dataInfoList = response.getDataInfoList();
        return mapDataInfoList(dataInfoList, tenantId, domain, region);
    }

    private List<UsagePushBandwidhTrafficDO> mapDataInfoList(BillDataInfo[] dataInfoList, Long tenantId, String domain, String region) {
        if (dataInfoList == null || dataInfoList.length == 0) {
            return Collections.emptyList();
        }

        return Arrays.stream(dataInfoList)
                .map(dataInfo -> mapToUsagePushBandwidthTrafficDO(dataInfo, tenantId, domain, region))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private UsagePushBandwidhTrafficDO mapToUsagePushBandwidthTrafficDO(BillDataInfo dataInfo, long tenantId, String domain, String region) {
        LocalDateTime startTime = DateUtils.stringToLocalDateTime(dataInfo.getTime());
        long bandwidth = NetworkUtils.bandwidthMbpsToBps(dataInfo.getBandwidth());
        long flux = NetworkUtils.trafficMBToB(dataInfo.getFlux());

        if (bandwidth == 0 && flux == 0) {
            log.debug("Bandwidth and flux are too small to process: bandwidth={}, flux={}", bandwidth, flux);
            return null;
        }

        Integer platform = DataPlatformEnum.PLATFORM_TENCENT.getPlatform();
        String convertToCustomFormat = DateUtils.convertToCustomFormat(dataInfo.getTime(), "yyyyMMddHHmm");
        String hashKey = redisUsageService.getPushKey(platform, domain, convertToCustomFormat, region);
        Map<String, Long> diffMap = redisUsageService.calculateDiffAndUpdateRedis(hashKey, bandwidth, flux);
        long diffBandwidth = diffMap.get("diffBandwidth");
        long diffFlux = diffMap.get("diffFlux");

        if (diffBandwidth == 0 && diffFlux == 0) {
            log.debug("No significant difference in bandwidth and flux: diffBandwidth={}, diffFlux={}", diffBandwidth, diffFlux);
            return null;
        }
        String billRegionByRegion = RegionEnum.getBillRegionByRegion(region);
        return UsagePushBandwidhTrafficDO.builder()
                .tenantId(tenantId)
                .region(region)
                .billingRegion(billRegionByRegion)
                .domain(domain)
                .dataPlatform(platform)
                .windowStart(startTime)
                .windowEnd(startTime.plusMinutes(GRANULARITY))
                .bandwidth(diffBandwidth)
                .traffic(diffFlux)
                .liveType(1)
                .curDate(startTime.toLocalDate())
                .build();
    }

    private DescribePushBandwidthAndFluxListRequest buildRequest(String region, String startTime, String endTime, String domain) {
        DescribePushBandwidthAndFluxListRequest request = new DescribePushBandwidthAndFluxListRequest();
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setPushDomains(new String[]{domain});
        request.setRegionNames(new String[]{region});
        request.setGranularity(GRANULARITY);
        return request;
    }
}