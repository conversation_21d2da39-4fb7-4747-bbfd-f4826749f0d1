package com.nspace.group.module.fusion.tencent.service.stat.usage;

import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.framework.common.enums.RegionEnum;
import com.nspace.group.framework.common.util.date.DateUtils;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.framework.common.util.number.NetworkUtils;
import com.nspace.group.module.fusion.tencent.common.TencentClientService;
import com.nspace.group.module.infra.dal.dataobject.analysis.usage.UsagePullBandwidhTrafficDO;
import com.nspace.group.module.infra.service.analysis.usage.UsagePullService;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.redis.RedisUsageService;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.live.v20180801.LiveClient;
import com.tencentcloudapi.live.v20180801.models.BillDataInfo;
import com.tencentcloudapi.live.v20180801.models.DescribeBillBandwidthAndFluxListRequest;
import com.tencentcloudapi.live.v20180801.models.DescribeBillBandwidthAndFluxListResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StreamPullServiceImpl implements StreamPullService {

    private static final Long GRANULARITY = 5L;

    private final TencentClientService tencentClientService;

    @Resource
    private RedisUsageService redisUsageService;

    @Resource
    private UsagePullService usagePullService;

    public StreamPullServiceImpl(TencentClientService tencentClientService) {
        this.tencentClientService = tencentClientService;
    }

    @Override
    public void getPullBandwidthAndFluxList(VendorAccountWithDomainsDTO account, String startTime, String endTime) throws TencentCloudSDKException {

        tencentClientService.validateParameters(account, startTime, endTime);

        LiveClient liveClient = tencentClientService.createLiveClient(account);
        String domain = tencentClientService.getDomainOrThrow(account);

        for (RegionEnum region : RegionEnum.values()) {
            DescribeBillBandwidthAndFluxListRequest request = buildRequest(region.getTxRegion(), startTime, endTime, domain);
            try {
                DescribeBillBandwidthAndFluxListResponse response = liveClient.DescribeBillBandwidthAndFluxList(request);
                List<UsagePullBandwidhTrafficDO> trafficData = mapDataInfoList(response.getDataInfoList(), account.getBindTenantId(), domain, region.getRegion());
                if (!trafficData.isEmpty()) {
                    usagePullService.insertBatchPullStream(trafficData);
                } else {
                    log.info("No data found for domain: {}, tenantId: {}", domain, account.getBindTenantId());
                }
            } catch (TencentCloudSDKException e) {
                log.error("Error fetching bandwidth and flux data for tenantId: {}, domain: {}, region: {}, error: {}",
                        account.getBindTenantId(), domain, region.getRegion(), e.getMessage(), e);
            }
        }
    }

    private List<UsagePullBandwidhTrafficDO> mapDataInfoList(BillDataInfo[] dataInfoList, Long tenantId, String domain, String region) {
        validateDataInfoList(dataInfoList, tenantId);
        return Arrays.stream(dataInfoList)
                .map(dataInfo -> mapToUsagePullBandwidthTrafficDO(dataInfo, tenantId, domain, region))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void validateDataInfoList(BillDataInfo[] dataInfoList, Long tenantId) {
        if (dataInfoList == null || dataInfoList.length == 0) {
            log.warn("Empty data returned for tenantId: {}", tenantId);
            throw new RuntimeException("No data returned for tenantId " + tenantId);
        }
    }

    private UsagePullBandwidhTrafficDO mapToUsagePullBandwidthTrafficDO(BillDataInfo dataInfo, long tenantId, String domain, String region) {
        if (dataInfo == null || domain == null || domain.isEmpty()) {
            log.warn("Invalid input: dataInfo={}, tenantId={}, domain={}", dataInfo, tenantId, domain);
            return null;
        }
        LocalDateTime startTime = DateUtils.stringToLocalDateTime(dataInfo.getTime());
        long bandwidth = NetworkUtils.bandwidthMbpsToBps(dataInfo.getBandwidth());
        long flux = NetworkUtils.trafficMBToB(dataInfo.getFlux());
        if (bandwidth == 0 && flux == 0) {
            log.debug("Bandwidth and flux are too small to process: bandwidth={}, flux={}", bandwidth, flux);
            return null;
        }
        Integer platform = DataPlatformEnum.PLATFORM_TENCENT.getPlatform();
        String convertToCustomFormat = DateUtils.convertToCustomFormat(dataInfo.getTime(), "yyyyMMddHHmm");
        String hashKey = redisUsageService.getPullKey(platform, domain, convertToCustomFormat, region);
        Map<String, Long> diffMap = redisUsageService.calculateDiffAndUpdateRedis(hashKey, bandwidth, flux);
        long diffBandwidth = diffMap.get("diffBandwidth");
        long diffFlux = diffMap.get("diffFlux");
        if (diffBandwidth == 0 && diffFlux == 0) {
            log.debug("No significant difference in bandwidth and flux: diffBandwidth={}, diffFlux={}", diffBandwidth, diffFlux);
            return null;
        }
        String billRegionByRegion = RegionEnum.getBillRegionByRegion(region);

        return UsagePullBandwidhTrafficDO.builder()
                .tenantId(tenantId)
                .region(region)
                .billingRegion(billRegionByRegion)
                .domain(domain)
                .dataPlatform(platform)
                .windowStart(startTime)
                .windowEnd(startTime.plusMinutes(GRANULARITY))
                .bandwidth(diffBandwidth)
                .traffic(diffFlux)
                .liveType(1)
                .curDate(startTime.toLocalDate())
                .build();
    }

    private DescribeBillBandwidthAndFluxListRequest buildRequest(String regionName, String startTime, String endTime, String domain) {
        DescribeBillBandwidthAndFluxListRequest request = new DescribeBillBandwidthAndFluxListRequest();
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setPlayDomains(new String[]{domain});
        request.setRegionNames(new String[]{regionName});
        request.setGranularity(GRANULARITY);
        return request;
    }
}