package com.nspace.group.module.fusion.tencent.service.stat.usage;

import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;

/**
 * 直播拉流带宽和流量数据查询
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：23:42
 */
public interface StreamPullService {
    void getPullBandwidthAndFluxList(VendorAccountWithDomainsDTO account, String startTime, String endTime) throws TencentCloudSDKException;
}
