package com.nspace.group.module.fusion.tencent.common;

import com.nspace.group.framework.common.util.date.DateUtils;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.common.profile.Language;
import com.tencentcloudapi.live.v20180801.LiveClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 腾讯云客户端
 *
 * @author：<EMAIL>
 * @date： 2024/11/7
 * @time：17:15
 */

@Slf4j
@Component
public class TencentClientService {

    public LiveClient createLiveClient(String region, String secretId, String secretKey, String endpoint) throws TencentCloudSDKException {
        Credential cred = new Credential(secretId, secretKey);

        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setReqMethod("GET");
        httpProfile.setConnTimeout(30);
        httpProfile.setReadTimeout(30);
        httpProfile.setEndpoint(endpoint);

        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setSignMethod(ClientProfile.SIGN_TC3_256);
        clientProfile.setHttpProfile(httpProfile);
        clientProfile.setDebug(true);
        clientProfile.setLanguage(Language.ZH_CN);

        return new LiveClient(cred, region, clientProfile);
    }

    public LiveClient createLiveClient(VendorAccountWithDomainsDTO account) throws TencentCloudSDKException {
        return createLiveClient("", account.getSecretId(), account.getSecretKey(), account.getEndpoint());
    }

    public void validateParameters(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
        if (account == null || startTime == null || endTime == null) {
            throw new IllegalArgumentException("Account, startTime, and endTime cannot be null.");
        }
    }

    public String getDomainOrThrow(VendorAccountWithDomainsDTO account) {
        return Optional.ofNullable(account.getDomain())
                .filter(domain -> !domain.isEmpty())
                .orElseThrow(() -> {
                    log.warn("Domain is missing for tenantId: {}", account.getBindTenantId());
                    return new RuntimeException("Domain is empty for tenantId " + account.getBindTenantId());
                });
    }

    public String incrementTimeByMinutes(String time, int minutes) {
        return DateUtils.plusMinutesToStreamTime(time, minutes);
    }
}