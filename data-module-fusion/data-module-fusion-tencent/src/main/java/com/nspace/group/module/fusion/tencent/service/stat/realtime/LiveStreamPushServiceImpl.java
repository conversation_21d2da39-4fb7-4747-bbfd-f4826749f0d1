package com.nspace.group.module.fusion.tencent.service.stat.realtime;

import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.framework.common.util.date.DateUtils;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.framework.common.util.string.StrUtils;
import com.nspace.group.module.fusion.tencent.common.TencentClientService;
import com.nspace.group.module.infra.constant.KafkaTopicConstants;
import com.nspace.group.module.infra.constant.RedisKeyConstants;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveStreamDO;
import com.nspace.group.module.infra.enums.analysis.LogPurposeEnum;
import com.nspace.group.module.infra.mq.message.OdsStreamLogMessage;
import com.nspace.group.module.infra.mq.producer.KafkaUniformProducer;
import com.nspace.group.module.infra.service.cloudvendor.VendorAccountService;
import com.nspace.group.module.infra.service.cloudvendor.dto.LiveStreamPageReqDTO;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.live.v20180801.LiveClient;
import com.tencentcloudapi.live.v20180801.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class LiveStreamPushServiceImpl implements LiveStreamPushService {

    private final TencentClientService tencentClientService;

    @Resource
    private VendorAccountService vendorAccountService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private KafkaUniformProducer kafkaUniformProducer;

    public LiveStreamPushServiceImpl(TencentClientService tencentClientService) {
        this.tencentClientService = tencentClientService;
    }

    @Override
    public void getStreamPushQualityList(VendorAccountWithDomainsDTO account, String startTime, String endTime) throws TencentCloudSDKException {
        LiveClient liveClient = tencentClientService.createLiveClient(account);

        List<OdsStreamLogMessage> pushInfoList = new ArrayList<>();

        LiveStreamPageReqDTO request = new LiveStreamPageReqDTO(
                account.getDomain(), account.getBindTenantId(),
                DateUtils.stringToLocalDateTime(startTime), DateUtils.stringToLocalDateTime(endTime)
        );
        int pageNum = 1;
        while (true) {
            request.setPageNo(pageNum++);
            PageResult<LiveStreamDO> pageResult = vendorAccountService.getVendorAccountStreamList(request);
            if (pageResult == null || pageResult.getList().isEmpty()) {
                break;
            }
            pageResult.getList().forEach(stream ->
                    processStreamData(liveClient, stream, pushInfoList, startTime, endTime)
            );
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        if (pushInfoList.isEmpty()) {
            log.error("No push data found for tenantId: {}, domain: {}", request.getTenantId(), request.getDomain());
            return;
        }

        pushInfoList.forEach(streamLog ->
                kafkaUniformProducer.send(KafkaTopicConstants.ODS_CLOUD_MERCHANT_STREAM_PUSH, streamLog.serialize())
        );
    }

    private void processStreamData(LiveClient liveClient, LiveStreamDO stream, List<OdsStreamLogMessage> pushInfoList,
                                   String startTime, String endTime) {
        try {
            DescribeStreamPushInfoListRequest describeRequest = buildDescribeStreamPushInfoListRequest(stream, startTime, endTime);
            DescribeStreamPushInfoListResponse response = liveClient.DescribeStreamPushInfoList(describeRequest);
            if (response != null) {
                Arrays.stream(response.getDataInfoList())
                        .filter(dataInfo -> dataInfo.getBandwidth() > 0)
                        .forEach(dataInfo ->
                                addPushData(stream, dataInfo, pushInfoList)
                        );
            }
        } catch (TencentCloudSDKException e) {
            log.error("Error fetching push info for stream: {}", stream.getStreamName(), e);
        }
    }

    private DescribeStreamPushInfoListRequest buildDescribeStreamPushInfoListRequest(LiveStreamDO stream, String startTime, String endTime) {
        DescribeStreamPushInfoListRequest request = new DescribeStreamPushInfoListRequest();
        request.setStreamName(stream.getStreamName());
        request.setAppName(stream.getAppName());
        request.setPushDomain(stream.getDomain());
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        return request;
    }

    private void addPushData(LiveStreamDO stream, PushQualityData dataInfo, List<OdsStreamLogMessage> pushInfoList) {
        if (!isPushInfoDataProcessed(dataInfo.getPushDomain(), dataInfo.getAppName(), stream.getStreamName(), dataInfo)) {
            pushInfoList.add(buildPushInfoData(dataInfo, stream));
        } else {
            log.info("数据已经处理过: {},", JsonUtils.toJsonString(dataInfo));
        }
    }

    /**
     * 判断当前时间的数据是否已经处理过
     */
    private boolean isPushInfoDataProcessed(String domain, String appName, String streamName, PushQualityData dataInfo) {
        String formattedTime = DateUtils.convertToCustomFormat(dataInfo.getTime(), "yyyyMMddHHmmss");
        String uniqueCode = StrUtils.hashString(String.format("%s:%s:%s:%s", domain, appName, streamName, formattedTime), 32);

        String minute = DateUtils.convertToCustomFormat(dataInfo.getTime(), "yyyyMMddHHmm");
        String redisKey = String.format(RedisKeyConstants.FUSION_REALTIME_PUSH_QUALITY_KEY, DataPlatformEnum.PLATFORM_TENCENT.getPlatform(), minute);
        return Boolean.TRUE.equals(
                stringRedisTemplate.execute((RedisCallback<Boolean>) connection -> {
                    byte[] keyBytes = redisKey.getBytes(StandardCharsets.UTF_8);
                    byte[] valueBytes = uniqueCode.getBytes(StandardCharsets.UTF_8);
                    connection.openPipeline();
                    connection.sIsMember(keyBytes, valueBytes);
                    connection.sAdd(keyBytes, valueBytes);
                    connection.expire(keyBytes, TimeUnit.MINUTES.toSeconds(3));
                    List<Object> results = connection.closePipeline();
                    return results != null && !results.isEmpty() && Boolean.TRUE.equals(results.get(0));
                })
        );
    }

    private OdsStreamLogMessage buildPushInfoData(PushQualityData dataInfo, LiveStreamDO stream) {

        String beginPushTime = dataInfo.getBeginPushTime();
        String logTime = dataInfo.getTime();
        long totalDuration = DateUtils.calculateTimeDifferenceInSeconds(beginPushTime, logTime);
        String requestId = StrUtils.hashString(String.format("%s_%s_%s", dataInfo.getPushDomain(), dataInfo.getAppName(), stream.getStreamName()), 32);
        long traffic = (long) (dataInfo.getFlux() * 1024 * 1024);
        dataInfo.setStreamParam(String.format("/%s/%s?%s", stream.getAppName(), stream.getStreamName(),dataInfo.getStreamParam()));
        return OdsStreamLogMessage.builder()
                .hostname("")
                .requestId(requestId)
                .logTime(DateUtils.convertToISO8601(logTime, "+08:00"))
                .round(0)
                .end(0)
                .category("push")
                .streamProtocol("")
                .internal(0)
                .duration(5)
                .totalDuration(totalDuration)
                .clientAddr(dataInfo.getClientIp())
                .serverAddr(dataInfo.getServerIp())
                .scheme("")
                .httpMethod("")
                .domain(dataInfo.getPushDomain())
                .requestUri(dataInfo.getStreamParam())
                .status(200)
                .bytesSent(0L)
                .intervalBytesSent(0L)
                .intervalBytesRecv(traffic)
                .bytesRecv(traffic)
                .connectTime(DateUtils.convertToISO8601(beginPushTime, "+08:00"))
                .firstByteRecvTime(0f)
                .serverProtocol("")
                .videoFps(dataInfo.getVideoFps())
                .videoBps(dataInfo.getVideoRate())
                .audioFps(dataInfo.getAudioFps())
                .audioBps(dataInfo.getAudioRate())
                .dataPlatform(DataPlatformEnum.PLATFORM_TENCENT.getPlatform())
                .logPurpose(LogPurposeEnum.MONITOR.getCode())
                .referer("")
                .firstGopSentTimeMs(0)
                .via("")
                .userAgent("")
                .avgGopSizeMs(0)
                .videoMaxGapMs(0)
                .err("")
                .upstreamNode("")
                .vCodec(dataInfo.getVCodec())
                .aCodec(dataInfo.getACodec())
                .rewriteUri("")
                .discontinuousCount(0)
                .discontinuousTime(0)
                .sourceStreamFps(0f)
                .videoDroppedRatio(0d)
                .build();
    }
}