package com.nspace.group.module.fusion.tencent.service.stat.realtime.dto;

import com.nspace.group.module.infra.dal.dataobject.analysis.realtime.MonitorRealtimePushDO;
import com.nspace.group.module.infra.dal.dataobject.analysis.realtime.MonitorRealtimePushQualityDO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * 推流质量和信息
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/7
 * @time：17:21
 */

@Data
@AllArgsConstructor
public class PushQualityAndInfoDTO {
    private List<MonitorRealtimePushQualityDO> pushQualityResp;
    private List<MonitorRealtimePushDO> pushInfoResp;
}