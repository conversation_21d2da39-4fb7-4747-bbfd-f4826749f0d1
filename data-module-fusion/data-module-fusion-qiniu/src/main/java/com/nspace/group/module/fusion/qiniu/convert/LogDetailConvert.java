package com.nspace.group.module.fusion.qiniu.convert;

import com.nspace.group.module.fusion.qiniu.client.model.LogDetail;
import com.nspace.group.module.fusion.qiniu.service.dto.LogDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version :LogDetailConvert.java, v0.1 2024年12月03日 09:01 zhangxin Exp
 */
@Mapper(uses = LogSourceInfoConvert.class)
public interface LogDetailConvert {

    LogDetailConvert INSTANCE = Mappers.getMapper(LogDetailConvert.class);

    LogDetailDTO toLogDetailDTO(LogDetail logDetail);

}
