package com.nspace.group.module.fusion.qiniu.biz;

import com.nspace.group.module.fusion.qiniu.service.dto.LogDTO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :VendorLogTransformService.java, v0.1 2024年11月18日 11:30 Exp
 */
public interface VendorLogTransformService {

    /**
     * 转换推流原始日志数据
     *
     * @param logSplitsList 原始日志数据
     * @return List<LogDTO>
     */
    List<LogDTO> transformPushLogs(List<List<String>> logSplitsList);

    /**
     * 转换拉流原始日志数据
     *
     * @param logSplitsList 原始日志数据
     * @return List<LogDTO>
     */
    List<LogDTO> transformPullLogs(List<List<String>> logSplitsList);

    /**
     * 转换混合原始日志数据
     *
     * @param logSplitsList 原始日志数据
     * @return Map<String, List <VendorLogMessage>>
     */
    Map<String, List<LogDTO>> transformMixedLogs(List<List<String>> logSplitsList);
}
