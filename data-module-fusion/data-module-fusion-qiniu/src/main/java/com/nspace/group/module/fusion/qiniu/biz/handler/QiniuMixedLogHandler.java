package com.nspace.group.module.fusion.qiniu.biz.handler;

import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :QiniuMixedLogHandler.java, v0.1 2024年11月25日 14:33 zhangxin Exp
 */
@Slf4j
public class QiniuMixedLogHandler {

    //拉流播放
    private static final String player = "player";
    //回源（没处理）
    private static final String puller = "puller";
    //推流
    private static final String publisher = "publisher";
    //转推（没处理）
    private static final String pusher = "pusher";

    //日志类型 字段位置
    private static final Integer typePosition = 10;

    /**
     * 日志处理主方法
     *
     * @param logSplitsList 原始日志数据
     * @return Map<String, List < String>>
     */
    public static Map<String, List<List<String>>> handle(List<List<String>> logSplitsList) {
        Map<Boolean, List<List<String>>> logSplitsPartitioned = logSplitsList.stream()
                .collect(Collectors.partitioningBy(
                        (List<String> logSplits) -> isPullLog(logSplits) || isPushLog(logSplits))
                );
        List<List<String>> unknownLogSplitsList = logSplitsPartitioned.get(Boolean.FALSE);
        if (!unknownLogSplitsList.isEmpty()) {
            unknownLogSplitsList.forEach(unknownLogSplits ->
                    log.warn("handle,unknown_log_line_type_found,type={},log_line={},skip",
                            unknownLogSplits.get(typePosition), unknownLogSplits)
            );
            log.warn("handle,unknown_log_lines_skipped,total_count={}", unknownLogSplitsList.size());
        }
        return logSplitsPartitioned.get(Boolean.TRUE).stream()
                .collect(
                        Collectors.groupingBy((List<String> logSplits) -> isPushLog(logSplits) ?
                                LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.getCode() : LiveDomainTypeEnum.DOMAIN_TYPE_PULL.getCode())
                );

    }

    //是否是推流日志
    private static boolean isPushLog(List<String> logSplits) {
        return publisher.equalsIgnoreCase(logSplits.get(typePosition));
    }

    //是否是拉流日志
    private static boolean isPullLog(List<String> logSplits) {
        return player.equalsIgnoreCase(logSplits.get(typePosition));
    }

}
