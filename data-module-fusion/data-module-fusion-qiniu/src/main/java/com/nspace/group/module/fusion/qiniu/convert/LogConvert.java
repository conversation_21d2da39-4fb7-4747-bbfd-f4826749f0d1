package com.nspace.group.module.fusion.qiniu.convert;

import com.nspace.group.module.fusion.qiniu.service.dto.LogDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :LogConvert.java, v0.1 2024年11月19日 10:21 zhangxin Exp
 */
@Mapper
public interface LogConvert {

    LogConvert INSTANCE = Mappers.getMapper(LogConvert.class);

    LogDTO getLogDTO(Map<String, String> logDataMap);

    List<LogDTO> getLogDTOList(List<Map<String, String>> logDataMapList);

}
