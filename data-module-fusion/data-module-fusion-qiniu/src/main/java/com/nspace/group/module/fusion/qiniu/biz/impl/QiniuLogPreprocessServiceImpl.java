package com.nspace.group.module.fusion.qiniu.biz.impl;

import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvParser;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.module.fusion.qiniu.biz.VendorLogPreprocessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.Collections;
import java.util.List;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @version :QiniuLogPreprocessServiceImpl.java, v0.1 2024年11月25日 14:14 Exp
 */
@Service
@Slf4j
public class QiniuLogPreprocessServiceImpl implements VendorLogPreprocessService {

    private static final CsvMapper csvMapper = CsvMapper.builder()
            .addModule(new JavaTimeModule())
            .enable(CsvParser.Feature.WRAP_AS_ARRAY)
            .enable(CsvParser.Feature.TRIM_SPACES)
            .enable(CsvParser.Feature.SKIP_EMPTY_LINES).build();

    @Override
    public List<List<String>> preprocess(Object rawLogData) {
        byte[] fileInBytes = (byte[]) rawLogData;
        if (fileInBytes.length == 0) {
            log.warn("preprocess,byte_array_empty");
            return Collections.emptyList();
        }
        try (InputStream inputStream = new ByteArrayInputStream(fileInBytes);
             GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream);
             InputStreamReader inputStreamReader = new InputStreamReader(gzipInputStream);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
             MappingIterator<List<String>> it = csvMapper.readerForListOf(String.class)
                     .readValues(bufferedReader)) {
            return it.readAll();
        } catch (IOException e) {
            log.error("preprocess,unknown_io_error,error_msg={}", e.getLocalizedMessage());
            throw new RuntimeException(e);
        }
    }
}
