package com.nspace.group.module.fusion.qiniu.client.model;

import java.time.OffsetDateTime;

/**
 * LogInfo
 *
 * <AUTHOR>
 */
public class LogSourceInfo {

    /**
     * 下载链接
     */
    private String url;
    /**
     * 时间
     */
    private OffsetDateTime time;
    /**
     * 日志文件名称
     */
    private String fileName;

    /**
     * 文件大小，字节
     */
    private Integer fsize;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public OffsetDateTime getTime() {
        return time;
    }

    public void setTime(OffsetDateTime time) {
        this.time = time;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getFsize() {
        return fsize;
    }

    public void setFsize(Integer fsize) {
        this.fsize = fsize;
    }

    @Override
    public String toString() {
        final StringBuffer sb = new StringBuffer("LogSourceInfo{");
        sb.append("url='").append(url).append('\'');
        sb.append(", time=").append(time);
        sb.append(", fileName='").append(fileName).append('\'');
        sb.append(", fsize=").append(fsize);
        sb.append('}');
        return sb.toString();
    }
}
