package com.nspace.group.module.fusion.qiniu.convert;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.module.fusion.qiniu.dal.dataobject.OdsOfflineLogStreamPullDO;
import com.nspace.group.module.fusion.qiniu.service.dto.LogDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <AUTHOR>
 * @version :OdsOfflineLogStreamPullConvert.java, v0.1 2024年11月19日 10:21 zhangxin Exp
 */
@Mapper
public interface OdsOfflineLogStreamPullConvert {

    DateTimeFormatter timestampFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssxxx");

    JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    OdsOfflineLogStreamPullConvert INSTANCE = Mappers.getMapper(OdsOfflineLogStreamPullConvert.class);

    @Mapping(target = "logTime", expression = "java(java.time.OffsetDateTime.parse(logDTO.getLogTime(), timestampFormatter).toLocalDateTime())")
    @Mapping(target = "logJson", expression = "java(logToString(logDTO))")
    @Mapping(target = "curTimestamp", expression = "java(java.time.LocalDateTime.now())")
    OdsOfflineLogStreamPullDO getOfflinePullLog(LogDTO logDTO);

    List<OdsOfflineLogStreamPullDO> getOfflinePullLogList(List<LogDTO> logDTOList);


    default String getOfflinePullLogListJson(List<OdsOfflineLogStreamPullDO> dos) throws JsonProcessingException {
        return jsonMapper.writeValueAsString(dos);

    }

    default String logToString(LogDTO logDTO) {
        try {
            return jsonMapper.writeValueAsString(logDTO);
        } catch (JsonProcessingException e) {
            return StringPool.EMPTY;
        }
    }

}
