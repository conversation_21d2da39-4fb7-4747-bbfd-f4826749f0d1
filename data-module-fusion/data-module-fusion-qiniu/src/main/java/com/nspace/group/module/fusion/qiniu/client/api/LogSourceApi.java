package com.nspace.group.module.fusion.qiniu.client.api;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.reflect.TypeToken;
import com.nspace.group.module.infra.client.*;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LogSourceApi {
    private ApiClient localVarApiClient;

    public LogSourceApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    /**
     * 为 getLogSource 构建call
     *
     * @param _callback upload/download 进度回调
     * @return Call
     * @throws ApiException 请求体序列化失败抛出
     */
    public okhttp3.Call getLogSourceCall(final ApiCallback _callback) throws ApiException {
        Object localVarPostBody = null;

        // 创建请求头、cookie、请求体map变量

        List<Pair> localVarQueryParams = new ArrayList<>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<>();
        Map<String, String> localVarHeaderParams = new HashMap<>();
        Map<String, String> localVarCookieParams = new HashMap<>();
        Map<String, Object> localVarFormParams = new HashMap<>();

        final String[] localVarAccepts = new String[]{"application/csv"};
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        localVarHeaderParams.put("Accept", localVarAccept);

        return localVarApiClient.buildCall(StringPool.EMPTY, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call getLogSourceValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        okhttp3.Call localVarCall = getLogSourceCall(_callback);
        return localVarCall;

    }

    /**
     * 从Url获取日志源文件
     *
     * @return byte[] 字节数组
     * @throws ApiException 文件请求失败抛出
     */
    public byte[] getLogSource() throws ApiException {
        ApiResponse<byte[]> localVarResp = getLogSourceWithHttpInfo();
        return localVarResp.getData();
    }

    public ApiResponse<byte[]> getLogSourceWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = getLogSourceValidateBeforeCall( null);
        Type localVarReturnType = new TypeToken<byte[]>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * 从Url获取日志源文件（异步）
     * @return Call
     * @throws ApiException 文件请求失败抛出
     */
    public okhttp3.Call getLogSourceAsync(final ApiCallback<byte[]> _callback) throws ApiException {

        okhttp3.Call localVarCall = getLogSourceValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<byte[]>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }

}
