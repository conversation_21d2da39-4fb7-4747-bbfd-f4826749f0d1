package com.nspace.group.module.fusion.qiniu.biz.handler;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.framework.common.enums.StreamProtocolTypeEnum;
import lombok.extern.slf4j.Slf4j;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :QiniuPushLogHandler.java, v0.1 2024年11月25日 14:33 zhangxin Exp
 */
@Slf4j
public class QiniuPushLogHandler {

    //七牛云字段和耕耘字段映射
    private static final Map<String, Integer> mappedFields;
    //默认填充字段&数据
    private static final Map<String, String> defaultFields;
    //默认为空字段
    private static final Map<String, String> emptyFields;

    //RequestId参与字段
    private static final List<Integer> requestIdFields;

    //clientAddr参与字段
    private static final List<Integer> clientAddrFields;

    //serverAddr参与字段
    private static final List<Integer> serverAddrFields;

    //Domain 字段位置
    private static final Integer domainPosition = 2;

    //RemoteIP 字段位置
    private static final Integer remoteIpPosition = 18;

    //RemotePort 字段位置
    private static final Integer remotePortPosition = 19;

    //开始时间戳（unix 毫秒） 字段位置
    private static final Integer timestampPosition = 1;
    //URL 字段位置
    private static final Integer urlPosition = 24;

    //HTTP Method 字段位置
    private static final Integer httpMethodPosition = 34;

    //duration（毫秒） 字段位置
    private static final Integer durationPosition = 39;

    //连接持续时长（毫秒） 字段位置
    private static final Integer sessionDurationPosition = 20;

    //timestamp格式
    private static final DateTimeFormatter timestampFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssxxx");

    //默认url协议 正则表达式
    private static final Pattern defaultProtocolPattern = Pattern.compile("^(\\w+)://.+$");

    static {
        //字段映射：15
        mappedFields = new HashMap<>(15);
        mappedFields.put("uniqueId", 0);
        mappedFields.put("logTime", timestampPosition);
        mappedFields.put("domain", domainPosition);
        mappedFields.put("requestUri", urlPosition);
        mappedFields.put("duration", durationPosition);
        mappedFields.put("totalDuration", sessionDurationPosition);
        mappedFields.put("status", 11);
        mappedFields.put("bytesRecv", 23);
        mappedFields.put("intervalBytesRecv", 22);
        mappedFields.put("connectTime", timestampPosition);
        mappedFields.put("scheme", 8);
        mappedFields.put("videoFps", 36);
        mappedFields.put("audioFps", 37);

        //默认填充：9
        defaultFields = new HashMap<>(9);
        defaultFields.put("hostname", StringPool.EMPTY);
        defaultFields.put("round", StringPool.ZERO);
        defaultFields.put("end", StringPool.ZERO);
        defaultFields.put("category", LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.getCode().toLowerCase());
        defaultFields.put("internal", StringPool.ZERO);
        defaultFields.put("serverProtocol", StringPool.EMPTY);
        defaultFields.put("firstByteRecvTime", "0.0");
        defaultFields.put("videoBps", StringPool.ZERO);
        defaultFields.put("audioBps", StringPool.ZERO);
        defaultFields.put("logPurpose", StringPool.ZERO);

        //默认为空字段：5
        emptyFields = new HashMap<>(5);
        emptyFields.put("intervalBytesSent", StringPool.ZERO);
        emptyFields.put("bytesSent", StringPool.ZERO);
        emptyFields.put("httpMethod", StringPool.EMPTY);
        emptyFields.put("country", StringPool.EMPTY);
        emptyFields.put("province", StringPool.EMPTY);
        emptyFields.put("isp", StringPool.EMPTY);

        clientAddrFields = new ArrayList<>(2);
        clientAddrFields.add(remoteIpPosition);
        clientAddrFields.add(remotePortPosition);

        serverAddrFields = new ArrayList<>(2);
        serverAddrFields.add(12);
        serverAddrFields.add(13);

        requestIdFields = new ArrayList<>(4);
        //RemoteIP
        requestIdFields.add(remoteIpPosition);
        //RemotePort
        requestIdFields.add(remotePortPosition);
        //Domain
        requestIdFields.add(domainPosition);
        //URL
        requestIdFields.add(urlPosition);

    }

    /**
     * 日志处理主方法
     *
     * @param platformNum   三方平台编号
     * @param logSplitsList 原始日志数据
     * @return List<Map>
     */
    public static List<Map<String, String>> handle(Integer platformNum, List<List<String>> logSplitsList) {
        Map<Boolean, List<List<String>>> validAndInvalidLogSplitsMap = logSplitsList.stream()
                .collect(Collectors.partitioningBy(logSplits -> logSplits.size() > 39));
        List<List<String>> invalidLogSplitsList = validAndInvalidLogSplitsMap.get(Boolean.FALSE);
        if (!invalidLogSplitsList.isEmpty()) {
            List<String> sampleLogLine = invalidLogSplitsList.get(0);
            log.error("handle,insufficient_log_line_size,sample_log_line={},size={},expected_size={},count={}",
                    sampleLogLine, sampleLogLine.size(), 40, invalidLogSplitsList.size());
            throw new RuntimeException("insufficient_log_line_size: " + sampleLogLine.size());
        }
        return validAndInvalidLogSplitsMap.get(Boolean.TRUE).stream()
                .map(logSplits -> QiniuPushLogHandler.generateLogDataMap(platformNum, logSplits)).collect(Collectors.toList());
    }

    private static HashMap<String, String> generateLogDataMap(Integer platformNum, List<String> logSplits) {
        HashMap<String, String> dataMap = new HashMap<>(30);
        //添加为空字段
        dataMap.putAll(emptyFields);
        //添加默认值字段
        dataMap.putAll(defaultFields);
        try {
            mappedFields.forEach((k, v) -> {
                String logValue = logSplits.get(v);
                //处理timestamp
                logValue = handleTimestamp(v, logValue);
                //处理http_method
                logValue = handleHttpMethod(v, logValue);
                //处理周期播放时长
                logValue = handleDuration(v, logValue);
                //处理总播放时长
                logValue = handleSessionDuration(v, logValue);
                dataMap.put(k, logValue);
            });
            dataMap.put("requestId", getRequestId(logSplits));
            dataMap.put("streamProtocol", getStreamProtocol(logSplits.get(urlPosition)));
            dataMap.put("clientAddr", getClientAddr(logSplits));
            dataMap.put("serverAddr", getServerAddr(logSplits));
            dataMap.put("dataPlatform", platformNum.toString());
        } catch (Exception e) {
            log.error("handle.generateLogDataMap,log_data_map_generate_error,log_data={}", logSplits);
            throw new RuntimeException(e);
        }
        return dataMap;
    }

    private static String getClientAddr(List<String> logSplits) {
        return clientAddrFields.stream().map(logSplits::get).collect(Collectors.joining(StringPool.COLON));
    }

    private static String getServerAddr(List<String> logSplits) {
        return serverAddrFields.stream().map(logSplits::get).collect(Collectors.joining(StringPool.COLON));
    }

    private static String handleTimestamp(Integer index, String logValue) {
        if (timestampPosition.equals(index)) {
            try {
                logValue = OffsetDateTime.parse(logValue, timestampFormatter).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
            } catch (DateTimeParseException e) {
                log.error("handle.handleTimestamp,timestamp_can_not_be_parsed,index={},logValue={},error_msg={}", index, logValue, e.getLocalizedMessage());
                throw new RuntimeException(e);
            }
        }
        return logValue;
    }

    private static String handleDuration(Integer index, String logValue) {
        if (durationPosition.equals(index) && NumberUtil.isInteger(logValue)) {
            logValue = String.valueOf(NumberUtil.parseInt(logValue) / 1000);
        }
        return logValue;
    }

    private static String handleSessionDuration(Integer index, String logValue) {
        if (sessionDurationPosition.equals(index) && NumberUtil.isInteger(logValue)) {
            logValue = String.valueOf(NumberUtil.parseInt(logValue) / 1000);
        }
        return logValue;
    }

    //处理http method
    private static String handleHttpMethod(Integer index, String logValue) {
        if (httpMethodPosition.equals(index) && StrUtil.isNotBlank(logValue)) {
            logValue = logValue.toUpperCase();
        }
        return logValue;
    }

    //获取stream_protocol
    private static String getStreamProtocol(String logValue) {
        String protocol = StreamProtocolTypeEnum.getProtocolType(logValue);
        if (StrUtil.isBlank(protocol)) {
            Matcher matcher = defaultProtocolPattern.matcher(logValue);
            if (matcher.matches()) {
                protocol = matcher.group(1).toLowerCase();
            }
        }
        return protocol;
    }

    //生成request_id
    private static String getRequestId(List<String> logSplits) {
        String logValStr = requestIdFields.stream().map(logSplits::get).collect(Collectors.joining());
        return DigestUtil.md5Hex(logValStr);
    }
}
