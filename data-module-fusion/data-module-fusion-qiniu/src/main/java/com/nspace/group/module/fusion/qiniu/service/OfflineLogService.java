package com.nspace.group.module.fusion.qiniu.service;


import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;

/**
 * <AUTHOR>
 * @version :OfflineLogService.java, v0.1 2024年12月02日 11:52 Exp
 */
public interface OfflineLogService {

    /**
     * 处理离线日志数据
     *
     * @param accountWithDomain 带域名的账号
     * @param interval          时间间隔
     * @param offset            开始时间偏移量
     * @param startTimestamp    开始时间戳
     */
    void processOfflineLog(VendorAccountWithDomainsDTO accountWithDomain, Integer interval, Integer offset, Long startTimestamp);

}
