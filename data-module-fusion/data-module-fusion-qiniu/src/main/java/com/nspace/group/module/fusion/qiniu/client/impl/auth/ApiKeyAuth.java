package com.nspace.group.module.fusion.qiniu.client.impl.auth;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.qiniu.util.Auth;
import lombok.Getter;

import java.nio.charset.StandardCharsets;

@Getter
public class ApiKeyAuth implements Authentication {

    // Authorization scheme
    private final String scheme;

    // ak
    private final String accessKey;

    // sk
    private final String secretKey;

    // HTTP_METHOD
    private final String httpMethod;

    // 请求路径
    private final String path;

    // 请求路径
    private String url;

    // RawQuery
    private final String rawQuery;

    // 请求域名
    private final String host;

    // 请求内容类型
    private final String contentType;

    //ContentLength
    private final String contentLength;

    // 请求Body
    private final String bodyStr;

    public ApiKeyAuth(String scheme, String accessKey, String secretKey,
                      String httpMethod, String path, String url, String rawQuery, String host,
                      String contentType, String contentLength, String bodyStr) {
        if (StrUtil.isBlank(httpMethod)) {
            throw new RuntimeException("ApiKeyAuth_constructor,httpMethod_is_blank");
        }
        if (StrUtil.isBlank(path)) {
            throw new RuntimeException("ApiKeyAuth_constructor,path_is_blank");
        }
        if (StrUtil.isBlank(host)) {
            throw new RuntimeException("ApiKeyAuth_constructor,host_is_blank");
        }
        if (StrUtil.isBlank(accessKey)) {
            throw new RuntimeException("ApiKeyAuth_constructor,accessKey_is_blank");
        }
        if (StrUtil.isBlank(secretKey)) {
            throw new RuntimeException("ApiKeyAuth_constructor,secretKey_is_blank");
        }
        this.scheme = scheme;
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.httpMethod = httpMethod;
        this.path = path;
        this.rawQuery = rawQuery;
        this.host = host;
        this.contentType = contentType;
        this.contentLength = contentLength;
        this.bodyStr = bodyStr;
        this.url = url;
    }

    public String generateToken() {
        Auth qiniuAuth = Auth.create(accessKey, secretKey);
        String encodedSign = qiniuAuth.signQiniuAuthorization(url, httpMethod,
                StrUtil.isBlank(bodyStr) ? null : bodyStr.getBytes(StandardCharsets.UTF_8), contentType);
        return scheme + StringPool.SPACE + encodedSign;
    }
}
