package com.nspace.group.module.fusion.qiniu.client.api;

import com.google.common.reflect.TypeToken;
import com.nspace.group.module.fusion.qiniu.client.impl.auth.ApiKeyAuth;
import com.nspace.group.module.fusion.qiniu.client.model.LogDetail;
import com.nspace.group.module.infra.client.*;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LogApi {
    private ApiClient localVarApiClient;

    public LogApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    /**
     * 为 getLogDetail 构建 Call
     * @param start 查询的起始时间
     * @param end 查询的结束时间
     * @param domain 需要查询的域名
     * @param _callback Callback for upload/download progress
     * @return 待执行的 Call
     * @throws ApiException 请求体序列化失败抛出
     */
    public okhttp3.Call getLogDetailCall(Long start, Long end, String domain, final ApiCallback _callback) throws ApiException {
        Object localVarPostBody = null;

        // create path and map variables
        ApiKeyAuth authentication = (ApiKeyAuth) localVarApiClient.getAuthentication();
        String localVarPath = authentication.getPath();

        List<Pair> localVarQueryParams = new ArrayList<>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<>();
        Map<String, String> localVarHeaderParams = new HashMap<>();
        Map<String, String> localVarCookieParams = new HashMap<>();
        Map<String, Object> localVarFormParams = new HashMap<>();

        if (start != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("start", start));
        }

        if (end != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("end", end));
        }

        if (domain != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("domain", domain));
        }

        String localVarAccept = localVarApiClient.selectHeaderAccept(new String[]{"application/json"});
        localVarHeaderParams.put("Accept", localVarAccept);

        localVarHeaderParams.put("Authorization", authentication.generateToken());

        return localVarApiClient.buildCall(localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call getLogDetailValidateBeforeCall(Long start, Long end, String orderBy, final ApiCallback _callback) throws ApiException {


        okhttp3.Call localVarCall = getLogDetailCall(start, end, orderBy, _callback);
        return localVarCall;

    }

    /**
     * 获取 log detail
     *
     * @param start 查询的起始时间
     * @param end 查询的结束时间
     * @param domain 需要查询的域名
     * @return DAGCollection
     * @throws ApiException API请求失败或响应体反序列化失败抛出
     */
    public LogDetail getLogDetail(Long start, Long end, String domain) throws ApiException {
        ApiResponse<LogDetail> localVarResp = getLogDetailWithHttpInfo(start, end, domain);
        return localVarResp.getData();
    }

    public ApiResponse<LogDetail> getLogDetailWithHttpInfo(Long start, Long end, String orderBy) throws ApiException {
        okhttp3.Call localVarCall = getLogDetailValidateBeforeCall(start, end, orderBy, null);
        Type localVarReturnType = new TypeToken<LogDetail>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * 获取 log detail (异步)
     *
     * @param start 查询的起始时间
     * @param end 查询的结束时间
     * @param domain 需要查询的域名
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException Api请求失败抛出，例如：请求体序列化失败
     */
    public okhttp3.Call getLogDetailAsync(Long start, Long end, String domain, final ApiCallback<LogDetail> _callback) throws ApiException {

        okhttp3.Call localVarCall = getLogDetailValidateBeforeCall(start, end, domain, _callback);
        Type localVarReturnType = new TypeToken<LogDetail>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
