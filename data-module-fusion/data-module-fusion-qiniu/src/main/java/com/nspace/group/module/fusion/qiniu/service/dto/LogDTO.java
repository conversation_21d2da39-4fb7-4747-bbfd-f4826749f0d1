package com.nspace.group.module.fusion.qiniu.service.dto;

/**
 * 第三方推拉流日志消息DTO
 *
 * <AUTHOR>
 */

public class LogDTO {

    //租户ID
    private Long tenantId;

    //唯一值
    private String uniqueId;

    /**
     * 机器名
     */
    private String hostname;
    /**
     * 请求ID，16进制字符串
     */
    private String requestId;
    /**
     * 请求时间，ISO 8601 standard format, example: 1970-09-28T12:00:00+06:00
     */
    private String logTime;
    /**
     * 第几轮打印，大于等于0
     */
    private Integer round;
    /**
     * 是否是结束的那一次，0或1
     */
    private Integer end;
    /**
     * 分类，pull:拉流，push:推流
     */
    private String category;

    /**
     * 流协议，rtmp，http_flv
     */
    private String streamProtocol;

    /**
     * 是否是内部请求，0或1
     */
    private Integer internal;

    /**
     * 当前周期时间长度, 单位s, 例如60
     */
    private Integer duration;

    /**
     * 从建联到现在经历的时间，单位s, 比如29393
     */
    private Integer totalDuration;

    /**
     * 用户地址, 例如*************:2932
     */
    private String clientAddr;

    /**
     * 服务端地址, 例如103.23.23.22:80。依赖LoadBalancer传递，目前获取不到
     */
    private String serverAddr;

    /**
     * 协议scheme, 例如http,https,rtmp,rtmps
     */
    private String scheme;

    /**
     * 请求方式 GET, POST, ...
     */
    private String httpMethod;

    /**
     * 域名，例如play.test.com
     */
    private String domain;

    /**
     * 请求uri,包含args. 例如 /app/test.flv?myt=2
     */
    private String requestUri;

    /**
     * response状态码，使用http的状态码定义
     */
    private Integer status;

    /**
     * 总发送字节数
     */
    private Long bytesSent;

    /**
     * 这个周期发送的字节数
     */
    private Long intervalBytesSent;

    /**
     * 这个周期接收的字节数
     */
    private Long intervalBytesRecv;

    /**
     * 总接收字节数
     */
    private Long bytesRecv;

    /**
     * 请求时间
     */
    private String connectTime;

    /**
     * 从收到请求，到接收到第一个byte，中间消耗的时间，单位秒，精度到毫秒. 例如: 3.234
     */
    private Float firstByteRecvTime;

    /**
     * 协议版本, 例如“HTTP/1.0”, “HTTP/1.1”, “HTTP/2.0”, or “HTTP/3.0”
     */
    private String serverProtocol;

    /**
     * 视频帧率, 这个周期的平均帧率
     */
    private Integer videoFps;

    /**
     * 视频码率, 这个周期的平均码率，单位: bit/s
     */
    private Integer videoBps;

    /**
     * 音频帧率, 这个周期的平均帧率
     */
    private Integer audioFps;
    /**
     * 音频码率, 这个周期的平均码率，单位: bit/s
     */
    private Integer audioBps;

    /**
     * 平台类型：0:NSPACE 1:腾讯云 2:中国电信 3:七牛云
     */
    private Integer dataPlatform;

    /**
     * 客户端ip对应的国家
     */
    private String country;

    /**
     * 客户端ip对应的省份
     */
    private String province;

    /**
     * 客户端ip对应的运营商名称
     */
    private String isp;

    /**
     * 日志用途  1：腾讯  0：七牛云
     */
    private Integer logPurpose;

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getLogTime() {
        return logTime;
    }

    public void setLogTime(String logTime) {
        this.logTime = logTime;
    }

    public Integer getRound() {
        return round;
    }

    public void setRound(Integer round) {
        this.round = round;
    }

    public Integer getEnd() {
        return end;
    }

    public void setEnd(Integer end) {
        this.end = end;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getStreamProtocol() {
        return streamProtocol;
    }

    public void setStreamProtocol(String streamProtocol) {
        this.streamProtocol = streamProtocol;
    }

    public Integer getInternal() {
        return internal;
    }

    public void setInternal(Integer internal) {
        this.internal = internal;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getTotalDuration() {
        return totalDuration;
    }

    public void setTotalDuration(Integer totalDuration) {
        this.totalDuration = totalDuration;
    }

    public String getClientAddr() {
        return clientAddr;
    }

    public void setClientAddr(String clientAddr) {
        this.clientAddr = clientAddr;
    }

    public String getServerAddr() {
        return serverAddr;
    }

    public void setServerAddr(String serverAddr) {
        this.serverAddr = serverAddr;
    }

    public String getScheme() {
        return scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }

    public String getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getBytesSent() {
        return bytesSent;
    }

    public void setBytesSent(Long bytesSent) {
        this.bytesSent = bytesSent;
    }

    public Long getIntervalBytesSent() {
        return intervalBytesSent;
    }

    public void setIntervalBytesSent(Long intervalBytesSent) {
        this.intervalBytesSent = intervalBytesSent;
    }

    public Long getIntervalBytesRecv() {
        return intervalBytesRecv;
    }

    public void setIntervalBytesRecv(Long intervalBytesRecv) {
        this.intervalBytesRecv = intervalBytesRecv;
    }

    public Long getBytesRecv() {
        return bytesRecv;
    }

    public void setBytesRecv(Long bytesRecv) {
        this.bytesRecv = bytesRecv;
    }

    public String getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(String connectTime) {
        this.connectTime = connectTime;
    }

    public Float getFirstByteRecvTime() {
        return firstByteRecvTime;
    }

    public void setFirstByteRecvTime(Float firstByteRecvTime) {
        this.firstByteRecvTime = firstByteRecvTime;
    }

    public String getServerProtocol() {
        return serverProtocol;
    }

    public void setServerProtocol(String serverProtocol) {
        this.serverProtocol = serverProtocol;
    }

    public Integer getVideoFps() {
        return videoFps;
    }

    public void setVideoFps(Integer videoFps) {
        this.videoFps = videoFps;
    }

    public Integer getVideoBps() {
        return videoBps;
    }

    public void setVideoBps(Integer videoBps) {
        this.videoBps = videoBps;
    }

    public Integer getAudioFps() {
        return audioFps;
    }

    public void setAudioFps(Integer audioFps) {
        this.audioFps = audioFps;
    }

    public Integer getAudioBps() {
        return audioBps;
    }

    public void setAudioBps(Integer audioBps) {
        this.audioBps = audioBps;
    }

    public Integer getDataPlatform() {
        return dataPlatform;
    }

    public void setDataPlatform(Integer dataPlatform) {
        this.dataPlatform = dataPlatform;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public Integer getLogPurpose() {
        return logPurpose;
    }

    public void setLogPurpose(Integer logPurpose) {
        this.logPurpose = logPurpose;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }
}
