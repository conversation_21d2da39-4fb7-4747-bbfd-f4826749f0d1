<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nspace.group</groupId>
        <artifactId>data-works</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>data-module-fusion</artifactId>
    <name>${project.artifactId}</name>
    <modules>
        <module>data-module-fusion-tencent</module>
        <module>data-module-fusion-qiniu</module>
        <module>data-module-fusion-ct</module>
    </modules>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <description>
       数据融合模块，主要将第三方或非标准数据，统一处理成nspace的数据格式，便于后续的计算。
    </description>
</project>
