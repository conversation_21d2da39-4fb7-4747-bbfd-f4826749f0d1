package com.nspace.group.module.fusion.ct.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FluxInfo {

    private Integer code;  // 状态码(100000表示成功)

    private String message;

    @JsonProperty("start_time")
    private Long startTime;  // 秒级UNIX时间戳

    @JsonProperty("end_time")
    private Long endTime;

    private String interval;  // 统计间隔(如5m/1h)

    @JsonProperty("req_hit_flow_rate_data_interval")
    private List<FlowIntervalData> reqHitFlowRateDataInterval;

    @Data
    public static class FlowIntervalData {
        @JsonProperty("miss_flow")
        private Long missFlow;  // 未命中流量(字节)

        @JsonProperty("time_stamp")
        private Long timeStamp;  // 数据点时间戳

        @JsonProperty("hit_flow_rate")
        private BigDecimal hitFlowRate;  // 命中率百分比

        private Long flow;  // 总流量(字节)

        @JsonProperty("hit_flow")
        private Long hitFlow;  // 命中流量(字节)

        @JsonProperty("product_type")
        private String productType;

        private String domain;

        private Integer province;

        private String isp;

        @JsonProperty("network_layer_protocol")
        private String networkLayerProtocol;

        @JsonProperty("application_layer_protocol")
        private String applicationLayerProtocol;

        private Integer abroad;
    }
}

