package com.nspace.group.module.fusion.ct.biz.api.data.strategy.composition;

import com.nspace.group.module.infra.design.chain.DataHandler;
import com.nspace.group.module.infra.design.chain.LogProcessHandler;
import com.nspace.group.module.fusion.ct.biz.api.data.strategy.offlinelog.OfflineLogProcessStrategy;
import com.nspace.group.module.infra.design.strategy.DataChainComposeStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("ctOfflineLogSync")
public class OfflineLogSync implements DataChainComposeStrategy {

    @Resource(name = "ctOfflineLogProcessStrategy")
    private OfflineLogProcessStrategy offlineLogProcessStrategy;

    @Override
    public DataHandler composeChain() {
        return new LogProcessHandler(offlineLogProcessStrategy);
    }
}
