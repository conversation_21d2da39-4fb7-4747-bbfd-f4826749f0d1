package com.nspace.group.module.fusion.ct.biz.api.data.strategy.usage;

import cn.hutool.core.lang.Assert;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.nspace.group.framework.common.util.region.RegionUtil;
import com.nspace.group.module.fusion.ct.biz.api.data.context.ApiDataContext;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.infra.design.strategy.CallResultProcessStrategy;
import com.nspace.group.module.fusion.ct.biz.handler.CtRegionHandler;
import com.nspace.group.module.fusion.ct.client.model.HttpsNumInfo;
import com.nspace.group.module.infra.convert.cdn.UsageGeneralCdnHttpsNumConvert;
import com.nspace.group.module.infra.service.cdn.UsageGeneralCdnHttpsNumService;
import com.nspace.group.module.infra.service.cdn.dto.UsageGeneralCdnHttpsNumDTO;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component("ctCdnHttpsNumDataProcessStrategy")
public class CdnHttpsNumDataProcessStrategy implements CallResultProcessStrategy {

    @Resource(name = "platformNumCaffeineCache")
    private LoadingCache<String, Integer> platformNumCaffeineCache;

    @Resource
    private UsageGeneralCdnHttpsNumService cdnHttpsNumService;

    @Override
    public boolean process(DataContext dataContext) {
        ApiDataContext apiDataContext = (ApiDataContext) dataContext;
        ZoneOffset defaultOffset = apiDataContext.getDefaultZoneOffset();
        HttpsNumInfo data = apiDataContext.getHttpsNumData();
        Map<String, Long> domainTenantIdMap = apiDataContext.getAccountDomains().stream()
                .collect(Collectors.toMap(VendorAccountWithDomainsDTO::getDomain,
                        VendorAccountWithDomainsDTO::getBindTenantId, (v1, v2) -> v1));
        List<HttpsNumInfo.RequestIntervalData> dataList = data.getReqRequestNumDataInterval();
        String gyVendor = apiDataContext.getGyVendor();
        Integer platformNum = platformNumCaffeineCache.get(gyVendor);
        Assert.notNull(platformNum, "unknown_gy_vendor,gy_vendor={}", gyVendor);

        List<UsageGeneralCdnHttpsNumDTO> httpsNumDataList = dataList
                .stream()
                .filter(dataInterval -> dataInterval.getRequestNum() != null && dataInterval.getRequestNum() > 0)
                .map(dataInterval -> {
                    Long timeStamp = dataInterval.getTimeStamp();
                    LocalDateTime windowStart = LocalDateTime.ofInstant(Instant.ofEpochSecond(timeStamp), defaultOffset);
                    String domain = dataInterval.getDomain();
                    Integer abroad = dataInterval.getAbroad();
                    String region = CtRegionHandler.abroadToRegion(abroad);
                    String billingRegion = RegionUtil.regionToBillingRegion(region);
                    Long httpsNum = dataInterval.getRequestNum();
                    LocalDateTime windowEnd = generateWindowEnd(timeStamp, defaultOffset);
                    return UsageGeneralCdnHttpsNumConvert.INSTANCE.getHttpsNumDTO(domainTenantIdMap.get(domain),
                            windowStart, domain, region, billingRegion, platformNum, httpsNum, windowEnd);
                }).collect(Collectors.toList());
        if (!httpsNumDataList.isEmpty()) {
            cdnHttpsNumService.saveHttpsNumData(httpsNumDataList);
        }
        return true;
    }

    private LocalDateTime generateWindowEnd(Long timeStamp, ZoneOffset offset) {
        Instant windowEndInstant = Instant.ofEpochSecond(timeStamp).plus(5, ChronoUnit.MINUTES);
        return LocalDateTime.ofInstant(windowEndInstant, offset);
    }
}
