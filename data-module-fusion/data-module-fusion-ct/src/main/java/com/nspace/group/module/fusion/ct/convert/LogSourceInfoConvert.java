package com.nspace.group.module.fusion.ct.convert;

import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.module.fusion.ct.client.model.LogSourceInfo;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogInfoDTO;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @version :LogDetailConvert.java, v0.1 2024年12月03日 09:01 zhangxin Exp
 */
@Mapper
public interface LogSourceInfoConvert {

    LogSourceInfoConvert INSTANCE = Mappers.getMapper(LogSourceInfoConvert.class);

    @Mapping(target = "fileTime", source = "logInfo.timepoint")
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "generateTime", ignore = true)
    @Mapping(target = "fileUrl", source = "logInfo.url")
    @Mapping(target = "fileSize", source = "logInfo.size")
    @Mapping(target = "fileName", ignore = true)
    VendorOfflineLogInfoDTO getOfflineLogInfo(Long tenantId, String domain, String platform, LogSourceInfo logInfo);


    @AfterMapping
    default void fillFileNameAndFileTime(@MappingTarget VendorOfflineLogInfoDTO vendorOfflineLogInfoDTO, String domain, LogSourceInfo logInfo) {
        String ext = ".gz";
        String normalizedUrl = URLUtil.normalize(logInfo.getUrl());
        String path = URLUtil.getPath(normalizedUrl);
        int domainLastPos = path.lastIndexOf(domain);
        int extPos = path.indexOf(ext, domainLastPos);
        String fileName = path.substring(domainLastPos, extPos + ext.length());
        int fileNameExtPos = fileName.length() - ext.length();
        String timeAndPart = fileName.substring(domain.length() + 1, fileNameExtPos);

        String timeStr = timeAndPart.substring(0, timeAndPart.lastIndexOf(StringPool.UNDERSCORE));
        LocalDateTime fileTime = LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy_MM_dd_HH_mm"));
        vendorOfflineLogInfoDTO.setFileName(fileName);
        vendorOfflineLogInfoDTO.setFileTime(fileTime);
    }
}
