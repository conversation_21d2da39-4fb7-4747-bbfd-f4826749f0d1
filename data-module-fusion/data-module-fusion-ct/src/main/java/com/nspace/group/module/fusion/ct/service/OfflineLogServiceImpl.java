package com.nspace.group.module.fusion.ct.service;

import com.nspace.group.module.infra.design.chain.DataHandler;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.fusion.ct.biz.api.data.context.OfflineLogContext;
import com.nspace.group.module.fusion.ct.biz.api.data.strategy.composition.OfflineLogSync;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.vendor.offlinelog.VendorOfflineLogProcessRecordService;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogProcessRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :OfflineLogServiceImpl.java, v0.1 2024年12月02日 11:52 Exp
 */
@Service("ctOfflineLogService")
@Slf4j
public class OfflineLogServiceImpl implements OfflineLogService {

    private static final ZoneOffset defaultOffset = ZoneOffset.ofHours(8);

    @Resource(name = "ctOfflineLogSync")
    private OfflineLogSync offlineLogSync;

    @Resource
    private VendorOfflineLogProcessRecordService processRecordService;

    @Override
    public void processOfflineLog(VendorAccountWithDomainsDTO accountWithDomain, Integer interval, Integer offset, Long startTimestamp) {

        Long tenantId = accountWithDomain.getBindTenantId();
        String domain = accountWithDomain.getDomain();
        String platform = accountWithDomain.getPlatform();

        //获取本次日志处理记录
        VendorOfflineLogProcessRecordDTO nextProcessRecord = processRecordService.getNextProcessRecord(tenantId, domain, platform, interval, offset, startTimestamp);
        //获取之前失败的日志处理记录，限定tenantId、domain、platform
        List<VendorOfflineLogProcessRecordDTO> failedProcessRecords = processRecordService.getFailedProcessRecords(tenantId, domain, platform);

        //本次&失败的日志文件处理记录集合
        List<VendorOfflineLogProcessRecordDTO> mergedProcessRecords = new ArrayList<>();

        //新旧日志文件处理记录合并，统一处理
        mergedProcessRecords.add(nextProcessRecord);
        mergedProcessRecords.addAll(failedProcessRecords);

        //开始时间结束时间都是以秒为单位
        //获取开始时间晚于当前时间的运行记录
        LocalDateTime now = LocalDateTime.now(defaultOffset).truncatedTo(ChronoUnit.SECONDS);
        Map<Boolean, List<VendorOfflineLogProcessRecordDTO>> validInvalidRecordsMap = mergedProcessRecords.stream()
                .collect(Collectors.partitioningBy(processRecord -> processRecord.getStartTime().isBefore(now)));
        validInvalidRecordsMap.get(Boolean.FALSE)
                .forEach(invalidRecord ->
                        log.warn("processOfflineLog,start_time_invalid,tenant_id={},domain={},platform={},start_time={},now_time={},skip",
                                tenantId, domain, platform, invalidRecord.getStartTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                                now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                );


        validInvalidRecordsMap.get(Boolean.TRUE).forEach(processRecord -> {
            DataContext offlineLogContext = new OfflineLogContext(accountWithDomain, processRecord, interval, defaultOffset);
            DataHandler handler = offlineLogSync.composeChain();
            handler.handle(offlineLogContext);
        });
    }
}
