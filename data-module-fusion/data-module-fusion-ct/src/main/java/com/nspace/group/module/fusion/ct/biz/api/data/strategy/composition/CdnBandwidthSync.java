package com.nspace.group.module.fusion.ct.biz.api.data.strategy.composition;

import com.nspace.group.module.infra.design.chain.DataProcessHandler;
import com.nspace.group.module.infra.design.chain.DataHandler;
import com.nspace.group.module.fusion.ct.biz.api.data.strategy.usage.CdnBandwidthProcessStrategy;
import com.nspace.group.module.infra.design.strategy.DataChainComposeStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("ctCdnBandwidthSync")
public class CdnBandwidthSync implements DataChainComposeStrategy {

    @Resource(name = "ctCdnBandwidthProcessStrategy")
    private CdnBandwidthProcessStrategy cdnBandwidthProcessStrategy;

    @Override
    public DataHandler composeChain() {
        return new DataProcessHandler(cdnBandwidthProcessStrategy);
    }
}
