package com.nspace.group.module.fusion.ct.client.model;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BandwidthInfo {

    private Integer code;  // 状态码(100000表示成功)

    private String message;

    @JsonProperty("start_time")
    private Long startTime;  // 秒级UNIX时间戳

    @JsonProperty("end_time")
    private Long endTime;

    private String interval;  // 统计间隔(如5m/1h)

    @JsonProperty("req_bandwidth_data_interval")
    private List<BandwidthData> reqBandwidthDataInterval;

    @Data
    public static class BandwidthData {

        @JsonProperty("time_stamp")
        private Long timeStamp;  // 数据点时间戳

        private BigDecimal bandwidth;  // 带宽值(单位bps)

        @JsonProperty("product_type")
        private String productType;

        private String domain;

        private Integer province;

        private String isp;

        @JsonProperty("network_layer_protocol")
        private String networkLayerProtocol;

        @JsonProperty("application_layer_protocol")
        private String applicationLayerProtocol;

        private Integer abroad;
    }

}
