package com.nspace.group.module.fusion.ct.biz.api.data.strategy.usage;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.util.string.StrUtils;
import com.nspace.group.module.fusion.ct.biz.api.data.context.ApiDataContext;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.infra.design.strategy.DataProcessStrategy;
import com.nspace.group.module.fusion.ct.client.model.HttpsNumInfo;
import com.nspace.group.module.infra.constant.RedisKeyConstants;
import com.nspace.group.module.infra.enums.vendor.BillingDataMetricEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component("ctCdnHttpsNumProcessStrategy")
public class CdnHttpsNumProcessStrategy implements DataProcessStrategy {

    @Resource(name = "msetnxExpireScript")
    private RedisScript<Boolean> msetnxExpireScript;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource(name = "ctCdnHttpsNumCallStrategy")
    private CdnHttpsNumCallStrategy cdnHttpsNumCallStrategy;

    @Resource(name = "ctCdnHttpsNumDataProcessStrategy")
    private CdnHttpsNumDataProcessStrategy cdnHttpsNumDataProcessStrategy;

    @Override
    public void process(DataContext dataContext) {

        ApiDataContext apiDataContext = (ApiDataContext) dataContext;

        int retryCount = 5;
        int curRetryCount = 0;
        List<String> taskKeys = apiDataContext.getAccountDomains().stream()
                .map(accountDomain -> getApiDataProcessTaskKey(apiDataContext.getGyVendor(), accountDomain.getBindTenantId(), accountDomain.getDomain(), BillingDataMetricEnum.HTTPS_NUM.name()))
                .collect(Collectors.toList());

        //重试retryCount-1次，每次间隔2、4、8、16秒
        while (curRetryCount < retryCount) {
            int waitTimeMillis = (2 << curRetryCount) * 1000;
            //计算Redis key过期时间（秒）
            long taskKeyExpiration = (waitTimeMillis / 1000 + 10) * 3;
            List<String> taskKeyValuePairs = taskKeys.stream().flatMap(taskKey -> Stream.of(taskKey, StringPool.ONE)).collect(Collectors.toList());
            Boolean success = stringRedisTemplate.execute(msetnxExpireScript, taskKeyValuePairs, Long.toString(taskKeyExpiration));
            //获取锁失败直接返回
            if (success == null || !success) {
                log.warn("fail_to_acquire_lock,task_keys={},no_op", taskKeys);
                break;
            }
            try {
                cdnHttpsNumCallStrategy.call(dataContext);
                HttpsNumInfo data = ((ApiDataContext) dataContext).getHttpsNumData();
                if (data == null || CollectionUtil.isEmpty(data.getReqRequestNumDataInterval())) {
                    log.info("try[{}],no_data_fetched", curRetryCount);
                    break;
                }
                boolean result = cdnHttpsNumDataProcessStrategy.process(dataContext);
                if (result) {
                    log.info("try[{}],data_process_success", curRetryCount);
                    break;
                }

            } catch (Exception e) {
                log.error("try[{}],unknown_exception,error={}", curRetryCount, ExceptionUtil.getRootCauseMessage(e));
            } finally {
                //任务执行结束，删除redis中的key
                log.info("try[{}],remove_task_key_from_registry,task_keys={}", curRetryCount, taskKeys);
                taskKeys.forEach(taskKey -> stringRedisTemplate.delete(taskKey));
            }
            if (curRetryCount != retryCount - 1) {
                ThreadUtil.sleep(waitTimeMillis);
            } else {
                log.error("try[{}],reached_retry_limit[{}],data_process_failed", curRetryCount, retryCount - 1);
            }
            curRetryCount++;
        }
    }

    private String getApiDataProcessTaskKey(String gyVendor, Long tenantId, String domain, String metricType) {
        String hashedDomain = StrUtils.hashString(tenantId + domain, 32);
        return String.format(RedisKeyConstants.FUSION_USAGE_DATA_KEY, gyVendor, metricType, hashedDomain);
    }

}
