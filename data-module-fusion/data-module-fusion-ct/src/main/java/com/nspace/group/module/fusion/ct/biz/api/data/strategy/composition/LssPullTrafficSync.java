package com.nspace.group.module.fusion.ct.biz.api.data.strategy.composition;

import com.nspace.group.module.infra.design.chain.DataHandler;
import com.nspace.group.module.infra.design.strategy.DataChainComposeStrategy;
import org.springframework.stereotype.Component;

@Component("ctLssPullTrafficSync")
public class LssPullTrafficSync implements DataChainComposeStrategy {

    @Override
    public DataHandler composeChain() {
        throw new UnsupportedOperationException();
    }
}
