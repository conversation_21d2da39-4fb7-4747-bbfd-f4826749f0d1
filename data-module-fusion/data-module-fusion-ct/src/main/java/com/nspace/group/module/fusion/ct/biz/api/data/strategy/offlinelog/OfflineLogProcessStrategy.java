package com.nspace.group.module.fusion.ct.biz.api.data.strategy.offlinelog;

import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.fusion.ct.biz.api.data.context.OfflineLogContext;
import com.nspace.group.module.infra.design.strategy.LogDataProcessStrategy;
import com.nspace.group.module.infra.enums.offlinelog.OfflineLogStatusEnum;
import com.nspace.group.module.infra.service.vendor.offlinelog.VendorOfflineLogProcessRecordService;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogProcessRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
@Component("ctOfflineLogProcessStrategy")
public class OfflineLogProcessStrategy implements LogDataProcessStrategy {

    @Resource(name = "ctOfflineLogDetailCallStrategy")
    private OfflineLogDetailCallStrategy offlineLogDetailCallStrategy;

    @Resource(name = "ctOfflineLogUrlProcessStrategy")
    private OfflineLogUrlProcessStrategy offlineLogUrlProcessStrategy;

    @Resource(name = "ctOfflineLogPersistenceStrategy")
    private OfflineLogPersistenceStrategy offlineLogPersistenceStrategy;

    @Resource
    private VendorOfflineLogProcessRecordService processRecordService;

    @Override
    public void process(DataContext dataContext) {
        OfflineLogContext offlineLogContext = (OfflineLogContext) dataContext;
        VendorOfflineLogProcessRecordDTO processRecord = offlineLogContext.getProcessRecord();
        Long tenantId = processRecord.getTenantId();
        String domain = processRecord.getDomain();
        String platform = processRecord.getPlatform();

        LocalDateTime startTime = processRecord.getStartTime();

        try {
            offlineLogDetailCallStrategy.call(dataContext);
            offlineLogUrlProcessStrategy.process(dataContext);
            offlineLogPersistenceStrategy.persist(dataContext);
            //处理成功
            processRecord.setStatus(OfflineLogStatusEnum.SUCCESS.getStatus());
        } catch (Exception e) {
            //处理失败，保存失败记录
            log.error("log_process_error,start_time={},error_msg={},save_record_and_continue", startTime, e.getLocalizedMessage());
            processRecord.setStatus(OfflineLogStatusEnum.FAILED.getStatus());
        }
        //新增或修改处理记录
        log.info("VendorOfflineLogProcessRecordService.saveProcessRecord,tenant_id={},domain={},platform={},start_time={},status={}",
                tenantId, domain, platform, startTime, processRecord.getStatus());
        processRecordService.saveProcessRecord(processRecord);
    }
}
