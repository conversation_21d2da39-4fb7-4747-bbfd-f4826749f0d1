package com.nspace.group.module.fusion.ct.client.impl.factory;

import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.infra.client.factory.ApiClientFactory;

public class CtApiClientFactory implements ApiClientFactory {

    public static final CtApiClientFactory INSTANCE = new CtApiClientFactory();

    public ApiClient createApiClient(String apiBasePath, Authentication authentication) {
        return new ApiClient(apiBasePath, authentication);
    }

    public ApiClient createApiClient(String apiBasePath) {
        return new ApiClient(apiBasePath);
    }

}
