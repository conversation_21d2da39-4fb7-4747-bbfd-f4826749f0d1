package com.nspace.group.module.fusion.ct.client.impl.auth;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.HmacAlgorithm;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.module.infra.client.auth.Authentication;
import lombok.Getter;

@Getter
public class CtApiAuth implements Authentication {

    // ak
    private final String accessKey;

    // sk
    private final String secretKey;

    // 请求路径
    private final String path;

    // Query
    private final String rawQuery;

    // 请求域名
    private final String host;

    // 请求Body
    private final String bodyStr;

    public CtApiAuth(String accessKey, String secretKey, String path, String rawQuery,
                     String host, String bodyStr) {
        if (StrUtil.isBlank(path)) {
            throw new RuntimeException("ApiKeyAuth_constructor,path_is_blank");
        }
        if (StrUtil.isBlank(host)) {
            throw new RuntimeException("ApiKeyAuth_constructor,host_is_blank");
        }
        if (StrUtil.isBlank(accessKey)) {
            throw new RuntimeException("ApiKeyAuth_constructor,accessKey_is_blank");
        }
        if (StrUtil.isBlank(secretKey)) {
            throw new RuntimeException("ApiKeyAuth_constructor,secretKey_is_blank");
        }
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.path = path;
        this.host = host;
        this.bodyStr = bodyStr;
        this.rawQuery = rawQuery;
    }

    public String generateSignature(long timestamp) {
        long bucket = timestamp / 86400000;
        // 中间层加密数据
        String signedStr = accessKey + ":" + bucket;
        String tempSignature = getSignature(secretKey, signedStr);

        String signedData = String.join(StringPool.NEWLINE, accessKey, String.valueOf(timestamp), path + rawQuery);
        return getSignature(tempSignature, signedData);
    }

    /**
     * 获取中间签名
     *
     * @param key  中间层密钥
     * @param data 要加密的数据
     * @return signature
     */
    private String getSignature(String key, String data) {
        byte[] bytes = DigestUtil.hmac(HmacAlgorithm.HmacSHA256, Base64.decode(key.getBytes())).digest(data);
        String signature = Base64.encodeUrlSafe(bytes);
        return signature.replace(StringPool.EQUALS, StringPool.EMPTY);
    }
}
