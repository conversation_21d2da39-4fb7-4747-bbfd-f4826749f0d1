package com.nspace.group.module.fusion.ct.biz.api.data.strategy.composition;

import com.nspace.group.module.infra.design.chain.DataHandler;
import com.nspace.group.module.infra.design.strategy.DataChainComposeStrategy;
import org.springframework.stereotype.Component;

@Component("ctLssPushTrafficSync")
public class LssPushTrafficSync implements DataChainComposeStrategy {

    @Override
    public DataHandler composeChain() {
        throw new UnsupportedOperationException();
    }
}
