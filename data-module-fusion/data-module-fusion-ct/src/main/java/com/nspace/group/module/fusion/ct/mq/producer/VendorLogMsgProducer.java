package com.nspace.group.module.fusion.ct.mq.producer;

import com.nspace.group.module.fusion.ct.mq.message.VendorLogMessage;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version :VendorLogMsgProducer.java, v0.1 2025年03月13日 09:58 Exp
 */
@Component(value = "vendorCdnLogMsgProducer")
public class VendorLogMsgProducer {

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    public void send(String topic, @NotNull VendorLogMessage message) {
        String data = message.serialize();
        // 异步发送消息
        kafkaTemplate.send(topic, data);
    }
}
