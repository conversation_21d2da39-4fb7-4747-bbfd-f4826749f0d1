package com.nspace.group.module.fusion.ct.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.scripting.support.ResourceScriptSource;

/**
 * Redis lua脚本配置
 */
@Configuration
public class RedisScriptConfig {


    /**
     * 调用MSETNX命令同时设置超时时间
     *
     * @return RedisScript
     */
    @Bean(name = "msetnxExpireScript")
    public RedisScript<Boolean> msetnxExpireScript() {
        ResourceScriptSource resourceScriptSource = new ResourceScriptSource(new ClassPathResource("scripts/msetnx_expire.lua"));
        return RedisScript.of(resourceScriptSource.getResource(), Boolean.class);
    }
}

