package com.nspace.group.module.fusion.ct.biz.api.data.strategy.usage;

import cn.hutool.core.lang.Assert;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.nspace.group.framework.common.util.region.RegionUtil;
import com.nspace.group.module.fusion.ct.biz.api.data.context.ApiDataContext;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.infra.design.strategy.CallResultProcessStrategy;
import com.nspace.group.module.fusion.ct.biz.handler.CtRegionHandler;
import com.nspace.group.module.fusion.ct.client.model.FluxInfo;
import com.nspace.group.module.infra.convert.cdn.UsageGeneralCdnBandwidthTrafficConvert;
import com.nspace.group.module.infra.service.cdn.UsageGeneralCdnBandwidthTrafficService;
import com.nspace.group.module.infra.service.cdn.dto.UsageGeneralCdnBandwidthTrafficDTO;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component("ctCdnTrafficDataProcessStrategy")
public class CdnTrafficDataProcessStrategy implements CallResultProcessStrategy {

    @Resource(name = "platformNumCaffeineCache")
    private LoadingCache<String, Integer> platformNumCaffeineCache;

    @Resource
    private UsageGeneralCdnBandwidthTrafficService cdnBandwidthTrafficService;

    @Override
    public boolean process(DataContext dataContext) {
        ApiDataContext apiDataContext = (ApiDataContext) dataContext;
        ZoneOffset defaultOffset = apiDataContext.getDefaultZoneOffset();
        FluxInfo data = apiDataContext.getTrafficData();
        Map<String, Long> domainTenantIdMap = apiDataContext.getAccountDomains().stream()
                .collect(Collectors.toMap(VendorAccountWithDomainsDTO::getDomain,
                        VendorAccountWithDomainsDTO::getBindTenantId, (v1, v2) -> v1));
        List<FluxInfo.FlowIntervalData> dataList = data.getReqHitFlowRateDataInterval();
        String gyVendor = apiDataContext.getGyVendor();
        Integer platformNum = platformNumCaffeineCache.get(gyVendor);
        Assert.notNull(platformNum, "unknown_gy_vendor,gy_vendor={}", gyVendor);

        List<UsageGeneralCdnBandwidthTrafficDTO> trafficDataList = dataList
                .stream()
                .filter(dataInterval -> dataInterval.getFlow() != null && dataInterval.getFlow() > 0)
                .map(dataInterval -> {
                    Long timeStamp = dataInterval.getTimeStamp();
                    LocalDateTime windowStart = LocalDateTime.ofInstant(Instant.ofEpochSecond(timeStamp), defaultOffset);
                    String domain = dataInterval.getDomain();
                    Integer abroad = dataInterval.getAbroad();
                    String region = CtRegionHandler.abroadToRegion(abroad);
                    String billingRegion = RegionUtil.regionToBillingRegion(region);
                    Long flow = dataInterval.getFlow();
                    LocalDateTime windowEnd = generateWindowEnd(timeStamp, defaultOffset);
                    return UsageGeneralCdnBandwidthTrafficConvert.INSTANCE.getTrafficDTO(domainTenantIdMap.get(domain),
                            windowStart, domain, region, billingRegion, platformNum, flow, windowEnd);
                }).collect(Collectors.toList());
        if (!trafficDataList.isEmpty()) {
            cdnBandwidthTrafficService.saveTrafficData(trafficDataList);
        }
        return true;
    }

    private LocalDateTime generateWindowEnd(Long timeStamp, ZoneOffset offset) {
        Instant windowEndInstant = Instant.ofEpochSecond(timeStamp).plus(5, ChronoUnit.MINUTES);
        return LocalDateTime.ofInstant(windowEndInstant, offset);
    }
}
