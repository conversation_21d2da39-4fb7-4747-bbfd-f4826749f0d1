package com.nspace.group.module.fusion.ct.biz.api.data.strategy.offlinelog;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.exception.ServiceException;
import com.nspace.group.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.fusion.ct.biz.api.data.context.OfflineLogContext;
import com.nspace.group.module.infra.design.strategy.CallStrategy;
import com.nspace.group.module.fusion.ct.client.api.LogApi;
import com.nspace.group.module.fusion.ct.client.impl.auth.CtApiAuth;
import com.nspace.group.module.fusion.ct.client.impl.factory.CtApiClientFactory;
import com.nspace.group.module.fusion.ct.client.model.LogDetail;
import com.nspace.group.module.fusion.ct.client.model.LogDetailResult;
import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogProcessRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component("ctOfflineLogDetailCallStrategy")
public class OfflineLogDetailCallStrategy implements CallStrategy {
    @Override
    public void call(DataContext dataContext) {
        OfflineLogContext offlineLogContext = (OfflineLogContext) dataContext;
        String apiBaseUrl = offlineLogContext.getApiBaseUrl();
        String host = URLUtil.toUrlForHttp(URLUtil.normalize(apiBaseUrl)).getHost();
        Integer interval = offlineLogContext.getInterval();
        String apiPath = offlineLogContext.getApiPath();
        VendorOfflineLogProcessRecordDTO processRecord = offlineLogContext.getProcessRecord();

        VendorAccountWithDomainsDTO accountDomain = offlineLogContext.getAccountDomain();
        Long tenantId = accountDomain.getBindTenantId();
        String domain = accountDomain.getDomain();
        String accessKey = accountDomain.getSecretId();
        String secretKey = accountDomain.getSecretKey();

        LocalDateTime startTime = processRecord.getStartTime();
        Long[] startAndEnd = generateStartEnd(startTime, interval, offlineLogContext.getDefaultZoneOffset());
        long start = startAndEnd[0];
        long end = startAndEnd[1];
        //拼接鉴权对象
        Authentication auth = new CtApiAuth(accessKey, secretKey, apiPath, getRawQueryString(start, end, domain), host, StringPool.EMPTY);

        //调用天翼API获取日志文件信息
        log.info("begin_api_call,tenant_id={},start={},end={},domain={}", tenantId, start, end, domain);
        //获取ApiClient
        ApiClient apiClient = CtApiClientFactory.INSTANCE.createApiClient(apiBaseUrl, auth);
        //创建LogApi对象
        LogApi logApi = new LogApi(apiClient);
        try {
            LogDetail logDetail = logApi.getLogDetail(start, end, domain);
            log.info("after_api_call,tenant_id={},start={},end={},domain={},code={}",
                    tenantId, start, end, domain, logDetail.getCode());
            if ("100000".equals(logDetail.getCode())) {
                log.info("api_call_success,tenant_id={},start={},end={},domain={},code={},result_list={}",
                        tenantId, start, end, domain, logDetail.getCode(), logDetail.getResult());
                List<LogDetailResult> results = CollectionUtil.defaultIfEmpty(logDetail.getResult(), Collections.emptyList());
                offlineLogContext.setLogDetailResults(results);
                return;
            }
            throw new ServiceException(GlobalErrorCodeConstants.UNKNOWN.getCode(), logDetail.getMessage());
        } catch (ApiException e) {
            log.error("LogApi.getLogDetail,unknown_api_exception,start={},end={},domain={}", start, end, domain);
            throw new RuntimeException(e);
        }
    }

    private String getRawQueryString(Long start, Long end, String domain) {
        return StringPool.QUESTION_MARK +
                "start_time" + StringPool.EQUALS + start.toString() +
                StringPool.AMPERSAND +
                "end_time" + StringPool.EQUALS + end.toString() +
                StringPool.AMPERSAND +
                "domain" + StringPool.EQUALS + domain;

    }

    private Long[] generateStartEnd(LocalDateTime startTime, Integer interval, ZoneOffset defaultZoneOffset) {
        long start = startTime.toEpochSecond(defaultZoneOffset);
        long end = startTime.plusHours(interval).toEpochSecond(defaultZoneOffset);
        return new Long[]{start, end};
    }
}
