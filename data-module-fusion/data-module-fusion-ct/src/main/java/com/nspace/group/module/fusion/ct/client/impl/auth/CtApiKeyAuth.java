package com.nspace.group.module.fusion.ct.client.impl.auth;

import com.nspace.group.module.infra.client.auth.Authentication;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CtApiKeyAuth implements Authentication {

    // ak
    private String accessKey;

    // sk
    private String secretKey;

    //请求路径
    private String path;

    public static String getHmacSHA256_Base64(String context,String sk)throws Exception{
        Mac hmacSHA256 = Mac.getInstance("HmacSHA256");
        SecretKeySpec signingKey = new SecretKeySpec(sk.getBytes(), "HmacSHA256");
        hmacSHA256.init(signingKey);
        byte[] bytes = hmacSHA256.doFinal(context.getBytes("utf-8"));
        byte[] encode = java.util.Base64.getEncoder().encode(bytes);
        return new String(encode,"utf-8");
    }

    public static String formatdate(){
        DateFormat dateFormat = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss", Locale.ENGLISH);
        return dateFormat.format(new Date())+" GMT";
    }

}
