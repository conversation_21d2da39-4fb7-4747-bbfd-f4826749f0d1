package com.nspace.group.module.fusion.ct.biz.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nspace.group.framework.common.exception.util.ServiceExceptionUtil;
import com.nspace.group.module.fusion.ct.convert.VendorLogMessageConvert;
import com.nspace.group.module.fusion.ct.mq.message.VendorLogMessage;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @version :CtCdnOfflineLogHandler.java, v0.1 2025年03月11日 14:33 zhangxin Exp
 */
@Slf4j
public class CtCdnOfflineLogHandler {

    private static final int MAX_FIELD_INDEX = 31;

    //七牛云字段和耕耘字段映射
    private static final Map<String, String> mappedFields;
    //默认填充字段&数据
    private static final Map<String, String> defaultFields;

    private static final Map<String, String> numberFieldsDefault;

    private static final JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    static {
        //字段映射：29
        mappedFields = new HashMap<>(32);
        mappedFields.put("station", "station");
        mappedFields.put("unixtime", "unixtime");
        mappedFields.put("method", "method");
        mappedFields.put("scheme", "scheme");
        mappedFields.put("domain", "domain");
        mappedFields.put("uri", "uri");
        mappedFields.put("uriParam", "uri_param");
        mappedFields.put("returnCode", "return_code");
        mappedFields.put("requestTime", "request_time");
        mappedFields.put("requestSize", "request_size");
        mappedFields.put("responseSize", "response_size");
        mappedFields.put("hitInfo", "hit_info");
        mappedFields.put("connectIp", "connect_ip");
        mappedFields.put("serverAddr", "server_addr");
        mappedFields.put("serverPort", "server_port");
        mappedFields.put("referer", "referer");
        mappedFields.put("contentType", "content_type");
        mappedFields.put("userAgent", "user_agent");
        mappedFields.put("remotePort", "remote_port");
        mappedFields.put("lastModified", "last_modified");
        mappedFields.put("uuid", "uuid");
        mappedFields.put("viaInfo", "via_info");
        mappedFields.put("responseFbtTime", "response_fbt_time");
        mappedFields.put("bodyBytesSent", "body_bytes_sent");
        mappedFields.put("sentHttpContentLength", "sent_http_content_length");
        mappedFields.put("httpRange", "http_range");
        mappedFields.put("sentHttpContentRange", "sent_http_content_range");
        mappedFields.put("dyUserInfo", "dy_user_info");
        mappedFields.put("httpXForwardedFor", "http_x_forwarded_for");
        mappedFields.put("country", "country");
        mappedFields.put("httpVersion", "http_version");
        mappedFields.put("quic", "quic");

        //默认填充：1
        defaultFields = new HashMap<>(2);
        defaultFields.put("internal", StringPool.ZERO);

        numberFieldsDefault = new HashMap<>();
        numberFieldsDefault.put("unixtime", StringPool.ZERO);
        numberFieldsDefault.put("requestTime", StringPool.ZERO);
        numberFieldsDefault.put("requestSize", StringPool.ZERO);
        numberFieldsDefault.put("responseSize", StringPool.ZERO);
        numberFieldsDefault.put("returnCode", "200");
        numberFieldsDefault.put("remotePort", StringPool.ZERO);
        numberFieldsDefault.put("serverPort", StringPool.ZERO);
        numberFieldsDefault.put("bodyBytesSent", StringPool.ZERO);
        numberFieldsDefault.put("sentHttpContentLength", StringPool.ZERO);
        numberFieldsDefault.put("responseFbtTime", StringPool.ZERO);
    }

    /**
     * 日志处理主方法
     *
     * @param platformNum 三方平台编码
     * @param logMapList  原始日志数据
     * @return List<Map>
     */
    private static List<Map<String, String>> handle(Integer platformNum, List<Map<String, String>> logMapList) {
        Map<Boolean, List<Map<String, String>>> validAndInvalidLogSplitsMap = logMapList.stream()
                .collect(Collectors.partitioningBy(logSplits -> logSplits.size() > MAX_FIELD_INDEX));
        List<Map<String, String>> invalidLogSplitsList = validAndInvalidLogSplitsMap.get(Boolean.FALSE);
        if (!invalidLogSplitsList.isEmpty()) {
            throw ServiceExceptionUtil.invalidParamException("handle,insufficient_log_line_size,expected_size={}", MAX_FIELD_INDEX + 1);
        }
        try {
            return validAndInvalidLogSplitsMap.get(Boolean.TRUE).stream()
                    .map(logMap -> CtCdnOfflineLogHandler.generateLogDataMap(platformNum, logMap)).collect(Collectors.toList());
        } catch (Exception e) {
            throw ServiceExceptionUtil.exception0(GlobalErrorCodeConstants.UNKNOWN.getCode(),
                    "handle.generateLogDataMap,log_data_map_generate_error,err_msg={}", e.getLocalizedMessage());
        }
    }

    public static List<Map<String, String>> preprocess(Object rawLogData) {
        byte[] fileInBytes = (byte[]) rawLogData;
        if (fileInBytes.length == 0) {
            log.warn("preprocess,byte_array_empty");
            return Collections.emptyList();
        }
        try (InputStream inputStream = new ByteArrayInputStream(fileInBytes);
             GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream);
             InputStreamReader inputStreamReader = new InputStreamReader(gzipInputStream);
             BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
             MappingIterator<JsonNode> it = jsonMapper.readerFor(JsonNode.class)
                     .readValues(bufferedReader)) {
            List<Map<String, String>> logMapList = new ArrayList<>();
            while (it.hasNextValue()) {
                JsonNode node = it.nextValue();
                JsonNode mContents = node.path("mContents");
                if (mContents.isArray()) {
                    Map<String, String> kvMap = new HashMap<>();
                    for (JsonNode content : mContents) {
                        kvMap.put(content.get("mKey").asText(), content.get("mValue").asText());
                    }
                    logMapList.add(kvMap);
                }
            }
            return logMapList;
        } catch (IOException e) {
            log.error("preprocess,unknown_io_error,error_msg={}", e.getLocalizedMessage());
            throw new RuntimeException(e);
        }
    }

    public static List<VendorLogMessage> transformLogs(Integer platformNum, List<Map<String, String>> logSplitsList) {
        log.info("transformLogs,log_line_sample_before_transform,log_line={}",
                !CollectionUtil.isEmpty(logSplitsList) ? logSplitsList.get(0) : logSplitsList);
        List<Map<String, String>> logDataMapList = handle(platformNum, logSplitsList);
        return VendorLogMessageConvert.INSTANCE.toMessages(logDataMapList);
    }

    private static HashMap<String, String> generateLogDataMap(Integer platformNum, Map<String, String> logMap) {
        HashMap<String, String> dataMap = new HashMap<>(30);
        //添加默认值字段
        dataMap.putAll(defaultFields);
        mappedFields.forEach((k, v) -> {
            String logValue = logMap.get(v);
            if (numberFieldsDefault.containsKey(k)
                    && (StrUtil.isBlank(logValue) || StringPool.DASH.equals(logValue))) {
                logValue = numberFieldsDefault.get(k);
            }
            dataMap.put(k, logValue);
        });
        dataMap.put("dataPlatform", platformNum.toString());
        return dataMap;
    }

}
