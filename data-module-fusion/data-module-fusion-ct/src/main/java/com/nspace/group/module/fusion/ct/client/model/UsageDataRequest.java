package com.nspace.group.module.fusion.ct.client.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UsageDataRequest {
    //  开始时间
    @NotNull(message = "开始时间不能为空")
    Long start;
    //  结束时间
    @NotNull(message = "结束时间不能为空")
    Long end;
    //  域名
    String domain;
    //  分组字段
    String groupBy;
    //  区域
    Integer abroad;
}
