package com.nspace.group.module.fusion.ct.client.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * LogInfo
 *
 * <AUTHOR>
 */
public class LogSourceInfo {

    /**
     * 下载链接
     */
    private String url;
    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime timepoint;

    private String md5;

    /**
     * 文件大小，字节
     */
    private Integer size;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public LocalDateTime getTimepoint() {
        return timepoint;
    }

    public void setTimepoint(LocalDateTime timepoint) {
        this.timepoint = timepoint;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("LogSourceInfo{");
        sb.append("url='").append(url).append('\'');
        sb.append(", time=").append(timepoint);
        sb.append(", fsize=").append(size);
        sb.append('}');
        return sb.toString();
    }
}
