package com.nspace.group.module.fusion.ct.biz.api.data.context;

import cn.hutool.core.lang.Assert;
import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.module.fusion.ct.client.model.LogDetailResult;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogInfoDTO;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogProcessRecordDTO;

import java.time.ZoneOffset;
import java.util.List;

public class OfflineLogContext extends DataContext {

    private final String gyVendor = DataPlatformEnum.PLATFORM_CTVIP.getCode().toLowerCase();

    private final String apiBaseUrl = "https://open.ctcdn.cn";

    private final String apiPath = "/api/v1/log_bsstime_files";

    private final Integer interval;

    private final VendorAccountWithDomainsDTO accountDomain;

    private final VendorOfflineLogProcessRecordDTO processRecord;

    private List<LogDetailResult> logDetailResults;

    private List<VendorOfflineLogInfoDTO> offlineLogInfos;

    public OfflineLogContext(VendorAccountWithDomainsDTO accountDomain,
                             VendorOfflineLogProcessRecordDTO processRecord,
                             Integer interval, ZoneOffset defaultZoneOffset) {
        super(defaultZoneOffset);
        this.interval = interval;
        this.processRecord = processRecord;
        Assert.notNull(accountDomain);
        this.accountDomain = accountDomain;
    }

    public VendorAccountWithDomainsDTO getAccountDomain() {
        return accountDomain;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public String getApiPath() {
        return apiPath;
    }

    public VendorOfflineLogProcessRecordDTO getProcessRecord() {
        return processRecord;
    }

    public List<LogDetailResult> getLogDetailResults() {
        return logDetailResults;
    }

    public void setLogDetailResults(List<LogDetailResult> logDetailResults) {
        this.logDetailResults = logDetailResults;
    }

    public List<VendorOfflineLogInfoDTO> getOfflineLogInfos() {
        return offlineLogInfos;
    }

    public void setOfflineLogInfos(List<VendorOfflineLogInfoDTO> offlineLogInfos) {
        this.offlineLogInfos = offlineLogInfos;
    }

    public String getGyVendor() {
        return gyVendor;
    }

    public Integer getInterval() {
        return interval;
    }
}
