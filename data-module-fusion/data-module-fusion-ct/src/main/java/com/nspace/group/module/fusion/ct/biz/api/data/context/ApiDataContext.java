package com.nspace.group.module.fusion.ct.biz.api.data.context;

import cn.hutool.core.lang.Assert;
import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.module.fusion.ct.client.model.BandwidthInfo;
import com.nspace.group.module.fusion.ct.client.model.FluxInfo;
import com.nspace.group.module.fusion.ct.client.model.HttpsNumInfo;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;

import java.time.ZoneOffset;
import java.util.List;

public class ApiDataContext extends DataContext {

    private final String gyVendor = DataPlatformEnum.PLATFORM_CTVIP.getCode().toLowerCase();

    private final String apiBaseUrl = "https://open.ctcdn.cn";

    private final List<VendorAccountWithDomainsDTO> accountDomains;

    private final String apiPath;


    private final String accessKey;

    private final String secretKey;

    private final Long start;

    private final Long end;

    private FluxInfo trafficData;

    private BandwidthInfo bandwidthData;

    private HttpsNumInfo httpsNumData;

    public ApiDataContext(List<VendorAccountWithDomainsDTO> accountDomains,
                          String apiPath, Long start, Long end, ZoneOffset defaultZoneOffset) {
        super(defaultZoneOffset);
        Assert.notEmpty(accountDomains);
        Assert.notBlank(apiPath);
        Assert.notNull(start);
        Assert.notNull(end);
        this.start = start;
        this.end = end;
        VendorAccountWithDomainsDTO firstAccountDomain = accountDomains.get(0);
        this.accessKey  = firstAccountDomain.getSecretId();
        this.secretKey = firstAccountDomain.getSecretKey();
        Assert.notBlank(accessKey);
        Assert.notBlank(secretKey);
        this.apiPath = apiPath;
        this.accountDomains = accountDomains;
    }

    public List<VendorAccountWithDomainsDTO> getAccountDomains() {
        return accountDomains;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public String getApiPath() {
        return apiPath;
    }
    public String getAccessKey() {
        return accessKey;
    }
    public String getSecretKey() {
        return secretKey;
    }


    public FluxInfo getTrafficData() {
        return trafficData;
    }

    public void setTrafficData(FluxInfo trafficData) {
        this.trafficData = trafficData;
    }

    public BandwidthInfo getBandwidthData() {
        return bandwidthData;
    }

    public void setBandwidthData(BandwidthInfo bandwidthData) {
        this.bandwidthData = bandwidthData;
    }

    public HttpsNumInfo getHttpsNumData() {
        return httpsNumData;
    }

    public void setHttpsNumData(HttpsNumInfo httpsNumData) {
        this.httpsNumData = httpsNumData;
    }

    public Long getStart() {
        return start;
    }

    public Long getEnd() {
        return end;
    }

    public String getGyVendor() {
        return gyVendor;
    }
}
