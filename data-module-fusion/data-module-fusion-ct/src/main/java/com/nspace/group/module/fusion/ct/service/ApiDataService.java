package com.nspace.group.module.fusion.ct.service;


import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version :ApiDataService.java, v0.1 2025年05月27日 11:52 Exp
 */
public interface ApiDataService {

    /**
     * 处理CDN第三方接口数据
     *
     * @param accountDomains 域名&账号列表（都是拉流、都是播流或都是CDN）
     */
    void syncCdnData(List<VendorAccountWithDomainsDTO> accountDomains);

    /**
     * 处理直播第三方接口数据
     *
     * @param accountDomains 域名&账号列表（都是拉流、都是播流或都是CDN）
     */
    void syncLiveData(List<VendorAccountWithDomainsDTO> accountDomains);

}
