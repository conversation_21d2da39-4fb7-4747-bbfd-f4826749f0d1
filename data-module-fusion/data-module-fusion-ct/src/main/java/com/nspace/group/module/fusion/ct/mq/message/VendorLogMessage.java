package com.nspace.group.module.fusion.ct.mq.message;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Data;

/**
 * 日志消息
 *
 * <AUTHOR>
 */
@Data
public class VendorLogMessage {

    private static final JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    //节点标识 (固定值)
    private String station;

    //Unix时间戳，单位秒
    private Long unixtime;

    //请求方法
    private String method;

    //请求协议
    private String scheme;

    //域名
    private String domain;

    //URI
    private String uri;

    //请求参数 (包含问号)
    private String uriParam;

    //返回码
    private Integer returnCode;

    //返回时间 (毫秒)
    private Long requestTime;

    //请求大小 (字节)
    private Long requestSize;

    //返回大小 (字节)
    private Long responseSize;

    //命中信息
    private String hitInfo;

    //客户端IP
    private String connectIp;

    //节点IP
    private String serverAddr;

    //节点端口
    private Integer serverPort;

    //Referer
    private String referer;

    //内容类型
    private String contentType;

    //User-Agent
    private String userAgent;

    //客户端口
    private Integer remotePort;

    //文件最后修改时间
    private String lastModified;

    //请求唯一ID
    private String uuid;

    //Via头信息
    private String viaInfo;

    //应答首字节时间 (毫秒)
    private Long responseFbtTime;

    //实际发送body大小
    private Long bodyBytesSent;

    //Content-Length响应头
    private Long sentHttpContentLength;

    //请求Range头
    private String httpRange;

    //应答头里表示的range信息(由源站创建)
    private String sentHttpContentRange;

    //业务定制字段
    private String dyUserInfo;

    //X-Forwarded-For
    private String httpXForwardedFor;

    //CDN节点所在的国家2字母缩写(ISO 3166)
    private String country;

    //HTTP协议版本
    private String httpVersion;

    //QUIC协议标记
    private String quic;

    // 数据来源标识，默认0，内部数据【第三方数据】 1.腾讯数据；2.总过电信；3.七牛云 4:阿里云
    private Integer dataPlatform;

    // 是否是内部请求: 0-否，1-是
    private Integer internal;

    //采用jackson序列化
    public String serialize() {
        try {
            return jsonMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            //不应该进到这里
            return null;
        }
    }
}
