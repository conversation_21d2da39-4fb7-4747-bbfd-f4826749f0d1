package com.nspace.group.module.fusion.ct.service;

import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.module.infra.design.chain.DataHandler;
import com.nspace.group.module.fusion.ct.biz.api.data.context.ApiDataContext;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.fusion.ct.biz.api.data.strategy.composition.*;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("ctApiDataService")
@Slf4j
public class ApiDataServiceImpl implements ApiDataService {

    private static final ZoneOffset defaultOffset = ZoneOffset.ofHours(8);

    private static final String trafficApiPath = "/api/v2/statisticsanalysis/query_hit_flow_rate_data_by_domain";
    private static final String bandwidthApiPath = "/api/v2/statisticsanalysis/query_bandwidth_data";
    private static final String httpsNumApiPath = "/api/v2/statisticsanalysis/query_request_num_data";

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource(name = "ctCdnTrafficSync")
    private CdnTrafficSync cdnTrafficSync;

    @Resource(name = "ctCdnBandwidthSync")
    private CdnBandwidthSync cdnBandwidthSync;

    @Resource(name = "ctCdnHttpsNumSync")
    private CdnHttpsNumSync cdnHttpsNumSync;

    @Resource(name = "ctLssPullTrafficSync")
    private LssPullTrafficSync lssPullTrafficSync;

    @Resource(name = "ctLssPullBandwidthSync")
    private LssPullBandwidthSync lssPullBandwidthSync;

    @Resource(name = "ctLssPushTrafficSync")
    private LssPushTrafficSync lssPushTrafficSync;

    @Override
    public void syncCdnData(List<VendorAccountWithDomainsDTO> accountDomains) {
        List<VendorAccountWithDomainsDTO> cdnAccountDomains = accountDomains.stream()
                .filter(accountDomain -> BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(accountDomain.getBizType()))
                .collect(Collectors.toList());

        if (cdnAccountDomains.isEmpty()) {
            log.warn("syncCdnData,no_account_domains_provided,biz_type={},no_op", BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode());
        } else {
            LocalDateTime endTime = LocalDateTime.now(defaultOffset).truncatedTo(ChronoUnit.SECONDS);

            LocalDateTime timePoint = endTime.minusHours(2L);

            long start = timePoint.toEpochSecond(defaultOffset);
            long end = endTime.toEpochSecond(defaultOffset);

            DataContext trafficDataContext = new ApiDataContext(cdnAccountDomains, trafficApiPath, start, end, defaultOffset);
            DataContext bandwidthDataContext = new ApiDataContext(cdnAccountDomains, bandwidthApiPath, start, end, defaultOffset);
            DataContext httpsNumDataContext = new ApiDataContext(cdnAccountDomains, httpsNumApiPath, start, end, defaultOffset);

            DataHandler trafficHandler = cdnTrafficSync.composeChain();
            DataHandler bandwidthHandler = cdnBandwidthSync.composeChain();
            DataHandler httpsNumHandler = cdnHttpsNumSync.composeChain();

            executorService.execute(() -> trafficHandler.handle(trafficDataContext));
            executorService.execute(() -> bandwidthHandler.handle(bandwidthDataContext));
            executorService.execute(() -> httpsNumHandler.handle(httpsNumDataContext));
        }

    }

    @Override
    public void syncLiveData(List<VendorAccountWithDomainsDTO> accountDomains) {
        Map<String, List<VendorAccountWithDomainsDTO>> lssDomainTypeDomainsMap = accountDomains.stream()
                .filter(accountDomain -> BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(accountDomain.getBizType()))
                .collect(Collectors.groupingBy(VendorAccountWithDomainsDTO::getDomainType, Collectors.toList()));

        lssDomainTypeDomainsMap.forEach((domainType, accountDomainsForType) -> {
            if (accountDomainsForType.isEmpty()) {
                log.warn("syncLiveData,no_account_domains_provided,biz_type={},domain_type={},no_op",
                        BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), domainType);
            } else {
                LocalDateTime endTime = LocalDateTime.now(defaultOffset).truncatedTo(ChronoUnit.SECONDS);

                LocalDateTime timePoint = endTime.minusHours(2L);

                long start = timePoint.toEpochSecond(defaultOffset);
                long end = endTime.toEpochSecond(defaultOffset);
                if (LiveDomainTypeEnum.DOMAIN_TYPE_PULL.isSelf(domainType)) {
                    DataContext trafficDataContext = new ApiDataContext(accountDomainsForType, trafficApiPath, start, end, defaultOffset);
                    DataContext bandwidthDataContext = new ApiDataContext(accountDomainsForType, bandwidthApiPath, start, end, defaultOffset);
                    DataHandler trafficHandler = lssPullTrafficSync.composeChain();
                    DataHandler bandwidthHandler = lssPullBandwidthSync.composeChain();

                    executorService.execute(() -> trafficHandler.handle(trafficDataContext));
                    executorService.execute(() -> bandwidthHandler.handle(bandwidthDataContext));
                } else if (LiveDomainTypeEnum.DOMAIN_TYPE_PUSH.isSelf(domainType)) {
                    DataContext trafficDataContext = new ApiDataContext(accountDomainsForType, trafficApiPath, start, end, defaultOffset);
                    DataHandler trafficHandler = lssPushTrafficSync.composeChain();

                    executorService.execute(() -> trafficHandler.handle(trafficDataContext));
                }
            }
        });

    }
}
