package com.nspace.group.module.fusion.ct.convert;

import com.nspace.group.module.fusion.ct.mq.message.VendorLogMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version :VendorLogMessageConvert.java, v0.1 2024年11月19日 10:21 zhangxin Exp
 */
@Mapper
public interface VendorLogMessageConvert {

    VendorLogMessageConvert INSTANCE = Mappers.getMapper(VendorLogMessageConvert.class);

    VendorLogMessage toMessage(Map<String, String> logDataMap);

    List<VendorLogMessage> toMessages(List<Map<String, String>> logDataMapList);
}
