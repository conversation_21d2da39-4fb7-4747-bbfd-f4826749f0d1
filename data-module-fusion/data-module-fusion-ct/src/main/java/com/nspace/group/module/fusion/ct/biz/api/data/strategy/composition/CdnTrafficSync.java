package com.nspace.group.module.fusion.ct.biz.api.data.strategy.composition;

import com.nspace.group.module.infra.design.chain.DataProcessHandler;
import com.nspace.group.module.infra.design.chain.DataHandler;
import com.nspace.group.module.fusion.ct.biz.api.data.strategy.usage.CdnTrafficProcessStrategy;
import com.nspace.group.module.infra.design.strategy.DataChainComposeStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("ctCdnTrafficSync")
public class CdnTrafficSync implements DataChainComposeStrategy {

    @Resource(name = "ctCdnTrafficProcessStrategy")
    private CdnTrafficProcessStrategy cdnTrafficProcessStrategy;

    @Override
    public DataHandler composeChain() {
        return new DataProcessHandler(cdnTrafficProcessStrategy);
    }
}
