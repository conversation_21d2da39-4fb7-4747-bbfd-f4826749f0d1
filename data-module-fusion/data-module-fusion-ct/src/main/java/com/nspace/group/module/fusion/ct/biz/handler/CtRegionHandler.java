package com.nspace.group.module.fusion.ct.biz.handler;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :CtBillingRegionHandler.java, v0.1 2025年05月28日 14:33 zhangxin Exp
 */
public class CtRegionHandler {

    //国家（地区）映射json
    private static final JSONObject regionJson = JSONUtil.parseObj("{0:\"cn\",1:\"na\",201:\"na\",202:\"eu\",203:\"ap1\",204:\"ap2\",205:\"ap3\",206:\"af\",207:\"sa\"}");

    //国家（地区）映射
    private static final Map<Integer, String> regionMapping;

    static {
        TypeReference<Map<Integer, String>> regionTypeReference = new TypeReference<>() {};
        regionMapping = regionJson.toBean(regionTypeReference).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 获取天翼abroad字段对应耕耘计费区域
     *
     * @param abroadCode 区域编码
     * @return 编码对应值，没有则返回cn
     */
    public static String abroadToRegion(Integer abroadCode) {
        return StrUtil.blankToDefault(regionMapping.get(abroadCode), "cn");
    }
}
