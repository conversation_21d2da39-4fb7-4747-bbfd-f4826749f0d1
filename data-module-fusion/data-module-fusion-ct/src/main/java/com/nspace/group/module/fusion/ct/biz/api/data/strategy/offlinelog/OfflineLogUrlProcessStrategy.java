package com.nspace.group.module.fusion.ct.biz.api.data.strategy.offlinelog;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.fusion.ct.biz.api.data.context.OfflineLogContext;
import com.nspace.group.module.infra.design.strategy.CallResultProcessStrategy;
import com.nspace.group.module.fusion.ct.client.model.LogDetailResult;
import com.nspace.group.module.fusion.ct.client.model.LogSourceInfo;
import com.nspace.group.module.fusion.ct.convert.LogSourceInfoConvert;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.vendor.offlinelog.VendorOfflineLogInfoService;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component("ctOfflineLogUrlProcessStrategy")
public class OfflineLogUrlProcessStrategy implements CallResultProcessStrategy {

    @Resource
    private VendorOfflineLogInfoService offlineLogInfoService;

    @Override
    public boolean process(DataContext dataContext) {
        OfflineLogContext offlineLogContext = (OfflineLogContext) dataContext;
        VendorAccountWithDomainsDTO accountDomain = offlineLogContext.getAccountDomain();
        Long tenantId = accountDomain.getBindTenantId();
        String domain = accountDomain.getDomain();
        String platform = accountDomain.getPlatform();
        List<LogDetailResult> logDetailResults = offlineLogContext.getLogDetailResults();
        List<LogSourceInfo> logInfoList = new ArrayList<>();
        List<String> urlsForDomain = logDetailResults.stream()
                .filter(result -> !CollectionUtils.isEmpty(result.getLogs()))
                .peek(result -> logInfoList.addAll(result.getLogs()))
                .flatMap(resultDTO -> {
                    List<LogSourceInfo> logs = resultDTO.getLogs();
                    return Objects.isNull(logs) ? Stream.empty() : logs.stream().map(LogSourceInfo::getUrl);
                }).collect(Collectors.toList());
        List<VendorOfflineLogInfoDTO> processedLogInfos = offlineLogInfoService.getLogInfosByUrls(tenantId, domain, platform, urlsForDomain);

        Set<String> dbLogInfoFileUrls = processedLogInfos.stream().map(VendorOfflineLogInfoDTO::getFileUrl).collect(Collectors.toSet());
        //过滤掉新获取到的日志文件信息中已经处理过的数据
        List<VendorOfflineLogInfoDTO> newLogInfos = logInfoList.stream()
                .filter(logInfo -> !dbLogInfoFileUrls.contains(logInfo.getUrl()))
                .map(logInfo -> LogSourceInfoConvert.INSTANCE.getOfflineLogInfo(tenantId, domain, platform, logInfo))
                .collect(Collectors.toList());

        offlineLogContext.setOfflineLogInfos(newLogInfos);
        return true;
    }
}
