package com.nspace.group.module.fusion.ct.biz.api.data.strategy.composition;

import com.nspace.group.module.infra.design.chain.DataProcessHandler;
import com.nspace.group.module.infra.design.chain.DataHandler;
import com.nspace.group.module.fusion.ct.biz.api.data.strategy.usage.CdnHttpsNumProcessStrategy;
import com.nspace.group.module.infra.design.strategy.DataChainComposeStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("ctCdnHttpsNumSync")
public class CdnHttpsNumSync implements DataChainComposeStrategy {

    @Resource(name = "ctCdnHttpsNumProcessStrategy")
    private CdnHttpsNumProcessStrategy cdnHttpsNumProcessStrategy;

    @Override
    public DataHandler composeChain() {
        return new DataProcessHandler(cdnHttpsNumProcessStrategy);
    }
}
