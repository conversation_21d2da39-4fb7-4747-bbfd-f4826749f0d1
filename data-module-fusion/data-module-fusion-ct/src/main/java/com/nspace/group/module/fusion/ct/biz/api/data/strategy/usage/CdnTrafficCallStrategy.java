package com.nspace.group.module.fusion.ct.biz.api.data.strategy.usage;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.exception.ServiceException;
import com.nspace.group.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.fusion.ct.biz.api.data.context.ApiDataContext;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.infra.design.strategy.CallStrategy;
import com.nspace.group.module.fusion.ct.client.api.UsageDataApi;
import com.nspace.group.module.fusion.ct.client.impl.auth.CtApiKeyAuth;
import com.nspace.group.module.fusion.ct.client.impl.factory.CtApiClientFactory;
import com.nspace.group.module.fusion.ct.client.model.FluxInfo;
import com.nspace.group.module.fusion.ct.client.model.UsageDataRequest;
import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Slf4j
@Component("ctCdnTrafficCallStrategy")
public class CdnTrafficCallStrategy implements CallStrategy {
    @Override
    public void call(DataContext dataContext) {
        ApiDataContext apiDataContext = (ApiDataContext) dataContext;

        Long start = apiDataContext.getStart();
        Long end = apiDataContext.getEnd();

        String apiPath = apiDataContext.getApiPath();
        String apiBaseUrl = apiDataContext.getApiBaseUrl();
        String accessKey = apiDataContext.getAccessKey();
        String secretKey = apiDataContext.getSecretKey();

        String domainsAsString = apiDataContext.getAccountDomains().stream()
                .map(VendorAccountWithDomainsDTO::getDomain).collect(Collectors.joining(StringPool.COMMA));
        String groupBy = "domain";
        Integer abroad = 0;

        //拼接鉴权对象
        Authentication auth = new CtApiKeyAuth(accessKey, secretKey, apiPath);

        //调用天翼API
        log.info("begin_api_call,apiPath={},start={},end={},domain={}", apiPath, start, end,domainsAsString);
        // 获取ApiClient
        ApiClient apiClient = CtApiClientFactory.INSTANCE.createApiClient(apiBaseUrl, auth);
        //创建Api对象
        UsageDataApi dataApi = new UsageDataApi(apiClient);
        try {
            UsageDataRequest usageDataRequest = new UsageDataRequest(start, end, domainsAsString, groupBy, abroad);
            Object ctData = dataApi.getUsageData(usageDataRequest);
            FluxInfo trafficDetail = (FluxInfo) ctData;
            log.info("after_api_call,apiPath={},start={},end={},domain={},code={}",
                    apiPath, start, end, domainsAsString, trafficDetail.getCode());
            if (100000 == trafficDetail.getCode()) {
                log.info("api_call_success,path={},start={},end={},domain={},api_resp={}",
                        apiPath, start, end, domainsAsString, JsonUtils.toJsonString(trafficDetail));
                apiDataContext.setTrafficData(trafficDetail);
                return;
            }
            throw new ServiceException(GlobalErrorCodeConstants.UNKNOWN.getCode(), trafficDetail.getMessage());
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("UsageDataApi.getUsageData,unknown_api_exception,apiPath={},start={},end={}", apiPath, start, end);
            throw new RuntimeException(ExceptionUtil.getRootCauseMessage(e));
        }
    }
}
