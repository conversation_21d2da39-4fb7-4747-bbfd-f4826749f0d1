package com.nspace.group.module.fusion.ct.client.api;

import com.google.common.reflect.TypeToken;

import com.nspace.group.module.fusion.ct.client.impl.auth.CtApiKeyAuth;
import com.nspace.group.module.fusion.ct.client.model.BandwidthInfo;
import com.nspace.group.module.fusion.ct.client.model.FluxInfo;
import com.nspace.group.module.fusion.ct.client.model.HttpsNumInfo;
import com.nspace.group.module.fusion.ct.client.model.UsageDataRequest;
import com.nspace.group.module.infra.client.*;

import java.lang.reflect.Type;
import java.util.*;


public class UsageDataApi {
    private ApiClient localVarApiClient;


    public UsageDataApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    private static final String bandwidthPath = "/api/v2/statisticsanalysis/query_bandwidth_data";
    private static final String fluxPath = "/api/v2/statisticsanalysis/query_hit_flow_rate_data_by_domain";
    private static final String httpsPath = "/api/v2/statisticsanalysis/query_request_num_data";


    /**
     * 为 getUsageData 构建 Call
     *
     * @param request   请求参数
     * @param _callback Callback for upload/download progress
     * @return 待执行的 Call
     * @throws Exception 请求体序列化失败抛出
     */
    public okhttp3.Call getUsageDataCall(UsageDataRequest request, String path, CtApiKeyAuth authentication, final ApiCallback _callback) throws Exception {

        String ak = authentication.getAccessKey();
        String appSecret = authentication.getSecretKey();

        String gmtdate = CtApiKeyAuth.formatdate();
        String sign_string = "POST\n\napplication/json;charset=utf-8\n" + gmtdate + "\n" + path;
        String signature = CtApiKeyAuth.getHmacSHA256_Base64(sign_string, appSecret);
        String authorization = String.format("CTYUN %s:%s", ak, signature);

        List<Pair> localVarQueryParams = new ArrayList<>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<>();
        Map<String, String> localVarHeaderParams = new HashMap<>();
        Map<String, String> localVarCookieParams = new HashMap<>();
        Map<String, Object> localVarFormParams = new HashMap<>();
        Map<String, Object> localVarPostBody = new HashMap<>();


        //域名集合
        List<String> domainList = Arrays.asList(request.getDomain().split(","));
        List<String> groupByList = Arrays.asList(request.getGroupBy().split(","));


        localVarPostBody.put("start_time", request.getStart());
        localVarPostBody.put("end_time", request.getEnd());
        localVarPostBody.put("domain", domainList);
        if (request.getAbroad() != null) {
            localVarPostBody.put("abroad", request.getAbroad());
        }
        localVarPostBody.put("group_by", groupByList);


        localVarHeaderParams.put("date", gmtdate);
        localVarHeaderParams.put("authorization", authorization);
        localVarHeaderParams.put("Host", "open.ctcdn.cn");
        localVarHeaderParams.put("Content-Type", "application/json;charset=utf-8");

        return localVarApiClient.buildCall(path, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call getUsageDataValidateBeforeCall(UsageDataRequest request, String path, CtApiKeyAuth authentication, final ApiCallback _callback) throws Exception {

        return getUsageDataCall(request, path, authentication, _callback);

    }

    /**
     * 获取 usage data
     *
     * @param request 请求参数
     * @return DAGCollection
     * @throws Exception 请求失败或响应体反序列化失败抛出
     */
    public Object getUsageData(UsageDataRequest request) throws Exception {
        ApiResponse<Object> localVarResp = getUsageDataWithDomain(request);
        return localVarResp.getData();
    }

    public ApiResponse<Object> getUsageDataWithDomain(UsageDataRequest request) throws Exception {

        Type localVarReturnType;
        CtApiKeyAuth authentication = (CtApiKeyAuth) localVarApiClient.getAuthentication();
        String path = authentication.getPath();

        if (bandwidthPath.equals(path)) {
            localVarReturnType = new TypeToken<BandwidthInfo>() {
            }.getType();
        } else if (fluxPath.equals(path)) {
            localVarReturnType = new TypeToken<FluxInfo>() {
            }.getType();
        } else if (httpsPath.equals(path)) {
            localVarReturnType = new TypeToken<HttpsNumInfo>() {
            }.getType();
        } else {
            throw new Exception("path is not valid");
        }

        okhttp3.Call localVarCall = getUsageDataValidateBeforeCall(request, path, authentication, null);

        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }
}
