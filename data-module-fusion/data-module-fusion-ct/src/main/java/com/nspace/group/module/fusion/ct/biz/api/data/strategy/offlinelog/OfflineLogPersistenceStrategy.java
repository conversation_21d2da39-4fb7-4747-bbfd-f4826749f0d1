package com.nspace.group.module.fusion.ct.biz.api.data.strategy.offlinelog;

import cn.hutool.core.lang.Assert;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.fusion.ct.biz.api.data.context.OfflineLogContext;
import com.nspace.group.module.infra.design.strategy.LogPersistenceStrategy;
import com.nspace.group.module.fusion.ct.biz.handler.CtCdnOfflineLogHandler;
import com.nspace.group.module.fusion.ct.client.api.LogSourceApi;
import com.nspace.group.module.fusion.ct.client.impl.factory.CtApiClientFactory;
import com.nspace.group.module.fusion.ct.mq.message.VendorLogMessage;
import com.nspace.group.module.fusion.ct.mq.producer.VendorLogMsgProducer;
import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.infra.enums.offlinelog.OfflineLogStatusEnum;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.infra.service.vendor.offlinelog.VendorOfflineLogInfoService;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("ctOfflineLogPersistenceStrategy")
public class OfflineLogPersistenceStrategy implements LogPersistenceStrategy {

    private static final String LOG_TOPIC = "ods_general_cdn_log_vendor";

    @Resource(name = "platformNumCaffeineCache")
    private LoadingCache<String, Integer> platformNumCaffeineCache;

    @Resource(name = "vendorCdnLogMsgProducer")
    private VendorLogMsgProducer vendorLogMsgProducer;

    @Resource
    private VendorOfflineLogInfoService offlineLogInfoService;
    
    @Override
    public void persist(DataContext dataContext) {
        OfflineLogContext offlineLogContext = (OfflineLogContext) dataContext;
        VendorAccountWithDomainsDTO accountDomain = offlineLogContext.getAccountDomain();
        Long tenantId = accountDomain.getBindTenantId();
        String domain = accountDomain.getDomain();
        String platform = accountDomain.getPlatform();
        List<VendorOfflineLogInfoDTO> newLogInfos = offlineLogContext.getOfflineLogInfos();

        String gyVendor = offlineLogContext.getGyVendor();
        Integer platformNum = platformNumCaffeineCache.get(gyVendor);
        Assert.notNull(platformNum, "unknown_gy_vendor,gy_vendor={}", gyVendor);

        newLogInfos.forEach(vendorLogInfo -> {
            String fileUrl = vendorLogInfo.getFileUrl();
            log.info("begin_log_source_fetching,tenant_id={},domain={},url={}", tenantId, domain, fileUrl);
            Object logData = fetchLogSource(fileUrl);
            List<Map<String, String>> logMapList = CtCdnOfflineLogHandler.preprocess(logData);
            log.info("end_log_source_fetching,tenant_id={},domain={},platform={},url={}", tenantId, domain, platform, fileUrl);
            List<VendorLogMessage> logMessages = CtCdnOfflineLogHandler.transformLogs(platformNum, logMapList);
            log.info("logTransformService.transformLogs,log_messages_generated,count={}", logMessages.size());
            logMessages.forEach(vendorLogMessage ->
                    vendorLogMsgProducer.send(LOG_TOPIC, vendorLogMessage)
            );
            //处理成功
            vendorLogInfo.setStatus(OfflineLogStatusEnum.SUCCESS.getStatus());
            //新增日志文件新信息
            log.info("VendorOfflineLogInfoService.saveLogInfo,tenant_id={},domain={},platform={},file_url={}",
                    tenantId, domain, platform, vendorLogInfo.getFileUrl());
            offlineLogInfoService.saveLogInfo(vendorLogInfo);
        });
    }

    /**
     * 获取离线日志信息
     *
     * @param sourceLocation Log源文件路径
     * @return Object
     */
    private Object fetchLogSource(String sourceLocation) {
        //获取ApiClient
        ApiClient apiClient = CtApiClientFactory.INSTANCE.createApiClient(sourceLocation);
        //创建LogSourceApi对象
        LogSourceApi logSourceApi = new LogSourceApi(apiClient);
        try {
            byte[] logSourceInBytes = logSourceApi.getLogSource();
            log.info("fetchLogSource,log_source_fetched,sourceLocation={}", sourceLocation);
            return logSourceInBytes;
        } catch (ApiException e) {
            log.error("LogSourceApi.fetchLogSource,unknown_api_exception,sourceLocation={}", sourceLocation);
            throw new RuntimeException(e);
        }
    }
}
