package com.nspace.group.scheduler.controller;

import com.nspace.group.framework.common.pojo.CommonResult;
import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.framework.common.util.object.BeanUtils;
import com.nspace.group.framework.job.core.util.CronUtils;
import com.nspace.group.module.infra.dal.dataobject.job.JobDO;
import com.nspace.group.module.infra.service.job.JobService;
import com.nspace.group.module.infra.service.job.dto.JobPageReqDTO;
import com.nspace.group.module.infra.service.job.dto.JobSaveReqDTO;
import com.nspace.group.scheduler.controller.vo.job.JobPageReqVO;
import com.nspace.group.scheduler.controller.vo.job.JobRespVO;
import com.nspace.group.scheduler.controller.vo.job.JobSaveReqVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import org.quartz.SchedulerException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static com.nspace.group.framework.common.pojo.CommonResult.success;

/**
 * job 任务操作控制器
 *
 * @author：<EMAIL>
 * @date： 2024/11/8
 * @time：15:38
 */
@RestController
@RequestMapping("/job")
@Validated
public class JobController {

    @Resource
    private JobService jobService;

    @PostMapping("/create")
    @Operation(summary = "创建定时任务")
    public CommonResult<Long> createJob(@RequestBody JobSaveReqVO createReqVO)
            throws SchedulerException {
        return success(jobService.createJob(BeanUtils.toBean(createReqVO, JobSaveReqDTO.class)));
    }

    @PutMapping("/update")
    @Operation(summary = "更新定时任务")
    public CommonResult<Boolean> updateJob(@RequestBody JobSaveReqVO updateReqVO)
            throws SchedulerException {
        jobService.updateJob(BeanUtils.toBean(updateReqVO, JobSaveReqDTO.class));
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新定时任务的状态")
    @Parameters({
            @Parameter(name = "id", description = "编号", required = true, example = "1024"),
            @Parameter(name = "status", description = "状态", required = true, example = "1"),
    })
    public CommonResult<Boolean> updateJobStatus(@RequestParam(value = "id") Long id, @RequestParam("status") Integer status)
            throws SchedulerException {
        jobService.updateJobStatus(id, status);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除定时任务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<Boolean> deleteJob(@RequestParam("id") Long id)
            throws SchedulerException {
        jobService.deleteJob(id);
        return success(true);
    }

    @PutMapping("/trigger")
    @Operation(summary = "触发定时任务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<Boolean> triggerJob(@RequestParam("id") Long id) throws SchedulerException {
        jobService.triggerJob(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得定时任务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<JobRespVO> getJob(@RequestParam("id") Long id) {
        JobDO job = jobService.getJob(id);
        return success(BeanUtils.toBean(job, JobRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得定时任务分页")
    public CommonResult<PageResult<JobRespVO>> getJobPage(@Valid JobPageReqVO pageVO) {
        PageResult<JobDO> pageResult = jobService.getJobPage(BeanUtils.toBean(pageVO, JobPageReqDTO.class));
        return success(BeanUtils.toBean(pageResult, JobRespVO.class));
    }

    @GetMapping("/get-next-times")
    @Operation(summary = "获得定时任务的下 n 次执行时间")
    @Parameters({
            @Parameter(name = "id", description = "编号", required = true, example = "1024"),
            @Parameter(name = "count", description = "数量", example = "5")
    })
    public CommonResult<List<LocalDateTime>> getJobNextTimes(
            @RequestParam("id") Long id,
            @RequestParam(value = "count", required = false, defaultValue = "5") Integer count) {
        JobDO job = jobService.getJob(id);
        if (job == null) {
            return success(Collections.emptyList());
        }
        return success(CronUtils.getNextTimes(job.getCronExpression(), count));
    }
}
