package com.nspace.group.scheduler.controller;

import com.nspace.group.module.logs.service.offlinelog.OfflineLogFactory;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogGenerateService;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogService;
import com.nspace.group.module.logs.service.offlinelog.dto.TaskDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class OfflineJobSyncController {


    @Resource
    private OfflineLogService offlineLogService;



    @GetMapping("/offline-log/sync")
    public void syncOfflineLog(TaskDTO dto) {
        offlineLogService.dealOfflineLog(dto);
    }

    @GetMapping("/offline-log/merge")
    public void mergeOfflineLog(TaskDTO dto) {
        offlineLogService.dealMergeFilesJob(dto);
    }


    @GetMapping("/offline-log/delayed")
    public void delayedOfflineLog(TaskDTO taskDTO) {
        offlineLogService.dealDelayedOfflineLog(taskDTO);
    }
}
