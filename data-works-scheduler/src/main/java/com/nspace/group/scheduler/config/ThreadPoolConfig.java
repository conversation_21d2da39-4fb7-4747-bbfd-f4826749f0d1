package com.nspace.group.scheduler.config;

/**
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/12/9
 * @time：21:02
 */

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ThreadPoolConfig {

    @Bean(name = "commonThreadPool")
    @Primary
    public Executor commonThreadPool(
            @Value("${dataworks.threadpool.corePoolSize:10}") int corePoolSize,
            @Value("${dataworks.threadpool.maxPoolSize:20}") int maxPoolSize,
            @Value("${dataworks.threadpool.queueCapacity:30}") int queueCapacity,
            @Value("${dataworks.threadpool.keepAliveSeconds:60}") int keepAliveSeconds) {

        return buildExecutor("common-thread-pool-", corePoolSize, maxPoolSize, queueCapacity, keepAliveSeconds);
    }

    @Bean(name = "deliveryThreadPool")
    public Executor deliveryThreadPool(
            @Value("${dataworks.threadpool.corePoolSize:10}") int corePoolSize,
            @Value("${dataworks.threadpool.maxPoolSize:20}") int maxPoolSize,
            @Value("${dataworks.threadpool.queueCapacity:30}") int queueCapacity,
            @Value("${dataworks.threadpool.keepAliveSeconds:60}") int keepAliveSeconds) {

        return buildExecutor("delivery-thread-pool-", corePoolSize, maxPoolSize, queueCapacity, keepAliveSeconds);
    }

    private ThreadPoolTaskExecutor buildExecutor(String threadNamePrefix, int corePoolSize, int maxPoolSize, int queueCapacity, int keepAliveSeconds) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix(threadNamePrefix);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.initialize();
        return executor;
    }
}