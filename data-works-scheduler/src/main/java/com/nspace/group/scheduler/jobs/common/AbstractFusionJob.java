package com.nspace.group.scheduler.jobs.common;

import cn.hutool.json.JSONObject;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.framework.common.enums.LiveDomainTypeEnum;
import com.nspace.group.framework.common.util.date.DateUtils;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.framework.job.core.handler.JobHandler;
import com.nspace.group.module.infra.service.cloudvendor.VendorAccountService;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.*;

@Slf4j
public abstract class AbstractFusionJob implements JobHandler {

    @Resource
    protected VendorAccountService vendorAccountService;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    protected JSONObject validateAndParseParam(String param) {
        JSONObject object = JsonUtils.parseObject(param, JSONObject.class);
        if (object == null || object.isEmpty()) {
            log.warn("Invalid parameters, execution halted: params_error");
            return null;
        }
        return object;
    }

    protected String[] getTimeRange(JSONObject object) {
        Integer interval = object.getInt("interval");
        String endTime = DateUtils.getSystemTime();
        String startTime = DateUtils.minusSecondToStreamTime(endTime, interval);
        return new String[]{startTime, endTime};
    }

    protected String getDomainCode(JSONObject object) {
        String domainType = object.getStr("domain_type");
        String code = LiveDomainTypeEnum.getCode(domainType);
        if (code == null || code.isEmpty()) {
            log.warn("Invalid domain type: {}", domainType);
            return null;
        }
        return code;
    }

    protected List<VendorAccountWithDomainsDTO> getVendorAccountDomains(String code) {
        return vendorAccountService.getVendorAccountDomains(
                DataPlatformEnum.getQueryVendorCodes(),
                BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(),
                code
        );
    }

    protected abstract void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime);

    @Override
    public String execute(String param) throws Exception {
        String execClassName = this.getClass().getSimpleName();
        JSONObject object = validateAndParseParam(param);
        if (object == null) {
            return "params_error";
        }
        String code = getDomainCode(object);
        if (code == null) return "domain_type_error";

        String[] timeRange = getTimeRange(object);
        List<VendorAccountWithDomainsDTO> vendorAccountDomains = getVendorAccountDomains(code);

        if (vendorAccountDomains == null || vendorAccountDomains.isEmpty()) {
            log.info("No vendor account domains found, execution halted");
            return "vendor account is empty";
        }

        CountDownLatch latch = new CountDownLatch(vendorAccountDomains.size());

        vendorAccountDomains.forEach(account -> {
            executorService.execute(() -> {
                try {
                    log.info("{}#starting account:{}, tenantId:{}, domain:{}, param:{}", execClassName, account.getAccount(), account.getBindTenantId(), account.getDomain(), param);
                    processAccount(account, timeRange[0], timeRange[1]);
                    log.info("{}#finish account:{}, tenantId:{}, domain:{}, param:{}", execClassName, account.getAccount(), account.getBindTenantId(), account.getDomain(), param);
                } catch (Exception e) {
                    log.error("{}#process account:{}, tenantId:{}, domain:{}, error:{}", execClassName, account.getAccount(), account.getBindTenantId(), account.getDomain(), e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        });
        latch.await();

        log.info("All tasks completed for {}", execClassName);
        return "success";
    }
}