package com.nspace.group.scheduler.jobs.fusion.usage;


import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.fusion.ct.service.ApiDataService;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 天翼通用CDN计费数据融合
 */
@Slf4j
@Component
public class FusionCtCdnUsageDataJob extends AbstractFusionJob {

    @Resource(name = "ctApiDataService")
    private ApiDataService apiDataService;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
    }

    @Override
    public String execute(String param) throws Exception {

        List<VendorAccountWithDomainsDTO> accountWithDomains = getTenantDomainParams();
        if (accountWithDomains.isEmpty()) {
            log.warn("no_vendor_account_provided,no_op");
            return "vendor account is empty";
        }
        Lists.partition(accountWithDomains, 20).forEach(subAccountDomains -> {
            executorService.execute(() -> {
                List<String> domainsForBatch = subAccountDomains.stream()
                        .map(VendorAccountWithDomainsDTO::getDomain).collect(Collectors.toList());
                log.info("[job_execute_start],domains={},param={}", domainsForBatch, param);
                apiDataService.syncCdnData(subAccountDomains);
                log.info("[job_execute_end],domains={},param={}", domainsForBatch, param);
            });
        });
        return "success";
    }

    private List<VendorAccountWithDomainsDTO> getTenantDomainParams() {
        String platform = DataPlatformEnum.PLATFORM_CTVIP.getCode().toLowerCase();
        List<VendorAccountWithDomainsDTO> accountDomains = vendorAccountService.getVendorAccountDomains(Collections.singletonList(platform),
                BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), null);
        return accountDomains.stream()
                .filter(accountDomain -> {
                    String extInfo = accountDomain.getExtInfo();
                    return StrUtil.isNotBlank(extInfo)
                            && JsonUtils.parseTree(extInfo)
                            .path("data_sync_config")
                            .path("usage")
                            .path("enable")
                            .asBoolean();
                })
                .sorted(Comparator.comparing(VendorAccountWithDomainsDTO::getId))
                .collect(Collectors.toList());
    }
}
