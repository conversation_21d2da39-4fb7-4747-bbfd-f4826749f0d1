package com.nspace.group.scheduler.jobs.logs.offlinelog.live;

import cn.hutool.json.JSONObject;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.framework.job.core.handler.JobHandler;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogService;
import com.nspace.group.module.logs.service.offlinelog.dto.TaskDTO;
import com.nspace.group.scheduler.jobs.logs.offlinelog.BaseOfflineJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 用于导出历史数据和延迟数据：
 * - 根据配置及历史导出批次生成新的导出任务；
 * - 若历史数据与远端一致，且存在未同步的延迟日志，则执行增量导出；
 * - 若历史数据与远端不一致，则执行全量导出。
 */
@Slf4j
@Component("liveDelayedOfflineLogGenerateJob")
public class LiveDelayedOfflineLogGenerateJob  extends BaseOfflineJob {


    @Override
    protected String doExecute(TaskDTO taskDTO) {
        return offlineLogService.dealDelayedOfflineLog(taskDTO);
    }
}
