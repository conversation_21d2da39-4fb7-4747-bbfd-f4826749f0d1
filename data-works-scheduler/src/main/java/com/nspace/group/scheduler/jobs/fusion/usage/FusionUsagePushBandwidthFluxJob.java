package com.nspace.group.scheduler.jobs.fusion.usage;

import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import com.nspace.group.scheduler.service.tencent.TencentSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 数据分析-计费用量-直播推流带宽和流量统计
 * 每15分钟执行一次
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/6
 * @time：11:25
 */
@Slf4j
@Component
public class FusionUsagePushBandwidthFluxJob extends AbstractFusionJob {

    @Resource
    private TencentSyncService tencentSyncService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
        if (DataPlatformEnum.PLATFORM_TENCENT.getCode().equals(account.getPlatform().toUpperCase())){
            tencentSyncService.describePushBandwidthAndFluxList(account, startTime, endTime);
        }
    }
}
