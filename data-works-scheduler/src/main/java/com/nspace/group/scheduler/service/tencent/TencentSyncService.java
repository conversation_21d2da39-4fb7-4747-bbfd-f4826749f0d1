package com.nspace.group.scheduler.service.tencent;

import com.nspace.group.module.fusion.tencent.service.stat.realtime.LiveStreamPushService;
import com.nspace.group.module.fusion.tencent.service.stat.usage.StreamPullService;
import com.nspace.group.module.fusion.tencent.service.stat.usage.StreamPushService;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 云商户-腾讯云任务
 *
 * @author：yang<PERSON><EMAIL>
 * @date： 2024/11/6
 * @time：11:40
 */
@Slf4j
@Service
public class TencentSyncService {

    @Resource
    private StreamPullService streamPullService;

    @Resource
    private StreamPushService streamPushService;

    @Resource
    private LiveStreamPushService liveStreamPushService;

    /**
     * 计费用量-直播播放带宽和流量数据查询
     */
    public void describeBillBandwidthAndFluxList(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
        try {
            streamPullService.getPullBandwidthAndFluxList(account, startTime, endTime);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 计费用量-直播推流带宽和流量数据查询
     */
    public void describePushBandwidthAndFluxList(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
        try {
            streamPushService.getPushBandwidthAndFluxList(account, startTime, endTime);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 实时监控-推流流量带宽/推流质量
     *
     * @param account
     * @param startTime
     * @param endTime
     */
    public void monitorRealtimeStreamPushInfo(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
        try {
            liveStreamPushService.getStreamPushQualityList(account, startTime, endTime);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
