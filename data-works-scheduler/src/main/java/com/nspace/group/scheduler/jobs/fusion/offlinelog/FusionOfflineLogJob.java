package com.nspace.group.scheduler.jobs.fusion.offlinelog;


import cn.hutool.json.JSONObject;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.module.fusion.qiniu.service.OfflineLogService;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;


/**
 * 离线日志拉取
 */

@Slf4j
@Component
public class FusionOfflineLogJob extends AbstractFusionJob {

    @Resource(name = "qiniuOfflineLogService")
    private OfflineLogService offlineLogService;

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
    }

    @Override
    public String execute(String param) throws Exception {
        JSONObject object = validateAndParseParam(param);
        if (object == null) {
            return "params_error";
        }

        List<VendorAccountWithDomainsDTO> accountWithDomains = getAccountDomains();
        if (accountWithDomains.isEmpty()) {
            log.warn("no_vendor_account_provided,no_op");
            return "vendor account is empty";
        }
        Integer interval = object.getInt("interval");
        Integer offset = object.getInt("offset");
        Long startTimestamp = object.getLong("start");

        accountWithDomains.forEach(accountWithDomain ->
                executorService.execute(() -> {
                    String account = accountWithDomain.getAccount();
                    Long tenantId = accountWithDomain.getBindTenantId();
                    String domain = accountWithDomain.getDomain();
                    log.info("[job_execute],before_execute,account={},tenantId={},domain={},param={}", account, tenantId, domain, param);
                    offlineLogService.processOfflineLog(accountWithDomain, interval, offset, startTimestamp);
                    log.info("[job_execute],finished_execute,account={},tenantId={},domain={},param={}", account, tenantId, domain, param);
                })
        );
        return "success";
    }

    private List<VendorAccountWithDomainsDTO> getAccountDomains() {
        String platform = DataPlatformEnum.PLATFORM_QINIU.getCode().toLowerCase();
        return vendorAccountService.getVendorAccountDomains(Arrays.asList(platform),
                BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), null);
    }

}
