package com.nspace.group.scheduler.jobs.logs.offlinelog.live;


import com.nspace.group.module.logs.service.offlinelog.dto.TaskDTO;
import com.nspace.group.scheduler.jobs.logs.offlinelog.BaseOfflineJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 执行离线日志同步任务，包括：
 * - 同步最近1小时内的用户数据；
 * - 处理失败次数未超过3次的历史失败任务。
 */
@Slf4j
@Component("liveOfflineLogGenerateJob")
public class LiveOfflineLogGenerateJob extends BaseOfflineJob {


    @Override
    protected String doExecute(TaskDTO taskDTO) {
        return offlineLogService.dealOfflineLog(taskDTO);
    }
}
