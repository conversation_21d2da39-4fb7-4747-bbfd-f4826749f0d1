package com.nspace.group.scheduler.jobs.logs.offlinelog.cdn;

import com.nspace.group.module.logs.service.offlinelog.dto.TaskDTO;
import com.nspace.group.scheduler.jobs.logs.offlinelog.BaseOfflineJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 寻找可能潜在的合并任务,即之前子批次跑完,但合并失败的任务
 * 同一批次的离线任务可能被划分为多个子批次，需合并其数据库记录和 OSS 文件。
 * 合并策略需根据文件大小区分：
 * - 小于 5MB：无法使用 OSS composeObject；
 * - 大于等于 5MB：可使用 OSS composeObject 实现服务端合并。
 */
@Slf4j
@Component("CDNOfflineLogMergeJob")
public class CDNOfflineLogMergeJob extends BaseOfflineJob {


    @Override
    protected String doExecute(TaskDTO taskDTO) {
        return offlineLogService.dealMergeFilesJob(taskDTO);
    }
}
