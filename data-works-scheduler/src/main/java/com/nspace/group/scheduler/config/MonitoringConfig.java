package com.nspace.group.scheduler.config;

import io.micrometer.core.aop.TimedAspect;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.logging.LoggingMeterRegistry;
import io.micrometer.core.instrument.logging.LoggingRegistryConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 系统监控配置
 */
@Configuration
public class MonitoringConfig {

    /**
     * 日志Registry，提交的meter直接打印到日志中
     *
     * @return LoggingMeterRegistry
     */
    @Bean(name = "loggingMeterRegistry")
    public MeterRegistry loggingMeterRegistry() {
        LoggingMeterRegistry loggingMeterRegistry = LoggingMeterRegistry.builder(LoggingRegistryConfig.DEFAULT).build();
        //添加到全局CompositeMeterRegistry
        Metrics.addRegistry(loggingMeterRegistry);
        return loggingMeterRegistry;
    }

    /**
     * TimedAspect，用于AOP统计方法执行时间
     *
     * @return TimedAspect
     */
    @Bean
    public TimedAspect timedAspect() {
        //使用micrometer全局CompositeMeterRegistry
        return new TimedAspect();
    }
}