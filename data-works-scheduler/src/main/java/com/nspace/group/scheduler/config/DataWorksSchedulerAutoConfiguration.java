package com.nspace.group.scheduler.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 任务调度配置
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/7
 * @time：10:04
 */

@Configuration
@ComponentScan(basePackages = "com.nspace.group.scheduler")
@Slf4j
public class DataWorksSchedulerAutoConfiguration {
    public DataWorksSchedulerAutoConfiguration() {
        log.info("SchedulerConfig 自动配置成功");
    }
}
