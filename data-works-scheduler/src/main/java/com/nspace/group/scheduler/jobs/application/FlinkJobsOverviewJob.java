package com.nspace.group.scheduler.jobs.application;

import com.nspace.group.framework.job.core.handler.JobHandler;
import com.nspace.group.module.application.service.FlinkJobsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

/**
 * flink jobs/overview 数据获取
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2025/1/7
 * @time：20:25
 */
@Slf4j
@Component
public class FlinkJobsOverviewJob implements JobHandler {

    @Resource
    private FlinkJobsService flinkJobsService;

    /**
     * 执行任务
     *
     * @param param 参数
     * @return 结果
     * @throws Exception 异常
     */
    @Override
    public String execute(String param) throws Exception {
        flinkJobsService.saveJobsExecutionRecord();
        return param;
    }
}
