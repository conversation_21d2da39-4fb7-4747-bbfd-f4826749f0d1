package com.nspace.group.scheduler.jobs.fusion.realtime;


import com.nspace.group.framework.common.enums.DataPlatformEnum;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import com.nspace.group.scheduler.service.tencent.TencentSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 实时监控-推流流量带宽/推流质量监控
 *
 * @author：yang<PERSON>ow<PERSON>@gycloud.com
 * @date： 2024/11/7
 * @time：15:15
 */

@Slf4j
@Component
public class FusionRealtimeStreamPushInfoJob extends AbstractFusionJob {

    @Resource
    private TencentSyncService tencentSyncService;

    /**
     * @param account
     * @param startTime
     * @param endTime
     */
    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
        if (DataPlatformEnum.PLATFORM_TENCENT.getCode().equals(account.getPlatform().toUpperCase())) {
            tencentSyncService.monitorRealtimeStreamPushInfo(account, startTime, endTime);
        }
    }
}
