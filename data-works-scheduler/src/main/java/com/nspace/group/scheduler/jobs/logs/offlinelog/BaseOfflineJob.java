package com.nspace.group.scheduler.jobs.logs.offlinelog;

import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.framework.job.core.handler.JobHandler;
import com.nspace.group.module.logs.service.offlinelog.OfflineLogService;
import com.nspace.group.module.logs.service.offlinelog.dto.TaskDTO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public abstract class BaseO<PERSON>line<PERSON><PERSON> implements JobHandler {


    @Resource
    protected OfflineLogService offlineLogService;

    @Override
    public String execute(String param) throws Exception {
        TaskDTO taskDTO = JsonUtils.parseObject(param, TaskDTO.class);
        if (taskDTO == null) {
            throw new RuntimeException("TaskDTO is null");
        }
        return doExecute(taskDTO);
    }

    protected abstract String doExecute(TaskDTO taskDTO);

}
