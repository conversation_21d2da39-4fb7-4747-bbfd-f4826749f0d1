package com.nspace.group.scheduler.jobs.logs.vendor;


import cn.hutool.json.JSONObject;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;
import com.nspace.group.module.logs.biz.vendor.VendorLogRecoveryService;
import com.nspace.group.scheduler.jobs.common.AbstractFusionJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 通用CDN第三方投递异常日志处理任务
 */

@Slf4j
@Component
public class VendorCdnLogRecoveryJob extends AbstractFusionJob {

    @Resource(name = "commonThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource
    private VendorLogRecoveryService logRecoveryService;

    @Override
    protected void processAccount(VendorAccountWithDomainsDTO account, String startTime, String endTime) {
    }

    @Override
    public String execute(String param) throws Exception {
        JSONObject object = JsonUtils.parseObject(param, JSONObject.class);
        Integer limit = null;
        if (object != null) {
            limit = object.getInt("limit");
        }

        limit = limit == null || limit <= 0 ? 10000 : limit;

        Integer finalLimit = limit;
        executorService.execute(() -> {
            log.info("[VendorCdnLogRecoveryJob_execute],before_execute");
            logRecoveryService.recoverLogs(BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), finalLimit);
            log.info("[VendorCdnLogRecoveryJob_execute],finished_execute");
        });
        return "success";
    }
}
