<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.nspace.group</groupId>
        <artifactId>data-works</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>data-works-scheduler</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>

    <properties>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- DB 相关 -->
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-spring-boot-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-spring-boot-starter-job</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>
        <!-- 消息队列相关 -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-spring-boot-starter-banner</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-module-fusion-tencent</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-module-fusion-qiniu</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-module-fusion-ct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-module-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nspace.group</groupId>
            <artifactId>data-module-application</artifactId>
        </dependency>
    </dependencies>
</project>