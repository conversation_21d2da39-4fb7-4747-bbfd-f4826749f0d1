package com.nspace.group.module.application.service.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class FlinkJobResponseDTO {
    @JsonProperty("jobs")
    public List<FlinkJob> jobs;

    @Data
    public static class FlinkJob {
        private String jid;
        private String name;
        @JsonProperty("start-time")
        private long startTime;
        @JsonProperty("end-time")
        private long endTime;
        private long duration;
        private String state;
        @JsonProperty("last-modification")
        private long lastModification;
    }
}