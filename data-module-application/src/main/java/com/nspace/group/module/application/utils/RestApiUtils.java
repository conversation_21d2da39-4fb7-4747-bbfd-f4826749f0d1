package com.nspace.group.module.application.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;


@Component
@Slf4j
public class RestApiUtils {

    private final RestTemplate restTemplate;

    public RestApiUtils(RestTemplateBuilder restTemplateBuilder) {
        this.restTemplate = restTemplateBuilder.build();
    }

    /**
     * 通用的 GET 请求处理方法
     */
    public <T> T fetchData(String url, Class<T> responseType) {
        try {
            T result = restTemplate.getForObject(url, responseType);
            if (result instanceof Iterable) {
                for (Object item : (Iterable<?>) result) {
                    log.info(" - {}", item);
                }
            }
            if (result == null) {
                throw new RuntimeException("Empty response from: " + url);
            }
            return result != null ? result : (T) new ArrayList<>();
        } catch (Exception e) {
            log.error("Fetching data from {} failed: ", url, e);
            throw new RuntimeException("Failed to fetch data.", e);
        }
    }
}
