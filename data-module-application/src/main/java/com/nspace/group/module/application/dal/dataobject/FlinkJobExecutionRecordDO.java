package com.nspace.group.module.application.dal.dataobject;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import java.io.Serializable;
import java.time.LocalDateTime;

@TableName("flink_job_execution_record")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FlinkJobExecutionRecordDO implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String jobId;
    private String jobName;
    private String jobStatus;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}