--
--dbname nspace_log create database nspace_log;
--
DROP TABLE IF EXISTS `live_log_delivery_record`;
CREATE TABLE `live_log_delivery_record` (
   `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
   `domain` VARCHAR(128) NOT NULL COMMENT '域名',
   `biz_type` VARCHAR(32) NOT NULL COMMENT '业务类型,云直播：LSS',
   `log_type` VARCHAR(128) NOT NULL COMMENT '日志类型',
   `log_start_time` DATETIME NOT NULL COMMENT '日志起始时间',
   `log_end_time` DATETIME NOT NULL COMMENT '日志结束时间',
   `log_start_id` BIGINT NOT NULL COMMENT '日志起始ID',
   `log_end_id` BIGINT NOT NULL COMMENT '日志结束ID',
   `retry_count` SMALLINT NOT NULL COMMENT '失败后重试次数，默认值0',
   `delivery_count` BIGINT NOT NULL COMMENT '投递数量',
   `delivery_status` SMALLINT NOT NULL COMMENT '日志投递状态 0:初始值，1:成功，3：重试，-1：过期',
   `delivery_result` VARCHAR(1024) COMMENT '日志投递返回结果',
   `deleted` SMALLINT NOT NULL DEFAULT 0 COMMENT '是否删除 0否1是',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据写入时间',
   `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
)ENGINE=OLAP
UNIQUE KEY(`id`,`tenant_id`, `domain`, `biz_type`,`log_type`,`log_start_time`)
COMMENT '云直播原始日志投递-投递记录表'
PARTITION BY RANGE(`log_start_time`)(
)
DISTRIBUTED BY HASH(`id`,`domain`) BUCKETS 16
PROPERTIES (
   "replication_allocation" = "tag.location.default: 1",
   "enable_unique_key_merge_on_write" = "true",
   "store_row_column" = "true",
   "storage_format" = "V2",
   "compression" = "zstd",
   "compaction_policy" = "time_series",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "dynamic_partition.enable" = "true",
   "dynamic_partition.time_unit" = "DAY",
   "dynamic_partition.start" = "-60",
   "dynamic_partition.end" = "7",
   "dynamic_partition.prefix" = "day",
   "dynamic_partition.buckets" = "16"
);
DROP TABLE IF EXISTS `live_log_delivery_detail`;
CREATE TABLE `live_log_delivery_detail` (
   `log_time` DATETIME NOT NULL COMMENT '日志记录时间',
   `domain` VARCHAR(128) NOT NULL COMMENT '域名',
   `log_id` BIGINT NOT NULL COMMENT '日志ID',
   `status` SMALLINT NOT NULL COMMENT '状态码 1-成功 2-失败，4-延迟数据补发',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据写入时间',
   `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
)ENGINE=OLAP
UNIQUE KEY(`log_time`, `domain`, `log_id`)
COMMENT '云直播原始日志投递-投递明细表'
PARTITION BY RANGE(`log_time`) (
)
DISTRIBUTED BY HASH(`log_time`, `domain`) BUCKETS 16
PROPERTIES (
   "replication_allocation" = "tag.location.default: 1",
   "enable_unique_key_merge_on_write" = "true",
   "store_row_column" = "true",
   "storage_format" = "V2",
   "compression" = "zstd",
   "compaction_policy" = "time_series",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "dynamic_partition.enable" = "true",
   "dynamic_partition.time_unit" = "DAY",
   "dynamic_partition.start" = "-60",
   "dynamic_partition.end" = "7",
   "dynamic_partition.prefix" = "day",
   "dynamic_partition.buckets" = "16"
);
DROP TABLE IF EXISTS `cdn_log_delivery_record`;
CREATE TABLE `cdn_log_delivery_record` (
   `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `tenant_id` BIGINT NOT NULL COMMENT '租户ID',
   `domain` VARCHAR(128) NOT NULL COMMENT '域名',
   `biz_type` VARCHAR(32) NOT NULL COMMENT '业务类型,通用CDN：CDN',
   `log_type` VARCHAR(128) NOT NULL COMMENT '日志类型',
   `log_start_time` DATETIME NOT NULL COMMENT '日志起始时间',
   `log_end_time` DATETIME NOT NULL COMMENT '日志结束时间',
   `log_start_id` BIGINT NOT NULL COMMENT '日志起始ID',
   `log_end_id` BIGINT NOT NULL COMMENT '日志结束ID',
   `retry_count` SMALLINT NOT NULL COMMENT '失败后重试次数，默认值0',
   `delivery_count` BIGINT NOT NULL COMMENT '投递数量',
   `delivery_status` SMALLINT NOT NULL COMMENT '日志投递状态 0:初始值，1:成功，3：重试，-1：过期',
   `delivery_result` VARCHAR(1024) COMMENT '日志投递返回结果',
   `deleted` SMALLINT NOT NULL DEFAULT 0 COMMENT '是否删除 0否1是',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据写入时间',
   `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
)ENGINE=OLAP
UNIQUE KEY(`id`,`tenant_id`, `domain`, `biz_type`,`log_type`,`log_start_time`)
COMMENT '通用CDN原始日志投递-投递记录表'
PARTITION BY RANGE(`log_start_time`) (
)
DISTRIBUTED BY HASH(`id`,`domain`) BUCKETS 16
PROPERTIES (
   "replication_allocation" = "tag.location.default: 1",
   "enable_unique_key_merge_on_write" = "true",
   "store_row_column" = "true",
   "storage_format" = "V2",
   "compression" = "zstd",
   "compaction_policy" = "time_series",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "dynamic_partition.enable" = "true",
   "dynamic_partition.time_unit" = "DAY",
   "dynamic_partition.start" = "-60",
   "dynamic_partition.end" = "7",
   "dynamic_partition.prefix" = "day",
   "dynamic_partition.buckets" = "16"
);
DROP TABLE IF EXISTS `cdn_log_delivery_detail`;
CREATE TABLE `cdn_log_delivery_detail` (
   `log_time` DATETIME NOT NULL COMMENT '日志记录时间',
   `domain` VARCHAR(128) NOT NULL COMMENT '域名',
   `log_id` BIGINT NOT NULL COMMENT '日志ID',
   `status` SMALLINT NOT NULL COMMENT '状态码 0-成功 1-失败，4-延迟日志补发',
   `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据写入时间',
   `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间'
)ENGINE=OLAP
UNIQUE KEY(`log_time`, `domain`, `log_id`)
COMMENT '通用CDN原始日志投递-投递明细表'
PARTITION BY RANGE(`log_time`) (
)
DISTRIBUTED BY HASH(`log_time`, `domain`) BUCKETS 16
PROPERTIES (
   "replication_allocation" = "tag.location.default: 1",
   "enable_unique_key_merge_on_write" = "true",
   "store_row_column" = "true",
   "storage_format" = "V2",
   "compression" = "zstd",
   "compaction_policy" = "time_series",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "dynamic_partition.enable" = "true",
   "dynamic_partition.time_unit" = "DAY",
   "dynamic_partition.start" = "-60",
   "dynamic_partition.end" = "7",
   "dynamic_partition.prefix" = "day",
   "dynamic_partition.buckets" = "16"
);
DROP TABLE IF EXISTS `vendor_live_log_detail`;
CREATE TABLE `vendor_live_log_detail` (
    `id`            BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `log_time`      DATETIME NOT NULL COMMENT '日志记录时间',
    `log_json`      JSON     NOT NULL COMMENT '日志对应的json数据信息',
    `log_status`    SMALLINT NOT NULL DEFAULT '2' COMMENT '日志状态',
    `cur_timestamp` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据写入时间'
) ENGINE = OLAP
UNIQUE KEY(`id`, `log_time`)
COMMENT '第三方直播日志投递给耕耘-写入kafka失败日志处理'
PARTITION BY RANGE(`log_time`) (
)
DISTRIBUTED BY HASH(`id`) BUCKETS 8
PROPERTIES (
   "replication_allocation" = "tag.location.default: 2",
   "enable_unique_key_merge_on_write" = "true",
   "store_row_column" = "true",
   "storage_format" = "V2",
   "compression" = "zstd",
   "compaction_policy" = "time_series",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "dynamic_partition.enable" = "true",
   "dynamic_partition.time_unit" = "DAY",
   "dynamic_partition.start" = "-30",
   "dynamic_partition.end" = "7",
   "dynamic_partition.prefix" = "day"
);
DROP TABLE IF EXISTS `vendor_cdn_log_detail`;
CREATE TABLE `vendor_cdn_log_detail` (
    `id`            BIGINT   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `log_time`      DATETIME NOT NULL COMMENT '日志记录时间',
    `log_json`      JSON     NOT NULL COMMENT '日志对应的json数据信息',
    `log_status`    SMALLINT NOT NULL DEFAULT '2' COMMENT '日志状态',
    `cur_timestamp` datetime          DEFAULT CURRENT_TIMESTAMP COMMENT '数据写入时间'
) ENGINE = OLAP
UNIQUE KEY(`id`, `log_time`)
COMMENT '第三方CDN日志投递给耕耘-写入kafka失败日志处'
PARTITION BY RANGE(`log_time`) (
)
DISTRIBUTED BY HASH(`id`) BUCKETS 8
PROPERTIES (
   "replication_allocation" = "tag.location.default: 2",
   "enable_unique_key_merge_on_write" = "true",
   "store_row_column" = "true",
   "storage_format" = "V2",
   "compression" = "zstd",
   "compaction_policy" = "time_series",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "dynamic_partition.enable" = "true",
   "dynamic_partition.time_unit" = "DAY",
   "dynamic_partition.start" = "-30",
   "dynamic_partition.end" = "7",
   "dynamic_partition.prefix" = "day"
);

DROP TABLE IF EXISTS `delivery_cdn_log_fail_detail`;
CREATE TABLE `delivery_cdn_log_fail_detail` (
    `delivery_vendor`    VARCHAR(128) NOT NULL COMMENT '日志投递第三方编号',
    `log_time`      DATETIME NOT NULL COMMENT '日志记录时间',
    `id`            BIGINT   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `log_json`      JSON     NOT NULL COMMENT '日志对应的json数据信息',
    `log_status`    SMALLINT NOT NULL DEFAULT '2' COMMENT '日志状态 0-是被 1-成功',
    `delivery_times`    SMALLINT NOT NULL DEFAULT '2' COMMENT '投递次数',
    `delivery_timestamp` datetime          DEFAULT CURRENT_TIMESTAMP COMMENT '最近一次投递时间',
    `cur_timestamp` datetime          DEFAULT CURRENT_TIMESTAMP COMMENT '数据写入时间'
) ENGINE = OLAP
UNIQUE KEY(`delivery_vendor`,`log_time`,`id`)
COMMENT '耕耘CDN日志投递给第三方-投递失败日志详情'
PARTITION BY RANGE(`log_time`) (
)
DISTRIBUTED BY HASH(`id`) BUCKETS 8
PROPERTIES (
   "replication_allocation" = "tag.location.default: 2",
   "enable_unique_key_merge_on_write" = "true",
   "store_row_column" = "true",
   "storage_format" = "V2",
   "compression" = "zstd",
   "compaction_policy" = "time_series",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "dynamic_partition.enable" = "true",
   "dynamic_partition.time_unit" = "DAY",
   "dynamic_partition.start" = "-30",
   "dynamic_partition.end" = "7",
   "dynamic_partition.prefix" = "day"
);

DROP TABLE IF EXISTS `delivery_live_log_fail_detail`;
CREATE TABLE `delivery_live_log_fail_detail` (
    `delivery_vendor`    VARCHAR(128) NOT NULL COMMENT '日志投递第三方编号',
    `log_time`      DATETIME NOT NULL COMMENT '日志记录时间',
    `id`            BIGINT   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `log_json`      JSON     NOT NULL COMMENT '日志对应的json数据信息',
    `log_status`    SMALLINT NOT NULL DEFAULT '2' COMMENT '日志状态 0-是被 1-成功',
    `delivery_times`    SMALLINT NOT NULL DEFAULT '2' COMMENT '投递次数',
    `delivery_timestamp` datetime          DEFAULT CURRENT_TIMESTAMP COMMENT '最近一次投递时间',
    `cur_timestamp` datetime          DEFAULT CURRENT_TIMESTAMP COMMENT '数据写入时间'
) ENGINE = OLAP
UNIQUE KEY(`delivery_vendor`,`log_time`,`id`)
COMMENT '耕耘直播日志投递给第三方-投递失败日志详情'
PARTITION BY RANGE(`log_time`) (
)
DISTRIBUTED BY HASH(`id`) BUCKETS 8
PROPERTIES (
   "replication_allocation" = "tag.location.default: 2",
   "enable_unique_key_merge_on_write" = "true",
   "store_row_column" = "true",
   "storage_format" = "V2",
   "compression" = "zstd",
   "compaction_policy" = "time_series",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "dynamic_partition.enable" = "true",
   "dynamic_partition.time_unit" = "DAY",
   "dynamic_partition.start" = "-30",
   "dynamic_partition.end" = "7",
   "dynamic_partition.prefix" = "day"
);