package com.nspace.group.module.logs.utils;

import cn.hutool.core.io.unit.DataSizeUtil;
import com.nspace.group.module.logs.service.offlinelog.dto.FileMeta;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

@Slf4j
public class GzipUtils {


    private static final int BUFFER_SIZE = 256 * 1024;

    /**
     * 将文件按照从此到大的顺序排序,以 500MB 的大小分成一组
     *
     * @param metas 带合并的列表
     * @return 分组后的列表
     */
    public static List<List<FileMeta>> groupFiles(List<FileMeta> metas, Long fileSize) {
        List<FileMeta> fileMetas = metas.stream()
                .sorted((a, b) -> Long.compare(b.getFileSize(), a.getFileSize())) // 大文件优先
                .collect(Collectors.toList());
        List<List<FileMeta>> result = new ArrayList<>();
        List<Long> groupSizes = new ArrayList<>();
        for (FileMeta fileMeta : fileMetas) {
            boolean additional = false;
            for (int i = 0; i < result.size(); i++) {
                if (groupSizes.get(i) + fileMeta.getFileSize() <= fileSize) {
                    result.get(i).add(fileMeta);
                    groupSizes.set(i, groupSizes.get(i) + fileMeta.getFileSize());
                    additional = true;
                    break;
                }
            }
            if (!additional) {
                List<FileMeta> newGroup = new ArrayList<>();
                newGroup.add(fileMeta);
                result.add(newGroup);
                groupSizes.add(fileMeta.getFileSize());
            }
        }
        return result;
    }

    @SneakyThrows
    public static FileMeta merge(List<FileMeta> groupedFiles, String fileName, BiConsumer<String, List<String>> consumer) {
        FileMeta metadata = new FileMeta();
        List<String> files = groupedFiles.stream()
                .map(FileMeta::getLogName).collect(Collectors.toList());
        consumer.accept(fileName, files);
        int rowCount = groupedFiles.stream().mapToInt(FileMeta::getRows).sum();
        metadata.setRows(rowCount);
        return metadata;
    }

    @SneakyThrows
    public static void compressGzip(FileMeta meta, String command) {
        long startTime = System.currentTimeMillis();
        String fileName = meta.getLogName();
        long originSize = FileUtils.sizeOf(new File(fileName));
        String gzipFileName = fileName + ".gz";
        ProcessBuilder process = new ProcessBuilder(command, "-1", fileName);
        process.inheritIO();
        Process proc = process.start();
        int code = proc.waitFor();
        if (code != 0) {
            throw new IOException(command + " 失败, exit=" + code);
        }
        long size = FileUtils.sizeOf(new File(gzipFileName));
        log.debug("Compress file {} in {} ms,originSize:{} size :{}", fileName, System.currentTimeMillis() - startTime
                , DataSizeUtil.format(originSize), DataSizeUtil.format(size));
        meta.setFileSize(size);
        meta.setLogName(gzipFileName);
    }


    /**
     * @param fileName 压缩后的文件名
     * @param files    参与压缩的文件列表
     */
    @SneakyThrows
    public static void mergeGzip(String fileName, List<String> files) {
        try (FileOutputStream fos = new FileOutputStream(fileName);
             BufferedOutputStream bos = new BufferedOutputStream(fos, BUFFER_SIZE)) {
            for (String file : files) {
                try (InputStream fis = new FileInputStream(file);
                     BufferedInputStream bis = new BufferedInputStream(fis, BUFFER_SIZE)) {
                    IOUtils.copy(bis, bos);
                }
            }
        }
    }

}
