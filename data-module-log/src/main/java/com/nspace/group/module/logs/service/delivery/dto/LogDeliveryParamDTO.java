package com.nspace.group.module.logs.service.delivery.dto;


import java.util.StringJoiner;

/**
 * LogDeliveryParamDTO
 *
 * <AUTHOR>
 */
public class LogDeliveryParamDTO {

    /**
     * 多租户编号
     */
    private final Long tenantId;

    /**
     * 用户侧域名
     */
    private final String domain;

    /**
     * 业务类型,云直播：LSS 通用CDN：CDN
     */
    private final String bizType;

    /**
     * 日志类型 云直播：PUSH、PULL 通用CDN：GENERAL
     */
    private final String type;

    /**
     * 投递目标 示例：JD
     * @see com.nspace.group.module.logs.enums.LogDeliveryTargetEnum
     */
    private final String targetType;

    /**
     * 单次投递限制
     */
    private final Integer limit;

    /**
     * 批量查询限制
     */
    private final Integer batchLimit;

    /**
     * 日志延迟偏移量（秒）
     */
    private final Integer delayOffset;


    /**
     * 双方约定的唯一标识
     */
    private final String privateKey;

    /**
     * ak
     */
    private final String accessKey;

    /**
     * 接口path
     */
    private final String apiPath;

    /**
     * 接口基础Url
     */
    private final String apiBaseUrl;

    //------阿里云日志投递相关------
    /**
     * 日志投递项目
     */
    private final String project;

    /**
     * 日志投递日志库
     */
    private final String logStore;

    public LogDeliveryParamDTO(Long tenantId, String domain, String bizType, String type, String targetType, Integer limit, Integer batchLimit, Integer delayOffset, String privateKey, String accessKey, String apiPath, String apiBaseUrl, String project, String logStore) {
        this.tenantId = tenantId;
        this.domain = domain;
        this.bizType = bizType;
        this.type = type;
        this.targetType = targetType;
        this.limit = limit != null && limit > 0 ? limit : 5000;
        this.batchLimit = batchLimit != null && batchLimit > 0 ? batchLimit : 10000;
        this.delayOffset = delayOffset != null && delayOffset > 0 ? delayOffset : 60;
        this.project = project;
        this.logStore = logStore;
        if (this.limit > this.batchLimit) {
            throw new RuntimeException("limit can't be greater than batchLimit");
        }
        this.privateKey = privateKey;
        this.accessKey = accessKey;
        this.apiPath = apiPath;
        this.apiBaseUrl = apiBaseUrl;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public String getDomain() {
        return domain;
    }

    public String getBizType() {
        return bizType;
    }

    public String getType() {
        return type;
    }

    public String getTargetType() {
        return targetType;
    }

    public Integer getLimit() {
        return limit;
    }

    public Integer getBatchLimit() {
        return batchLimit;
    }

    public Integer getDelayOffset() {
        return delayOffset;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public String getApiPath() {
        return apiPath;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public String getProject() {
        return project;
    }

    public String getLogStore() {
        return logStore;
    }

    @Override
    public String toString() {
        return new StringJoiner(",")
                .add("target=" + targetType)
                .add("delay=" + delayOffset)
                .add("tenantId=" + tenantId)
                .add("domain=" + domain)
                .add("bizType=" + bizType)
                .add("logType=" + type)
                .toString();
    }
}
