package com.nspace.group.module.logs.utils;

public class EscapeUtils {

    /**
     * 对单个字段进行 CSV 转义
     */
    public static String escapeCSV(String field) {
        if (field == null) {
            return "";
        }
        boolean containsSpecial = field.contains(",") || field.contains("\"");
        String escaped = field.replace("\"", "\"\""); // 转义双引号
        if (containsSpecial) {
            escaped = "\"" + escaped + "\""; // 包裹引号
        }
        return escaped;
    }
}
