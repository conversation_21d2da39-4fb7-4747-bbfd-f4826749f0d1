package com.nspace.group.module.logs.service.vendor.dto;


import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :VendorLogDetailDTO.java, v0.1 2025年05月12日 10:58 zhangxin Exp
 */
public class VendorLogDetailDTO {

    private Long id;

    /**
     * log日志时间
     */
    private LocalDateTime logTime;

    /**
     * 日志对应的json数据信息
     */
    private String logJson;

    /**
     * 日志接收状态 1:成功 2:失败
     */
    private Integer logStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getLogTime() {
        return logTime;
    }

    public void setLogTime(LocalDateTime logTime) {
        this.logTime = logTime;
    }

    public String getLogJson() {
        return logJson;
    }

    public void setLogJson(String logJson) {
        this.logJson = logJson;
    }

    public Integer getLogStatus() {
        return logStatus;
    }

    public void setLogStatus(Integer logStatus) {
        this.logStatus = logStatus;
    }

}
