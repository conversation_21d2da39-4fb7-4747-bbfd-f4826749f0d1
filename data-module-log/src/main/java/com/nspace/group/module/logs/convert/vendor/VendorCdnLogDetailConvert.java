package com.nspace.group.module.logs.convert.vendor;

import com.nspace.group.module.logs.dal.dataobject.vendor.VendorCdnLogDetailDO;
import com.nspace.group.module.logs.service.vendor.dto.VendorLogDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version :VendorCdnLogDetailConvert.java, v0.1 2025年05月12日 10:21 zhangxin Exp
 */
@Mapper
public interface VendorCdnLogDetailConvert {

    VendorCdnLogDetailConvert INSTANCE = Mappers.getMapper(VendorCdnLogDetailConvert.class);

    VendorLogDetailDTO getVendorLogDetailDTO(VendorCdnLogDetailDO record);

    List<VendorLogDetailDTO> getVendorLogDetailDTOList(List<VendorCdnLogDetailDO> records);
}
