package com.nspace.group.module.logs.convert.delivery;

import com.nspace.group.module.logs.dal.dataobject.delivery.DeliveryCdnLogDetailDO;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version :DeliveryCdnLogDetailConvert.java, v0.1 2025年05月12日 10:21 zhangxin Exp
 */
@Mapper
public interface DeliveryCdnLogDetailConvert {

    DeliveryCdnLogDetailConvert INSTANCE = Mappers.getMapper(DeliveryCdnLogDetailConvert.class);

    DeliveryLogDetailDTO getDeliveryLogDetailDTO(DeliveryCdnLogDetailDO record);

    List<DeliveryLogDetailDTO> getDeliveryLogDetailDTOList(List<DeliveryCdnLogDetailDO> records);
}
