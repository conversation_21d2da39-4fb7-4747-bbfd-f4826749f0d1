package com.nspace.group.module.logs.convert.delivery;

import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :LogDeliveryDetailConvert.java, v0.1 2025年04月10日 10:21 zhangxin Exp
 */
@Mapper
public interface LiveLogDeliveryDetailConvert {

    LiveLogDeliveryDetailConvert INSTANCE = Mappers.getMapper(LiveLogDeliveryDetailConvert.class);

    @Mapping(target = "createTime", ignore = true)
    LogDeliveryDetailDTO newDetailDTO(String domain, Long logId, LocalDateTime logTime, Integer status);

}
