package com.nspace.group.module.logs.biz.delivery.context;

import cn.hutool.core.lang.Assert;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;

import java.util.List;

public class JdLiveRecoveryContext extends LogContext {

    // 接口path
    private final String apiPath;

    // 接口基础Url
    private final String apiBaseUrl;

    //当前重试次数
    private Integer curRetryCount;

    // 当前日志条数
    private List<DeliveryLogDetailDTO> logDetails;

    public JdLiveRecoveryContext(String targetType, Integer logLimit, Integer batchLimit, Integer retryCount,
                                 String privateKey, String accessKey, String apiPath, String apiBaseUrl) {
        super(targetType, logLimit, batchLimit, retryCount, privateKey, accessKey);
        Assert.notBlank(apiPath);
        Assert.notBlank(apiBaseUrl);
        this.apiPath = apiPath;
        this.apiBaseUrl = apiBaseUrl;
    }

    public List<DeliveryLogDetailDTO> getLogDetails() {
        return logDetails;
    }

    public void setLogDetails(List<DeliveryLogDetailDTO> logDetails) {
        this.logDetails = logDetails;
    }

    public Integer getCurRetryCount() {
        return curRetryCount;
    }

    public void setCurRetryCount(Integer curRetryCount) {
        this.curRetryCount = curRetryCount;
    }

    public String getApiPath() {
        return apiPath;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

}
