package com.nspace.group.module.logs.utils;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class Constant {

    public static final String ISO8601_FORMAT = "CONCAT(DATE_FORMAT(%s, '%%Y-%%m-%%dT%%H:%%i:%%s'), '+08:00')";

    public static final String[] LIVE_FIELDS = new String[]{
            "request_id", String.format(ISO8601_FORMAT, "log_time"), "duration", "stream_protocol", "duration", "total_duration", "client_addr",
            "server_addr", "scheme", "http_method", "domain", "request_uri", "status", "bytes_sent", "interval_bytes_sent",
            "body_bytes_sent", "interval_bytes_recv", "bytes_recv", String.format(ISO8601_FORMAT, "connect_time"), "first_byte_recv_time", "server_protocol",
            "request_methods", "referer", "user_agent", "err", "discontinuous_count",
            "discontinuous_time", "round", "end"
    };

    public static final Integer LIVE_FIELDS_SIZE = LIVE_FIELDS.length - 2;

    public static final Integer LIVE_ROUND_INDEX = LIVE_FIELDS.length - 1;

    public static final Integer LIVE_END_INDEX = LIVE_FIELDS.length;

    public static final Set<Integer> LIVE_NUMBER_FIELDS = Set.of(2, 4, 5, 12, 13, 14, 15, 16, 17, 19, 25, 26, 27, 28);


    private static final String[] CDN_FIELDS = new String[]{
            String.format(ISO8601_FORMAT, "log_time"), "request_id", "client_addr", "server_addr", "http_method", "scheme",
            "domain", "request_uri", "uri_param", "server_protocol", "status", "cache_status", "http_referer",
            "http_response_range", "content_type", "response_size", "body_bytes_sent", "cache_level",
            "user_agent", "http_request_range", "sent_http_location", "sent_http_content_length", "remote_ip",
            "last_modified", "http_content_encoding", "http_accept", "quic"
    };


    public static final Integer CDN_FIELDS_SIZE = CDN_FIELDS.length;


    public static final Set<Integer> CDN_NUMBER_FIELDS = Set.of(15, 16, 21);


    public static final String LIVE_SELECT_FIELDS = String.join(",", LIVE_FIELDS);

    public static final String CDN_SELECT_FIELDS = String.join(",", CDN_FIELDS);




    public static final String SUCCESS = "SUCCESS";


    public static final String GZIP = "application/x-gzip";

    public static final Map<String, String> EXPIRE_TAGS = Map.of("expire", "2d");

}
