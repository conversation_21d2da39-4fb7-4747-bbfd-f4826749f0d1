package com.nspace.group.module.logs.convert.delivery;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.logs.client.model.JdLiveLogDeliveryResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version :LiveCdnLogConvert.java, v0.1 2025年03月19日 10:21 zhangxin Exp
 */
@Mapper
public interface LiveCdnLogConvert {

    LiveCdnLogConvert INSTANCE = Mappers.getMapper(LiveCdnLogConvert.class);
    JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();


    @Mapping(target = "respMsg", source = "message")
    @Mapping(target = "respHeaders", source = "responseHeaders")
    @Mapping(target = "respCode", source = "code")
    @Mapping(target = "respBody", source = "responseBody")
    @Mapping(target = "msg", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "requestId", ignore = true)
    JdLiveLogDeliveryResult getErrorLogDeliveryResult(ApiException e);

    @Named("strToMapListConvert")
    default List<Map<String, Object>> getLogMapList(String logJsonArr) throws JsonProcessingException {
        TypeFactory typeFactory = jsonMapper.getTypeFactory();
        TypeReference<HashMap<String, Object>> typeRef = new TypeReference<>() {};
        JavaType javaType = typeFactory.constructType(typeRef);
        List<Map<String, Object>> logMapList = jsonMapper.readValue(logJsonArr, typeFactory.constructCollectionType(List.class, javaType));
        return logMapList.stream().map(logMap -> {
            Map<String, Object> newLogMap = new HashMap<>(logMap.size());
            logMap.forEach((k, v) -> newLogMap.put(toSnakeCase(k), v));
            return newLogMap;
        }).collect(Collectors.toList());
    }

    @Named("snakeCaseConvert")
    default String toSnakeCase(String camelCaseKey) {
        if (StrUtil.isBlank(camelCaseKey)) return camelCaseKey;
        StringBuilder builder = new StringBuilder(camelCaseKey.length());
        char c = camelCaseKey.charAt(0);
        builder.append(Character.toLowerCase(c));
        for (int i = 1; i < camelCaseKey.length(); i++) {
            char ch = camelCaseKey.charAt(i);
            if (Character.isUpperCase(ch)) {
                builder.append(StringPool.UNDERSCORE).append(Character.toLowerCase(ch));
            } else {
                builder.append(ch);
            }
        }
        return builder.toString();
    }
}
