package com.nspace.group.module.logs.service.offlinelog.live;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.LiveOfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.LiveOfflineLogInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.LiveOfflineLogDetailInfoMapper;
import com.nspace.group.module.logs.dal.mapper.offlinelog.LiveOfflineLogInfoMapper;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import com.nspace.group.module.logs.service.offlinelog.OfflinePersistService;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;


@Service("live-offline-persist")
public class LiveOfflineLogPersistServiceImpl implements OfflinePersistService {

    @Resource
    private LiveOfflineLogInfoMapper liveOfflineLogMapper;

    @Resource
    private LiveOfflineLogDetailInfoMapper detailInfoMapper;

    @Override
    public void persist(OfflineLogInfoDO logDO) {
        if (logDO instanceof OfflineLogDetailInfoDO) {
            LiveOfflineLogDetailInfoDO detailDO = new LiveOfflineLogDetailInfoDO();
            BeanUtils.copyProperties(logDO, detailDO);
            if (logDO.getId() == null) {
                detailInfoMapper.insert(detailDO);
            } else {
                detailInfoMapper.updateById(detailDO);
            }
            logDO.setId(detailDO.getId());
        } else if (logDO != null) {
            LiveOfflineLogInfoDO infoDO = new LiveOfflineLogInfoDO();
            BeanUtils.copyProperties(logDO, infoDO);
            if (logDO.getId() == null) {
                liveOfflineLogMapper.insert(infoDO);
            } else {
                liveOfflineLogMapper.updateById(infoDO);
            }
            logDO.setId(infoDO.getId());
        }

    }

    @Override
    public void batchUpdate(OfflineLogDTO dto, String message, OfflineStatusEnum status, List<Long> detailIds) {
        LambdaUpdateWrapper<LiveOfflineLogInfoDO> wrapper = new UpdateWrapper<LiveOfflineLogInfoDO>().lambda()
                .set(LiveOfflineLogInfoDO::getStatus, status)
                .set(LiveOfflineLogInfoDO::getMessage, message)
                .set(LiveOfflineLogInfoDO::getUpdateTime, LocalDateTime.now())
                .in(LiveOfflineLogInfoDO::getId, dto.getCurrentIds());
        liveOfflineLogMapper.update(wrapper);
        List<Long> historyIds = dto.getHistoryIds();
        if (dto.isRemoveHistory() && CollectionUtil.isNotEmpty(historyIds)) {
            liveOfflineLogMapper.deleteBatchIds(historyIds);
        }
        if (CollectionUtil.isNotEmpty(detailIds)) {
            if (status == OfflineStatusEnum.FAILED) {
                LambdaUpdateWrapper<LiveOfflineLogDetailInfoDO> detailWrapper = Wrappers
                        .lambdaUpdate(LiveOfflineLogDetailInfoDO.class)
                        .in(LiveOfflineLogDetailInfoDO::getId, detailIds);
                detailWrapper.setSql("retry_count = retry_count + 1");
                detailInfoMapper.update(detailWrapper);
            } else if (status == OfflineStatusEnum.SUCCESS) {
                detailInfoMapper.deleteBatchIds(detailIds);
            }
        }
    }

    @Override
    public void batchUpdateDetails(OfflineLogDTO dto, String message, OfflineStatusEnum status) {
        List<Long> historyIds = dto.getHistoryIds();
        if (dto.isRemoveHistory() && CollectionUtil.isNotEmpty(historyIds)) {
            detailInfoMapper.deleteBatchIds(historyIds);
        }
        LambdaUpdateWrapper<LiveOfflineLogDetailInfoDO> wrapper = new UpdateWrapper<LiveOfflineLogDetailInfoDO>().lambda()
                .set(LiveOfflineLogDetailInfoDO::getStatus, status)
                .set(LiveOfflineLogDetailInfoDO::getMessage, message)
                .set(LiveOfflineLogDetailInfoDO::getUpdateTime, LocalDateTime.now())
                .in(LiveOfflineLogDetailInfoDO::getId, dto.getCurrentIds());
        detailInfoMapper.update(wrapper);
        liveOfflineLogMapper.recoverDetails(dto.getCurrentIds());
    }

    public List<Long> find(String batchNum, Long tenantId, String domain) {
        LambdaQueryWrapper<LiveOfflineLogInfoDO> wrapper = Wrappers.lambdaQuery(LiveOfflineLogInfoDO.class)
                .eq(LiveOfflineLogInfoDO::getBatchNum, batchNum)
                .eq(LiveOfflineLogInfoDO::getTenantId, tenantId)
                .eq(LiveOfflineLogInfoDO::getDomain, domain);
        return liveOfflineLogMapper.selectList(wrapper)
                .stream().map(OfflineLogInfoDO::getId).collect(Collectors.toList());
    }

    @Override
    public List<? extends OfflineLogDetailInfoDO> findDetails(String batchNum, Long tenantId, String domain) {
        LambdaQueryWrapper<LiveOfflineLogDetailInfoDO> wrapper = Wrappers.lambdaQuery(LiveOfflineLogDetailInfoDO.class)
                .eq(LiveOfflineLogDetailInfoDO::getBatchNum, batchNum)
                .eq(LiveOfflineLogDetailInfoDO::getTenantId, tenantId)
                .eq(LiveOfflineLogDetailInfoDO::getDomain, domain);
        return detailInfoMapper.selectList(wrapper);
    }

    @Override
    public int cleanExpiredLogs() {
        LocalDateTime startOfDay = LocalDateTime.now().with(LocalTime.MIN);
        QueryWrapper<LiveOfflineLogInfoDO> wrapper = Wrappers.query(LiveOfflineLogInfoDO.class)
                .isNotNull("lifecycle")
                .apply("create_time + CAST(lifecycle || ' days' AS interval) < {0}", startOfDay);
        return liveOfflineLogMapper.delete(wrapper);
    }


}
