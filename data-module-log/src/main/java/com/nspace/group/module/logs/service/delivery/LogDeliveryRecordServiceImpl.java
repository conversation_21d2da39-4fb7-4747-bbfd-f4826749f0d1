package com.nspace.group.module.logs.service.delivery;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.dal.handler.StreamLoadHandler;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 日志投递记录接口实现类
 *
 * <AUTHOR>
 * @since 2025-03-18 14:32:16
 */
@Service
@Slf4j
public class LogDeliveryRecordServiceImpl implements LogDeliveryRecordService {

    private final JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    @Resource(name = "nspaceLogStreamLoader")
    private StreamLoadHandler streamLoadHandler;

    @Resource
    private LiveLogDeliveryRecordService liveLogDeliveryRecordService;

    @Resource
    private CdnLogDeliveryRecordService cdnLogDeliveryRecordService;

    @Override
    public void save(LogDeliveryRecordDTO deliveryRecord) {
        String bizType = deliveryRecord.getBizType();
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            streamLoadData("live_log_delivery_record", Collections.singletonList(deliveryRecord));
            liveLogDeliveryRecordService.fillRecordId(deliveryRecord);
            return;
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            streamLoadData("cdn_log_delivery_record", Collections.singletonList(deliveryRecord));
            cdnLogDeliveryRecordService.fillRecordId(deliveryRecord);
            return;
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }

    private void streamLoadData(String tableName, List<LogDeliveryRecordDTO> deliveryRecords) {
        try {
            Map<String, String> loadResultMap = streamLoadHandler.sendData(tableName, jsonMapper.writeValueAsString(deliveryRecords));
            if ("Success".equals(loadResultMap.get("Status"))) {
                log.info("stream_load_success,NumberLoadedRows={},LoadTimeMs={}", loadResultMap.get("NumberLoadedRows"), loadResultMap.get("LoadTimeMs"));
            } else {
                log.error("stream_load_failed,Message={},ErrorURL={}", loadResultMap.get("Message"), loadResultMap.get("ErrorURL"));
                throw new RuntimeException("stream_load_failed");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

