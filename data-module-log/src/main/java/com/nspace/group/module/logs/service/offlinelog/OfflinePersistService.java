package com.nspace.group.module.logs.service.offlinelog;

import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;

import java.util.List;

public interface OfflinePersistService {


    void persist(OfflineLogInfoDO logDO);


    /**
     * 批量更新任务表
     *
     * @param dto       日志列表 的 id
     * @param message   错误信息
     * @param status    状态
     * @param detailIds 子任务 id 列表
     */
    void batchUpdate(OfflineLogDTO dto, String message, OfflineStatusEnum status, List<Long> detailIds);


    /**
     * 批量更新子任务表
     *
     * @param dto
     * @param message
     * @param status
     */
    void batchUpdateDetails(OfflineLogDTO dto, String message, OfflineStatusEnum status);

    /**
     * 获取历史任务批次
     *
     * @param batchNum
     * @param tenantId
     * @param domain
     * @return
     */
    List<Long> find(String batchNum, Long tenantId, String domain);


    /**
     * 获取批次详情
     *
     * @param batchNum
     * @param tenantId
     * @param domain
     * @return
     */
    List<? extends OfflineLogDetailInfoDO> findDetails(String batchNum, Long tenantId, String domain);

    /**
     * 移除过期的记录
     *
     * @return 移除的行数
     */
    int cleanExpiredLogs();
}
