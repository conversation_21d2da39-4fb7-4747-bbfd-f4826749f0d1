package com.nspace.group.module.logs.enums;

import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum VendorLogTypeEnum {
    PUSH("push", BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), "ods_emu_pub_vendor"),
    PULL("pull", BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), "ods_emu_play_vendor"),
    GENERAL("general", BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), "ods_general_cdn_log_vendor");

    /**
     * 类型
     */
    private final String type;

    /**
     * 业务类型
     */
    private final String bizType;

    /**
     * 目标topic
     */
    private final String topic;

    public boolean isSelf(String logType) {
        return isVendorLogType(logType) && this.getType().equals(logType);
    }

    public static boolean isVendorLogType(String logType) {
        return Arrays.stream(values()).map(VendorLogTypeEnum::getType).anyMatch(value -> value.equals(logType));
    }
}
