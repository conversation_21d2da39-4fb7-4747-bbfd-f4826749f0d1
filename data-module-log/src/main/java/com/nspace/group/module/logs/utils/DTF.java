package com.nspace.group.module.logs.utils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;

public class DTF {

    public static final DateTimeFormatter yyyyMMddHHmmss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter yyyyMMddHH = DateTimeFormatter.ofPattern("yyyyMMddHH");

    public static final LocalDateTime MIN = LocalDateTime.ofEpochSecond(0, 0, ZoneOffset.of("+08:00"));
}
