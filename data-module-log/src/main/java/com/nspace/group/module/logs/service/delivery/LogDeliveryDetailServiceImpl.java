package com.nspace.group.module.logs.service.delivery;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.module.logs.convert.delivery.LiveLogDeliveryDetailConvert;
import com.nspace.group.module.logs.dal.handler.StreamLoadHandler;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 日志投递详情接口实现类
 *
 * <AUTHOR>
 * @since 2025-04-10 14:32:16
 */
@Service
@Slf4j
public class LogDeliveryDetailServiceImpl implements LogDeliveryDetailService {

    private final JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS)
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    @Resource(name = "nspaceLogStreamLoader")
    private StreamLoadHandler streamLoadHandler;

    @Override
    public void saveMany(String bizType, String domain, Integer deliveryStatus, Map<Long, LocalDateTime> logIdTimeMap) {
        if (CollectionUtil.isEmpty(logIdTimeMap)) return;
        List<LogDeliveryDetailDTO> deliveryDetails = logIdTimeMap.entrySet().stream()
                .map(entry -> LiveLogDeliveryDetailConvert.INSTANCE.newDetailDTO(domain, entry.getKey(), entry.getValue(), deliveryStatus))
                .collect(Collectors.toList());
        if (BusinessTypeEnum.BUSINESS_TYPE_LSS.isSelf(bizType)) {
            streamLoadData("live_log_delivery_detail", deliveryDetails);
            return;
        } else if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) {
            streamLoadData("cdn_log_delivery_detail", deliveryDetails);
            return;
        }
        throw new RuntimeException("unsupported biz_type=" + bizType);
    }

    private void streamLoadData(String tableName, List<LogDeliveryDetailDTO> deliveryDetails) {
        try {
            Map<String, String> loadResultMap = streamLoadHandler.sendData(tableName, jsonMapper.writeValueAsString(deliveryDetails));
            if ("Success".equals(loadResultMap.get("Status"))) {
                log.info("stream_load_success,NumberLoadedRows={},LoadTimeMs={}", loadResultMap.get("NumberLoadedRows"), loadResultMap.get("LoadTimeMs"));
            } else {
                log.error("stream_load_failed,Message={},ErrorURL={}", loadResultMap.get("Message"), loadResultMap.get("ErrorURL"));
                throw new RuntimeException("stream_load_failed");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
