package com.nspace.group.module.logs.dal.dataobject.vendor;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :VendorCdnLogDetailDO.java, v0.1 2025年05月12日 10:58 zhangxin Exp
 */
@TableName(value = "vendor_cdn_log_detail")
public class VendorCdnLogDetailDO {

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * log日志时间
     */
    private LocalDateTime logTime;

    /**
     * 日志对应的json数据信息
     */
    private String logJson;

    /**
     * 日志接收状态 1:成功 2:失败
     */
    private Integer logStatus;

    /**
     * 数据写入时间
     */
    private LocalDateTime curTimestamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getLogTime() {
        return logTime;
    }

    public void setLogTime(LocalDateTime logTime) {
        this.logTime = logTime;
    }

    public String getLogJson() {
        return logJson;
    }

    public void setLogJson(String logJson) {
        this.logJson = logJson;
    }

    public Integer getLogStatus() {
        return logStatus;
    }

    public void setLogStatus(Integer logStatus) {
        this.logStatus = logStatus;
    }

    public LocalDateTime getCurTimestamp() {
        return curTimestamp;
    }

    public void setCurTimestamp(LocalDateTime curTimestamp) {
        this.curTimestamp = curTimestamp;
    }
}
