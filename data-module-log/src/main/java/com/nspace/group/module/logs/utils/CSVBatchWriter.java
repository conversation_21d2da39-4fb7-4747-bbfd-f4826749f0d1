package com.nspace.group.module.logs.utils;

import cn.hutool.core.io.FileUtil;
import com.nspace.group.module.logs.service.offlinelog.dto.FileMeta;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Getter
public class CSVBatchWriter {
    private final String filePrefix;
    private final int batchSize;
    private final int bufferSize;

    private int fileIndex = 0;
    private int rowCount = 0;
    private String currentFileName;

    private FileChannel fileChannel;
    private ByteBuffer byteBuffer;

    private final List<FileMeta> metas = new ArrayList<>();
    private long currentMills;

    public CSVBatchWriter(String filePrefix, int batchSize, int bufferSize) throws Exception {
        this.filePrefix = filePrefix;
        this.batchSize = batchSize;
        this.bufferSize = bufferSize;
        openNewWriter();
    }

    private void openNewWriter() throws Exception {
        closeWriter();
        currentFileName = filePrefix + "-" + (fileIndex++) + ".csv";
        FileUtil.newFile(currentFileName);
        FileOutputStream fileOutputStream = new FileOutputStream(currentFileName);
        fileChannel = fileOutputStream.getChannel();
        byteBuffer = ByteBuffer.allocateDirect(bufferSize); // 堆外内存
        currentMills = System.currentTimeMillis();
        rowCount = 0;
    }

    @SneakyThrows
    public void writeRow(String[] row) {
        if (rowCount >= batchSize) {
            openNewWriter();
        }
        StringBuilder sb = new StringBuilder(2048);
        for (String cell : row) {
            sb.append(EscapeUtils.escapeCSV(cell)).append(",");
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(System.lineSeparator());
        byte[] bytes = sb.toString().getBytes(StandardCharsets.UTF_8);
        if (byteBuffer.remaining() < bytes.length) {
            flush();
        }
        byteBuffer.put(bytes);
        rowCount++;
    }

    private void flush() throws IOException {
        byteBuffer.flip();
        while (byteBuffer.hasRemaining()) {
            fileChannel.write(byteBuffer);
        }
        byteBuffer.clear();
    }

    @SneakyThrows
    public List<FileMeta> getFiles() {
        closeWriter();
        return metas;
    }

    private void closeWriter() throws Exception {
        if (byteBuffer != null && byteBuffer.position() > 0) {
            flush();
        }
        log.debug("completed write {} in {} ms", currentFileName, System.currentTimeMillis() - currentMills);
        currentMills = System.currentTimeMillis();
        if (fileChannel != null) {
            fileChannel.close();
        }
        if (currentFileName != null && rowCount > 0) {
            metas.add(FileMeta.builder()
                    .logName(currentFileName)
                    .rows(rowCount)
                    .build());
        }
    }

    public void clean() {
        metas.forEach(meta -> FileUtils.deleteQuietly(new File(meta.getLogName())));
    }
}
