package com.nspace.group.module.logs.dal.mapper.offlinelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DomainStatDTO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;

@DS("nspace_log")
public interface OfflineLogMapper {


    /**
     * 获取当前时间开启离线日志的域名的信息
     *
     * @return
     */
    @DS("nspace_controller")
    List<OfflineLogDO> selectByCurrent();

    /**
     * 获取时间段内域名的数据量
     *
     * @param start   开始时间
     * @param end     结束时间
     * @param domains 域名列表
     * @return
     */
    @DS("nspace_analysis")
    List<DomainStatDTO> getDomainStat(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end
            , @Param("domains") Collection<String> domains, @Param("timestamp") LocalDateTime dateTime);


    List<DelayedOfflineLogDO> getHistoryLogs(@Param("batches") List<String> batches);

    @DS("nspace_analysis")
    List<DelayedOfflineLogDO> selectDelayedLogs(@Param("start") LocalDateTime startTime, @Param("end") LocalDateTime endTime
            , @Param("logs") List<DelayedOfflineLogDO> delayedLogs);



    /**
     * 获取失败的明细任务
     */
    Set<OfflineLogDO> selectFailedDetailTask();

    /**
     * 获取需要合并的任务
     *
     * @param time
     * @return
     */
    List<OfflineLogDO> selectMergeTask(LocalDateTime time);

    /**
     * 恢复详细任务为未删除状态
     *
     * @param ids
     */
    void recoverDetails(@Param("ids") List<Long> ids);

    /**
     * 查询所有详细任务列表
     *
     * @param dto
     * @return
     */
    List<? extends OfflineLogDetailInfoDO> findAllDetails(OfflineLogDTO dto);
}
