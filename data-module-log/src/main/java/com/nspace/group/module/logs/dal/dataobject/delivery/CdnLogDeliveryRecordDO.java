package com.nspace.group.module.logs.dal.dataobject.delivery;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :CdnLogDeliveryRecordDO.java, v0.1 2025年04月22日 14:23 zhangxin Exp
 */
@TableName(value = "cdn_log_delivery_record")
public class CdnLogDeliveryRecordDO {

    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 业务类型,云直播：LSS 通用CDN：CDN
     */
    private String bizType;

    /**
     * 日志开始时间
     */
    private LocalDateTime logStartTime;

    /**
     * 日志结束时间
     */
    private LocalDateTime logEndTime;

    /**
     * 日志开始ID
     */
    private Long logStartId;

    /**
     * 日志结束ID
     */
    private Long logEndId;

    /**
     * 日志类型
     */
    private String logType;

    /**
     * 失败后重试次数，默认值0
     */
    private Integer retryCount;

    /**
     * 投递条数
     */
    private Long deliveryCount;

    /**
     * 日志投递状态 0:初始值，1:成功，3：重试
     */
    private Integer deliveryStatus;

    /**
     * 日志投递返回结果
     */
    private String deliveryResult;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic(value = "0", delval = "1")
    private Integer deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public LocalDateTime getLogStartTime() {
        return logStartTime;
    }

    public void setLogStartTime(LocalDateTime logStartTime) {
        this.logStartTime = logStartTime;
    }

    public LocalDateTime getLogEndTime() {
        return logEndTime;
    }

    public void setLogEndTime(LocalDateTime logEndTime) {
        this.logEndTime = logEndTime;
    }

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Long getDeliveryCount() {
        return deliveryCount;
    }

    public void setDeliveryCount(Long deliveryCount) {
        this.deliveryCount = deliveryCount;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getDeliveryResult() {
        return deliveryResult;
    }

    public void setDeliveryResult(String deliveryResult) {
        this.deliveryResult = deliveryResult;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Long getLogStartId() {
        return logStartId;
    }

    public void setLogStartId(Long logStartId) {
        this.logStartId = logStartId;
    }

    public Long getLogEndId() {
        return logEndId;
    }

    public void setLogEndId(Long logEndId) {
        this.logEndId = logEndId;
    }
}
