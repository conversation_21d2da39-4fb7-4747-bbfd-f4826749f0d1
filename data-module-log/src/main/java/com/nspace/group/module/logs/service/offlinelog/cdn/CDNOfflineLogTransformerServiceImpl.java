package com.nspace.group.module.logs.service.offlinelog.cdn;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.nspace.group.module.logs.service.offlinelog.AbstractOfflineLogTransformerService;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static com.nspace.group.module.logs.utils.Constant.CDN_FIELDS_SIZE;
import static com.nspace.group.module.logs.utils.Constant.CDN_NUMBER_FIELDS;

@Service("cdn-offline-transformer")
public class CDNOfflineLogTransformerServiceImpl extends AbstractOfflineLogTransformerService {

    private final Map<Integer, String> defaultExpressions;

    {
        defaultExpressions = new HashMap<>();
//        defaultExpressions.put(0, "ISO8061(param[0])");
    }


    @Override
    public Map<Integer, Expression> getDefaultExpressions() {
        return defaultExpressions.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey
                , item -> AviatorEvaluator.compile(item.getValue(), true)));
    }

    @Override
    @SneakyThrows
    public String[] transform0(ResultSet resultSet) {
        String[] result = new String[CDN_FIELDS_SIZE];
        for (int i = 0; i < CDN_FIELDS_SIZE; i++) {
            String value = resultSet.getString(i + 1);
            if (value != null && !value.isEmpty()) {
                result[i] = value.replace("\r", "")
                        .replace("\n", "").replace("\r\n", "");
            } else {
                result[i] = CDN_NUMBER_FIELDS.contains(i) ? "0" : "-";
            }
        }
        return result;
    }
}
