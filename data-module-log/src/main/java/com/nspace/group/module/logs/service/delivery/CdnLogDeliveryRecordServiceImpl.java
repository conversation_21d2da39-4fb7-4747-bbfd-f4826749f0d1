package com.nspace.group.module.logs.service.delivery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nspace.group.module.logs.dal.dataobject.delivery.CdnLogDeliveryRecordDO;
import com.nspace.group.module.logs.dal.mapper.delivery.CdnLogDeliveryRecordMapper;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * CDN日志投递记录服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-03-18 14:32:16
 */
@Service
@DS("doris_nspace_log")
@Validated
public class CdnLogDeliveryRecordServiceImpl implements CdnLogDeliveryRecordService {

    @Resource
    CdnLogDeliveryRecordMapper cdnLogDeliveryRecordMapper;

    @Override
    public void fillRecordId(LogDeliveryRecordDTO deliveryRecord) {
        if (deliveryRecord.getId() != null) return;
        LambdaQueryWrapper<CdnLogDeliveryRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CdnLogDeliveryRecordDO::getId)
                .eq(CdnLogDeliveryRecordDO::getTenantId, deliveryRecord.getTenantId())
                .eq(CdnLogDeliveryRecordDO::getDomain, deliveryRecord.getDomain())
                .eq(CdnLogDeliveryRecordDO::getBizType, deliveryRecord.getBizType())
                .eq(CdnLogDeliveryRecordDO::getLogType, deliveryRecord.getLogType())
                .eq(CdnLogDeliveryRecordDO::getLogStartTime, deliveryRecord.getLogStartTime());
        CdnLogDeliveryRecordDO deliveryRecordDO = cdnLogDeliveryRecordMapper.selectOne(queryWrapper);
        deliveryRecord.setId(deliveryRecordDO.getId());
    }
}

