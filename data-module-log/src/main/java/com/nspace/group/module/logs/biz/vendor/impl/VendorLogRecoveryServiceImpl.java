package com.nspace.group.module.logs.biz.vendor.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.logs.biz.vendor.VendorLogDetailService;
import com.nspace.group.module.logs.biz.vendor.VendorLogRecoveryService;
import com.nspace.group.module.logs.enums.VendorLogTypeEnum;
import com.nspace.group.module.logs.service.vendor.dto.VendorLogDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;

@Slf4j
@Service(value = "vendorLogRecoveryService")
public class VendorLogRecoveryServiceImpl implements VendorLogRecoveryService {

    // Redis key过期时间（分钟）
    private final int TASK_KEY_EXPIRATION = 18;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    @Resource
    private VendorLogDetailService logDetailService;

    /**
     * 异常日志重新消费
     *
     * @param bizType 业务类型
     * @param limit   批次限制
     */
    @Override
    public void recoverLogs(String bizType, Integer limit) {
        String taskKey = getLogRecoveryTaskKey(bizType);
        Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(taskKey, StringPool.ONE, Duration.ofMinutes(TASK_KEY_EXPIRATION));
        //获取锁失败直接返回
        if (Boolean.FALSE.equals(success)) return;
        try {
            int curCount = 0;
            while (curCount <= limit) {
                //获取需要重新消费的日志
                List<VendorLogDetailDTO> logDetails = logDetailService.getDetails(bizType, null, limit);
                curCount = logDetails.size();
                if (curCount == 0) {
                    log.info("recoverLogs,VendorLogDetailService.getDetails,no_vendor_logs,biz_type={}", bizType);
                    return;
                }

                Queue<VendorLogDetailDTO> successDetailList = new ConcurrentLinkedQueue<>();
                List<CompletableFuture<SendResult<String, String>>> futures = new ArrayList<>();
                for (VendorLogDetailDTO logDetail : logDetails) {
                    String logJson = logDetail.getLogJson();
                    String logType = resolveVendorLogType(bizType, logJson);
                    ListenableFuture<SendResult<String, String>> f = null;
                    if (VendorLogTypeEnum.PUSH.isSelf(logType)) {
                        f = kafkaTemplate.send(VendorLogTypeEnum.PUSH.getTopic(), logJson);
                    } else if (VendorLogTypeEnum.PULL.isSelf(logType)) {
                        f = kafkaTemplate.send(VendorLogTypeEnum.PULL.getTopic(), logJson);
                    } else if (VendorLogTypeEnum.GENERAL.isSelf(logType)) {
                        f = kafkaTemplate.send(VendorLogTypeEnum.GENERAL.getTopic(), logJson);
                    }
                    if (f != null) {
                        f.addCallback(new SendLogListenableFutureCallback(logDetail, successDetailList));
                        futures.add(f.completable());
                    }
                }
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                allFutures.whenComplete((Void unused, Throwable throwable) -> {
                    if (!successDetailList.isEmpty()) {
                        logDetailService.updateMany(bizType, successDetailList);
                    }
                }).join();
                if (curCount < limit) break;
            }
        } catch (Exception e) {
            log.error("recoverLogs,unknown_exception,biz_type={},error={}", bizType, ExceptionUtil.getRootCauseMessage(e));
        } finally {
            //任务执行结束，删除redis中的key
            log.info("recoverLogs,remove_vendor_log_recover_task_from_registry,task={}", taskKey);
            stringRedisTemplate.delete(taskKey);
        }
    }

    private String resolveVendorLogType(String bizType, String logJson) {
        if (BusinessTypeEnum.BUSINESS_TYPE_CDN.isSelf(bizType)) return VendorLogTypeEnum.GENERAL.getType();
        return JsonUtils.parseTree(logJson).path("category").asText("unknown");
    }

    private String getLogRecoveryTaskKey(String bizType) {
        return String.join(StringPool.COLON, "vendor_log_recovery_task", bizType);
    }

    private static class SendLogListenableFutureCallback implements ListenableFutureCallback<SendResult<String, String>> {
        private final VendorLogDetailDTO vendorLogDetail;

        private final Queue<VendorLogDetailDTO> successDetailList;

        private SendLogListenableFutureCallback(VendorLogDetailDTO vendorLogDetail, Queue<VendorLogDetailDTO> successDetailList) {
            this.vendorLogDetail = vendorLogDetail;
            this.successDetailList = successDetailList;
        }

        @Override
        public void onSuccess(SendResult<String, String> result) {
            successDetailList.add(vendorLogDetail);
        }

        @Override
        public void onFailure(Throwable ex) {
        }
    }
}
