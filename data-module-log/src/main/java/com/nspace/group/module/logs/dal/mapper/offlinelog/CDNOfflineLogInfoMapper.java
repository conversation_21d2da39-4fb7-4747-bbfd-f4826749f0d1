package com.nspace.group.module.logs.dal.mapper.offlinelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.CDNOfflineLogInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@DS("nspace_log")
@Repository("cdn-offline-mapper")
public interface CDNOfflineLogInfoMapper extends OfflineLogMapper,BaseMapperX<CDNOfflineLogInfoDO> {

}
