package com.nspace.group.module.logs.convert.vendor;

import com.nspace.group.module.logs.dal.dataobject.vendor.VendorLiveLogDetailDO;
import com.nspace.group.module.logs.service.vendor.dto.VendorLogDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version :VendorLiveLogDetailConvert.java, v0.1 2025年05月12日 10:21 zhangxin Exp
 */
@Mapper
public interface VendorLiveLogDetailConvert {

    VendorLiveLogDetailConvert INSTANCE = Mappers.getMapper(VendorLiveLogDetailConvert.class);

    VendorLogDetailDTO getVendorLogDetailDTO(VendorLiveLogDetailDO record);

    List<VendorLogDetailDTO> getVendorLogDetailDTOList(List<VendorLiveLogDetailDO> records);
}
