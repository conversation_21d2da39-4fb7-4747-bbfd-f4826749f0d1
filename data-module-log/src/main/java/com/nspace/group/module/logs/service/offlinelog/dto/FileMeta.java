package com.nspace.group.module.logs.service.offlinelog.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileMeta {

    private String bucket;

    private String logName;

    private Long fileSize;

    private Integer rows;

    /**
     * 需要删除的本地文件,包括自身
     */
    private List<String> files = Collections.emptyList();

}
