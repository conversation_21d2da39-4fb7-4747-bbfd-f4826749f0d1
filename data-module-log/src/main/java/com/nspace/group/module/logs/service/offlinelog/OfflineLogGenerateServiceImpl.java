package com.nspace.group.module.logs.service.offlinelog;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.googlecode.aviator.Expression;
import com.nspace.group.framework.file.core.FileClient;
import com.nspace.group.framework.file.core.dto.MetaDTO;
import com.nspace.group.module.logs.config.logs.OfflineLogConfiguration;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DomainStatDTO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.OfflineLogMapper;
import com.nspace.group.module.logs.enums.MergeTypeEnum;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import com.nspace.group.module.logs.service.offlinelog.dto.FileMeta;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import com.nspace.group.module.logs.utils.*;
import io.minio.ObjectWriteArgs;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.sql.ResultSet;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version :OfflineLogGenerateServiceImpl.java, v0.1 2024年12月18日 11:52 Exp
 */
@Service
@Slf4j
public class OfflineLogGenerateServiceImpl implements OfflineLogGenerateService, ApplicationListener<ContextRefreshedEvent> {


    @Resource
    private OfflineLogFactory logFactory;
    @Resource
    private FileClient fileClient;
    @Resource
    private ExpressionManager expressionManager;
    @Resource
    private OfflineLogConfiguration configuration;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * spring 容器启动完成后,创建对应的文件
     *
     * @param event
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        //创建目录
        //todo: 目前没有生效
        log.info("创建目录 {}", configuration.getPath());
        FileUtil.mkdir(new File(configuration.getPath()));
        FileUtil.clean(configuration.getPath());
        //正常关闭时也清空对应目录中的文件
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            FileUtil.clean(configuration.getPath());
        }));
    }


    @Override
    public void generateAllOfflineLog(OfflineLogDTO dto, boolean removeHistory) {
        long start = System.currentTimeMillis();
        List<CompletableFuture<Void>> futures = new CopyOnWriteArrayList<>();
        OfflinePersistService persistService = logFactory.getPersistService(dto.getLogType());
        List<Long> historyIds = persistService.find(dto.getBatchNum(), dto.getTenantId(), dto.getDomain());
        Integer totalBatch = configuration.getTotalBatch();
        for (Integer i = 0; i < totalBatch; i++) {
            OfflineLogDTO task = OfflineLogDTO.newDTO(dto);
            task.setExecuteBatch(i);
            futures.add(generateOfflineLog(task));
        }
        CompletableFuture.allOf(wrap(futures)).whenCompleteAsync((result, ex) -> {
            log.info("延迟日志回补: 批次: {},域名: {},日志类型: {},耗时: {} ms", dto.getBatchNum(), dto.getDomain()
                    , dto.getLogType(), (System.currentTimeMillis() - start));
            if (ex == null) {
                dto.setHistoryIds(historyIds);
                dto.setRemoveHistory(removeHistory);
                mergeSubBatch(dto, true);
            }
        }, ThreadFactory.getIOExecutors());
    }

    @Override
    public CompletableFuture<Void> generateOfflineLog(OfflineLogDTO dto) {
        long start = System.currentTimeMillis();
        String logType = dto.getLogType();
        String batchNum = dto.getBatchNum();
        String domain = dto.getDomain();
        Integer executeBatch = dto.getExecuteBatch();
        Long totalRows = dto.getTotalCount();

        //如果数据库数据过少,则只进行一次分批处理,并保证批次能被60整除,如果无法获取行数,择取 doris 反查对应数据
        int minute = 60 / configuration.getTotalBatch();
        int maxBatch = configuration.getSubBatch();
        if (totalRows == null) {
            LocalDateTime dateTime = LocalDateTime.parse(batchNum, DTF.yyyyMMddHH);
            LocalDateTime startTime = dateTime.withMinute(executeBatch * 5).withSecond(0).withNano(0);
            LocalDateTime endTime = dateTime.withMinute((executeBatch + 1) * 5 - 1).withSecond(59).withNano(0);
            OfflineLogMapper logMapper = logFactory.getOfflineLogMapper(logType);
            List<DomainStatDTO> domainStats = logMapper.getDomainStat(startTime, endTime, List.of(domain), dto.getStartTimestamp());
            DomainStatDTO stat = CollUtil.getFirst(domainStats);
            if (stat != null) {
                totalRows = stat.getTotalCount();
                dto.setTotalCount(stat.getTotalCount());
                dto.setMaxTimeStamp(stat.getMaxTimeStamp());
            }
        }
        if (totalRows == null || totalRows == 0) {
            log.info("跳过该任务: 批次: {}, 域名: {}, 日志类型: {},当前批次:{} ", batchNum, domain, logType, executeBatch);
            return CompletableFuture.completedFuture(null);
        }
        long estimated = Math.round((float) totalRows / configuration.getBatchSize());
        int limitedBatch = Math.toIntExact(Math.max(1, Math.min(estimated, maxBatch)));
        int subBatch = Stream.of(15, 10, 6, 5, 4, 3, 2, 1)
                .filter(batch -> batch <= limitedBatch)
                .findFirst()
                .orElse(1);
        dto.setSubBatch(subBatch);
        int second = 60 * minute / subBatch;

        log.info("开始生成离线日志 - 批次: {}, 域名: {}, 日志类型: {}, 待同步 {} 行数据, 当前批次:{} 子任务数量:{},子任务步长:{}s"
                , batchNum, domain, logType, totalRows, executeBatch, subBatch, second);
        List<CompletableFuture<Void>> futures;
        // 全部子批次
        futures = new ArrayList<>();
        for (int batch = 0; batch < subBatch; batch++) {
            futures.add(executeQuietly(OfflineLogDTO.newDTO(dto), executeBatch, batch, second));
        }
        return CompletableFuture.allOf(wrap(futures)).whenComplete((v, e) -> {
            log.info("完成导出批次: {},日志类型: {}, 域名: {}, 批次 : {}, 耗时: {} ms", batchNum, logType, domain
                    , executeBatch, System.currentTimeMillis() - start);
        });
    }

    private CompletableFuture<Void> executeQuietly(OfflineLogDTO dto, Integer executeBatch, Integer subBatch, int minute) {
        OfflinePersistService persistService = logFactory.getPersistService(dto.getLogType());
        return execute(dto, executeBatch, subBatch, minute)
                .whenComplete((v, e) -> {
                    if (e != null) {
                        log.error(e.getMessage(), e);
                        OfflineLogInfoDO infoDO = OfflineConverters.convertDetail(dto);
                        dto.setExecuteBatch(executeBatch);
                        infoDO.setMessage(e.getMessage());
                        infoDO.setStatus(OfflineStatusEnum.FAILED);
                        persistService.persist(infoDO);
                    }
                });
    }

    /**
     * 合并子批次
     *
     * @param dto 实体
     */
    @Override
    public void mergeSubBatch(OfflineLogDTO dto, boolean force) {
        String batchNum = dto.getBatchNum();
        OfflinePersistService persistService = logFactory.getPersistService(dto.getLogType());
        List<? extends OfflineLogDetailInfoDO> details = persistService
                .findDetails(batchNum, dto.getTenantId(), dto.getDomain());
        boolean isAllSuccess = details.stream()
                .allMatch(item -> item.getStatus() == OfflineStatusEnum.SUCCESS);
        //强制合并,只需要满足子任务状态为成功即可
        if (isAllSuccess && force) {
            mergeSubBatchFiles(dto, new ArrayList<>(details));
        } else if (isAllSuccess) {
            // 若没有失败子任务，且子任务总大小超过 500MB 或最后一个文件距当前时间超过 1 小时，则视为所有子任务已完成。
            LocalDateTime maxTime = details.stream().map(OfflineLogDetailInfoDO::getUpdateTime)
                    .max(Comparator.naturalOrder()).orElse(LocalDateTime.MAX);
            if (maxTime.isBefore(LocalDateTime.now().minusHours(1))) {
                List<OfflineLogInfoDO> mergeDOs = new ArrayList<>(details);
                mergeSubBatchFiles(dto, mergeDOs);
            } else {
                details.sort(Comparator.comparing(OfflineLogDetailInfoDO::getFileSize));
                long totalSize = 0;
                long fileSize = configuration.getFileSize();
                List<OfflineLogInfoDO> mergeDOs = new ArrayList<>();
                for (OfflineLogDetailInfoDO detail : details) {
                    totalSize += detail.getFileSize();
                    if (totalSize > fileSize) {
                        break;
                    }
                    mergeDOs.add(detail);
                }
                if (totalSize > fileSize) {
                    mergeSubBatchFiles(dto, mergeDOs);
                }
            }
        }
    }

    /**
     * 合并所有子批次的文件
     *
     * @param dto      离线日志实体
     * @param mergeDOS 待合并的子批次
     */
    private void mergeSubBatchFiles(OfflineLogDTO dto, List<? extends OfflineLogInfoDO> mergeDOS) {
        String batchNum = dto.getBatchNum();
        Long tenantId = dto.getTenantId();
        String domain = dto.getDomain();
        //上一次合并没有执行完成或者异常后未删除
        String redisKey = String.format("%s-%s-%s-%s", "offline-log-merger", batchNum, tenantId, domain);
        Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 10, TimeUnit.MINUTES);
        if (Boolean.FALSE.equals(success)) {
            return;
        }
        OfflinePersistService persistService = logFactory.getPersistService(dto.getLogType());
        //使用历史记录的条数作为新纪录的起始点
        if (Boolean.FALSE.equals(dto.isRemoveHistory())) {
            List<Long> ids = persistService.find(batchNum, tenantId, domain);
            dto.setStartIndex(ids.size());
        }
        List<Long> details = new ArrayList<>(mergeDOS.size());
        List<FileMeta> metas = new ArrayList<>(mergeDOS.size());
        LocalDateTime maxTimeStamp = null;
        for (OfflineLogInfoDO item : mergeDOS) {
            details.add(item.getId());
            //取所有子批次的最大时间作为任务批次的最大时间
            LocalDateTime current = item.getMaxTimestamp();
            if (current != null && (maxTimeStamp == null || current.isAfter(maxTimeStamp))) {
                maxTimeStamp = current;
            }
            FileMeta meta = new FileMeta();
            meta.setFileSize(item.getFileSize());
            meta.setLogName(item.getLogName());
            meta.setRows(item.getRows());
            metas.add(meta);
        }
        // 设置最大时间戳
        dto.setMaxTimeStamp(maxTimeStamp);
        String fileName = String.format("%s_%s_%%02d.gz", domain, batchNum);
        MergeResponse response = mergeAndPersist(dto, fileName, metas, MergeTypeEnum.HYBRID_MERGE);
        CompletableFuture.allOf(wrap(response.futures))
                .whenComplete((result, ex) -> {
                    if (CollUtil.isNotEmpty(response.getIds())) {
                        dto.setCurrentIds(response.getIds());
                        if (ex == null) {
                            persistService.batchUpdate(dto, null, OfflineStatusEnum.SUCCESS, details);
                        } else {
                            log.error(ex.getMessage(), ex);
                            persistService.batchUpdate(dto, ex.getMessage(), OfflineStatusEnum.FAILED, details);
                        }
                    }
                    stringRedisTemplate.delete(redisKey);
                    response.getFiles().forEach(item -> {
                        FileUtils.deleteQuietly(new File(item));
                    });
                });
    }


    private CompletableFuture<Void> execute(OfflineLogDTO dto, int executeBatch, int batch, int second) {
        String logType = dto.getLogType();
        String domain = dto.getDomain();
        String batchNum = dto.getBatchNum();
        dto.setExecuteBatch(executeBatch);

        OfflineLogFetcherService fetcherService = logFactory.getFetcherService(logType);
        OfflineLogTransformerService transformerService = logFactory.getTransformerService(logType);

        LocalDateTime dateTime = LocalDateTime.parse(batchNum, DTF.yyyyMMddHH).withMinute(executeBatch * 5);
        LocalDateTime startTime = dateTime.plusSeconds((long) batch * second).withNano(0);
        LocalDateTime endTime = dateTime.plusSeconds((long) (batch + 1) * second - 1).withNano(0);

        String redisKey = String.format("%s-%s-%s-%s-%s-%s-%s", "offline-log-export", logType, batchNum, domain
                , executeBatch, startTime, endTime);

        //分批次执行不超过10分钟,超过10分钟,会被强制中断
        Boolean result = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 10, TimeUnit.MINUTES);
        if (Boolean.FALSE.equals(result)) {
            return CompletableFuture.completedFuture(null);
        }

        dto.setStartTime(startTime);
        dto.setEndTime(endTime);

        ResultSet resultSet = null;
        String filePrefix = UUID.randomUUID().toString();
        String csvPrefix = configuration.getPath() + "/" + filePrefix;
        CSVBatchWriter csvBatchWriter = null;
        try {
            //去查询明细表中的子任务记录,如果对应的记录成功,且 postgres 和 doris 数据无差异,则可以跳过当前子任务
            Boolean skip = shouldSkip(dto, startTime, endTime);
            if (Boolean.FALSE.equals(skip)) {
                log.info("开始执行 日志类型:{} 批次:{} 域名:{},第 {} 批次,开始时间:{},结束时间:{}", logType, batchNum, domain
                        , executeBatch, startTime, endTime);
                csvBatchWriter = new CSVBatchWriter(csvPrefix, configuration.getCsvRows(), configuration.getBufferSize());
                Map<Integer, Expression> defaultExpressions = transformerService.getDefaultExpressions();
                Map<Integer, Expression> expressions = expressionManager.getExpressions(dto.getTenantId(), domain, logType);
                defaultExpressions.putAll(expressions);

                resultSet = fetcherService.fetchLogsResult(startTime, endTime, domain
                        , dto.getStartTimestamp(), configuration.getFetchSize());
                while (resultSet.next()) {
                    String[] row = transformerService.transform(defaultExpressions, resultSet);
                    csvBatchWriter.writeRow(row);
                }
            }
        } catch (Exception e) {
            if (csvBatchWriter != null) {
                csvBatchWriter.clean();
            }
            stringRedisTemplate.delete(redisKey);
            return CompletableFuture.failedFuture(e);
        } finally {
            DbUtils.close(resultSet);
        }
        if (csvBatchWriter != null) {
            List<FileMeta> files = csvBatchWriter.getFiles();
            //将压缩逻辑抽出来,避免长期持有 resultSet,导致 grpc 超时
            return compressAndPersist(dto, executeBatch, files)
                    .whenComplete((re, ex) -> stringRedisTemplate.delete(redisKey));
        } else {
            return CompletableFuture.completedFuture(null)
                    .thenRun(() -> stringRedisTemplate.delete(redisKey));
        }
    }

    /**
     * 对比 doris 数据 和 postgres 数据是否一致,如果一致,则跳过对应子任务, 并回滚子任务状态,并删除其它子任务
     *
     * @param dto
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 是否可以跳过
     */
    @Nullable
    private boolean shouldSkip(OfflineLogDTO dto, LocalDateTime startTime, LocalDateTime endTime) {
        String batchNum = dto.getBatchNum();
        String logType = dto.getLogType();
        String domain = dto.getDomain();
        OfflineLogMapper logMapper = logFactory.getOfflineLogMapper(logType);
        OfflinePersistService persistService = logFactory.getPersistService(logType);
        boolean skip = false;
        List<? extends OfflineLogDetailInfoDO> details = logMapper.findAllDetails(dto);
        List<Long> historyIds = new ArrayList<>();
        Map<String, Integer> rows = details.stream()
                .filter(item -> item.getStartTime().equals(startTime) && item.getEndTime().equals(endTime))
                .peek(item -> historyIds.add(item.getId()))
                .filter(item -> item.getStatus() == OfflineStatusEnum.SUCCESS)
                .collect(Collectors.toMap(item -> item.getLogName().substring(0, 36),
                        OfflineLogInfoDO::getRows, Integer::sum));
        if (CollUtil.isEmpty(rows)) {
            return false;
        }
        List<DomainStatDTO> result = logMapper.getDomainStat(startTime, endTime, List.of(domain), dto.getStartTimestamp());
        DomainStatDTO statDTO = CollUtil.getFirst(result);
        if (statDTO != null) {
            Integer totalCount = Math.toIntExact(statDTO.getTotalCount());
            for (Map.Entry<String, Integer> entry : rows.entrySet()) {
                Integer sum = entry.getValue();
                String key = entry.getKey();
                if (totalCount.equals(sum)) {
                    List<Long> ids = details.stream().filter(item -> item.getLogName().startsWith(key))
                            .map(OfflineLogInfoDO::getId).collect(Collectors.toList());
                    historyIds.removeAll(ids);
                    dto.setCurrentIds(ids);
                    dto.setRemoveHistory(true);
                    dto.setHistoryIds(historyIds);
                    persistService.batchUpdateDetails(dto, null, OfflineStatusEnum.SUCCESS);
                    log.info("该任务已执行,日志类型:{} 批次:{} 域名:{}: 开始时间:{},结束时间:{}"
                            , logType, batchNum, domain, startTime, endTime);
                    skip = true;
                    break;
                }
            }
        }
        return skip;
    }

    @NotNull
    private CompletableFuture<Void> compressAndPersist(OfflineLogDTO dto, int executeBatch, List<FileMeta> files) {
        String fileName = String.format("%s_%d_%%02d.gz", UUID.randomUUID(), dto.getExecuteBatch());
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        files.forEach(item -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                            GzipUtils.compressGzip(item, configuration.getCommand())
                    , ThreadFactory.getCompressorExecutors());
            futures.add(future);
        });
        String logType = dto.getLogType();
        OfflineLogMapper logMapper = logFactory.getOfflineLogMapper(logType);
        OfflinePersistService persistService = logFactory.getPersistService(logType);
        List<? extends OfflineLogDetailInfoDO> details = logMapper.findAllDetails(dto);
        List<Long> historyIds = details.stream()
                .filter(item -> item.getStartTime().equals(dto.getStartTime()) && item.getEndTime().equals(dto.getEndTime()))
                .map(OfflineLogInfoDO::getId).collect(Collectors.toList());
        return CompletableFuture.allOf(wrap(futures))
                .thenApplyAsync(unused -> {
                    dto.setExecuteBatch(executeBatch);
                    return mergeAndPersist(dto, fileName, files, MergeTypeEnum.LOCAL);
                }, ThreadFactory.getIOExecutors())
                .thenCompose(response -> CompletableFuture.allOf(wrap(response.futures))
                .whenComplete((result, ex) -> {
                    List<Long> ids = response.getIds();
                    if (CollUtil.isNotEmpty(ids)) {
                        dto.setCurrentIds(ids);
                        dto.setRemoveHistory(true);
                        dto.setHistoryIds(historyIds);
                        if (ex != null) {
                            log.error(ex.getMessage());
                            persistService.batchUpdateDetails(dto, ex.getMessage(), OfflineStatusEnum.FAILED);
                        } else {
                            persistService.batchUpdateDetails(dto, null, OfflineStatusEnum.SUCCESS);
                        }
                    }
                    response.getFiles().forEach(item -> {
                        FileUtils.deleteQuietly(new File(item));
                    });
                    files.forEach(item -> {
                        FileUtils.deleteQuietly(new File(item.getLogName()));
                    });
                }));
    }


    /**
     * 合并子文件并上传至 oss 中
     *
     * @param dto
     * @param fileName  最终文件名
     * @param metas     文件原信息
     * @param mergeType 合并类型
     * @return 合并后的响应
     */
    private MergeResponse mergeAndPersist(OfflineLogDTO dto, String fileName, List<FileMeta> metas, MergeTypeEnum mergeType) {
        String logType = dto.getLogType();
        OfflinePersistService persistService = logFactory.getPersistService(logType);
        //说明是子任务合并,需要保证文件小于一定比例,否则回导致最终的文件不够要求
        Long fileSize = configuration.getFileSize();
        if (mergeType == MergeTypeEnum.LOCAL) {
            fileSize = configuration.getFileSize() / 3;
        }
        List<List<FileMeta>> groupedFiles = GzipUtils.groupFiles(metas, fileSize);
        List<Long> ids = new ArrayList<>();
        List<String> files = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(dto.getStartIndex());
        List<CompletableFuture<FileMeta>> futures = new ArrayList<>();
        groupedFiles.forEach(fileGroups -> {
            String finalName = String.format(fileName, index.getAndIncrement());
            CompletableFuture<FileMeta> future = mergeFileAndUpload(dto, finalName, mergeType, fileGroups)
                    .whenComplete((meta, ex) -> {
                        OfflineLogInfoDO logDO;
                        if (mergeType == MergeTypeEnum.LOCAL) {
                            logDO = OfflineConverters.convertDetail(dto);
                        } else {
                            logDO = OfflineConverters.convert(dto);
                        }
                        if (meta != null) {
                            BeanUtils.copyProperties(meta, logDO);
                            files.addAll(meta.getFiles());
                        }
                        logDO.setMaxTimestamp(dto.getMaxTimeStamp());
                        persistService.persist(logDO);
                        ids.add(logDO.getId());
                    });
            futures.add(future);
        });
        return MergeResponse.builder().ids(ids).files(files).futures(futures).build();
    }

    @Builder
    @Getter
    static class MergeResponse {
        private List<Long> ids;

        private List<String> files;

        private List<CompletableFuture<FileMeta>> futures;
    }

    /**
     * @param dto        日志实体
     * @param fileName   合并后的文件名称
     * @param mergeType  合并类型
     * @param fileGroups 待合并的文件信息
     * @return 合并后的文件元信息
     */
    @NotNull
    private CompletableFuture<FileMeta> mergeFileAndUpload(OfflineLogDTO dto, String fileName, MergeTypeEnum mergeType
            , List<FileMeta> fileGroups) {
        Map<String, String> tags = Map.of("expire", String.format("%dd", dto.getLifeCycle()));
        CompletableFuture<FileMeta> metaData;
        //如果所有文件大于 5m,则使用 oss 合并
        boolean allLargeEnough = fileGroups.stream()
                .allMatch(item -> item.getFileSize() > ObjectWriteArgs.MIN_MULTIPART_SIZE);
        if (allLargeEnough && mergeType == MergeTypeEnum.HYBRID_MERGE) {
            mergeType = MergeTypeEnum.OSS;
        }
        if (mergeType == MergeTypeEnum.OSS) {
            metaData = mergeBYOSS(dto, fileName, fileGroups, tags);
        } else if (mergeType == MergeTypeEnum.LOCAL) {
            metaData = mergeLocal(dto, fileName, fileGroups, Constant.EXPIRE_TAGS, false);
        } else {
            metaData = mergeLocal(dto, fileName, fileGroups, tags, true);
        }
        return metaData;
    }

    /**
     * 使用 oss 的方式进行合并,需要保证合并的文件都大于5M(最后一个文件可以不大于)
     *
     * @param dto        日志实体
     * @param fileName   文件名
     * @param fileGroups 待合并的文件
     * @param tags       标签,用于 oss 删除策略
     * @return 文件元数据
     */
    private CompletableFuture<FileMeta> mergeBYOSS(OfflineLogDTO dto, String fileName
            , List<FileMeta> fileGroups, Map<String, String> tags) {
        String logType = dto.getLogType();
        List<String[]> sources = fileGroups.stream()
                .map(item -> new String[]{logType, item.getLogName()})
                .collect(Collectors.toList());
        BiConsumer<String, List<String>> consumer = (name, files) ->
                fileClient.composeObject(logType, fileName, tags, sources);
        return CompletableFuture.supplyAsync(() -> GzipUtils.merge(fileGroups, fileName, consumer)
                        , ThreadFactory.getIOExecutors())
                .thenApply(meta -> {
                    MetaDTO metaDTO = fileClient.statObject(logType, fileName);
                    meta.setBucket(logType);
                    meta.setLogName(fileName);
                    meta.setFileSize(metaDTO.getFileSize());
                    return meta;
                });
    }


    /**
     * 使用本地的方式进行合并,可能需要将文件下载到本地
     *
     * @param dto        日志实体
     * @param fileName   文件名
     * @param fileGroups 待合并的文件
     * @param tags       标签,用于 oss 删除策略
     * @param isDownload 是否下载文件到本地
     * @return 文件元数据
     */
    private CompletableFuture<FileMeta> mergeLocal(OfflineLogDTO dto, String fileName, List<FileMeta> fileGroups
            , Map<String, String> tags, Boolean isDownload) {
        String logType = dto.getLogType();
        List<String> files = new CopyOnWriteArrayList<>();
        String localName = configuration.getPath() + "/" + fileName;
        files.add(localName);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        fileGroups.forEach(item -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                if (isDownload) {
                    String objectKey = item.getLogName();
                    String localPath = configuration.getPath() + "/" + objectKey;
                    fileClient.download(logType, objectKey, localPath);
                    item.setLogName(localPath);
                    files.add(localPath);
                } else {
                    files.add(item.getLogName());
                }
            }, ThreadFactory.getIOExecutors());
            futures.add(future);
        });
        return CompletableFuture.allOf(futures.toArray(wrap(futures)))
                .thenApply(unused -> {
                    FileMeta metadata = GzipUtils.merge(fileGroups, localName, GzipUtils::mergeGzip);
                    metadata.setFileSize(FileUtil.size(new File(localName)));
                    metadata.setLogName(fileName);
                    fileClient.upload(logType, fileName, localName, Constant.GZIP, tags);
                    metadata.setBucket(logType);
                    metadata.setFiles(files);
                    return metadata;
                });
    }


    public <T> CompletableFuture<?>[] wrap(List<CompletableFuture<T>> futures) {
        if (CollUtil.isEmpty(futures)) {
            CompletableFuture<T> failedFuture = CompletableFuture
                    .failedFuture(new IllegalArgumentException("empty future list"));
            return new CompletableFuture[]{failedFuture};
        }
        return futures.toArray(new CompletableFuture[0]);
    }


}
