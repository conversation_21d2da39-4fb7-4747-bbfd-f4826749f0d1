package com.nspace.group.module.logs.service.delivery;

import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 直播CDN日志投递-投递明细服务接口
 *
 * <AUTHOR>
 * @since 2025-05-12 14:32:16
 */
public interface DeliveryLiveLogDetailService {

    /**
     * 获取投递日志明细列表
     *
     * @param targetType     第三方代码
     * @param timeLowerLimit 创建时间下限
     * @param limit          批次限制
     * @return List<DeliveryLogDetailDTO>
     */
    List<DeliveryLogDetailDTO> getDetails(String targetType, LocalDateTime timeLowerLimit, Integer limit);

}

