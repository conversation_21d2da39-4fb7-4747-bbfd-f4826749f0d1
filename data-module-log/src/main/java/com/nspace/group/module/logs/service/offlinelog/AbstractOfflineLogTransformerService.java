package com.nspace.group.module.logs.service.offlinelog;

import cn.hutool.core.collection.CollUtil;
import com.googlecode.aviator.Expression;

import java.sql.ResultSet;
import java.util.Map;

public abstract class AbstractOfflineLogTransformerService implements OfflineLogTransformerService {


    @Override
    public String[] transform(Map<Integer, Expression> expressions, ResultSet resultSet) {
        String[] result = transform0(resultSet);
        if (CollUtil.isNotEmpty(expressions)) {
            expressions.forEach((index, expression) -> {
                Map<String, Object> env = expression.newEnv("param", result);
                result[index] = (String) expression.execute(env);
            });
        }
        return result;
    }

    public abstract Map<Integer, Expression> getDefaultExpressions();

    public abstract String[] transform0(ResultSet resultSet);
}
