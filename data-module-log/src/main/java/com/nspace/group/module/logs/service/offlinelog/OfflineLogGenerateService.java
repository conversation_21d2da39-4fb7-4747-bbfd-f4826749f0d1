package com.nspace.group.module.logs.service.offlinelog;


import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version :OfflineLogGenerateService.java, v0.1 2024年12月18日 11:52 Exp
 */
public interface OfflineLogGenerateService {

    /**
     * 延迟日志重跑
     *
     * @param dto           离线日志DTO 实体
     * @param removeHistory 是否删除旧的任务
     */
    void generateAllOfflineLog(OfflineLogDTO dto, boolean removeHistory);

    /**
     * 跑单独的批次,每次只跑 5 分钟
     *
     * @param dto
     */
    CompletableFuture<Void> generateOfflineLog(OfflineLogDTO dto);

    /**
     * 合并子批次到一个批次中
     *
     * @param dto   离线日志DTO 实体
     * @param force 强制合并,用于延迟日志重跑
     */
    void mergeSubBatch(OfflineLogDTO dto, boolean force);
}
