package com.nspace.group.module.logs.service.delivery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nspace.group.module.logs.dal.dataobject.delivery.LiveLogDeliveryRecordDO;
import com.nspace.group.module.logs.dal.mapper.delivery.LiveLogDeliveryRecordMapper;
import com.nspace.group.module.logs.service.delivery.dto.LogDeliveryRecordDTO;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 直播日志投递记录服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-03-18 14:32:16
 */
@Service
@DS("doris_nspace_log")
@Validated
public class LiveLogDeliveryRecordServiceImpl implements LiveLogDeliveryRecordService {

    @Resource
    private LiveLogDeliveryRecordMapper liveLogDeliveryRecordMapper;

    @Override
    public void fillRecordId(LogDeliveryRecordDTO deliveryRecord) {
        if (deliveryRecord.getId() != null) return;
        LambdaQueryWrapper<LiveLogDeliveryRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(LiveLogDeliveryRecordDO::getId)
                .eq(LiveLogDeliveryRecordDO::getTenantId, deliveryRecord.getTenantId())
                .eq(LiveLogDeliveryRecordDO::getDomain, deliveryRecord.getDomain())
                .eq(LiveLogDeliveryRecordDO::getBizType, deliveryRecord.getBizType())
                .eq(LiveLogDeliveryRecordDO::getLogType, deliveryRecord.getLogType())
                .eq(LiveLogDeliveryRecordDO::getLogStartTime, deliveryRecord.getLogStartTime());
        LiveLogDeliveryRecordDO deliveryRecordDO = liveLogDeliveryRecordMapper.selectOne(queryWrapper);
        deliveryRecord.setId(deliveryRecordDO.getId());
    }
}

