package com.nspace.group.module.logs.dal.mapper.offlinelog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogExpressionDO;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("nspace_log")
public interface OfflineLogExpressionMapper extends BaseMapper<OfflineLogExpressionDO> {


}
