package com.nspace.group.module.logs.dal.dataobject.offlinelog;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DelayedOfflineLogDO {

    private String batchNum;

    private String logType;

    private Long tenantId;

    private String domain;

    private Long syncedCount;

    private Long unsyncedCount;

    /**
     * 补偿/延迟日志的起始查询时间
     */
    private LocalDateTime timestamp;

    /**
     * 补偿日志的最大记录时间
     */
    private LocalDateTime maxTimeStamp;

    private Integer lifeCycle;

    /**
     * 文件起始点位
     */
    private Integer startIndex = 0;
}
