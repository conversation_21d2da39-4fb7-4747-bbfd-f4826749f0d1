package com.nspace.group.module.logs.service.vendor;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.logs.convert.vendor.VendorCdnLogDetailConvert;
import com.nspace.group.module.logs.dal.dataobject.vendor.VendorCdnLogDetailDO;
import com.nspace.group.module.logs.dal.mapper.vendor.VendorCdnLogDetailMapper;
import com.nspace.group.module.logs.enums.VendorLogStatusEnum;
import com.nspace.group.module.logs.service.vendor.dto.VendorLogDetailDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 通用CDN第三方日志投递-投递明细服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-05-12 14:32:16
 */
@Service
@DS("doris_nspace_log")
public class VendorCdnLogDetailServiceImpl implements VendorCdnLogDetailService {

    @Resource
    private VendorCdnLogDetailMapper deliveryDetailMapper;

    @Override
    public List<VendorLogDetailDTO> getDetails(LocalDateTime timeLowerLimit, Integer limit) {
        LambdaQueryWrapperX<VendorCdnLogDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.select(VendorCdnLogDetailDO::getId, VendorCdnLogDetailDO::getLogTime,
                        VendorCdnLogDetailDO::getLogJson, VendorCdnLogDetailDO::getLogStatus)
                .ge(timeLowerLimit != null, VendorCdnLogDetailDO::getCurTimestamp, timeLowerLimit)
                .ne(VendorCdnLogDetailDO::getLogStatus, VendorLogStatusEnum.SUCCESS.getStatus())
                .orderByAsc(VendorCdnLogDetailDO::getCurTimestamp);

        Page<VendorCdnLogDetailDO> page = new Page<>(1, limit);
        List<VendorCdnLogDetailDO> records = deliveryDetailMapper.selectPage(page.setSearchCount(false), queryWrapper).getRecords();
        return VendorCdnLogDetailConvert.INSTANCE.getVendorLogDetailDTOList(records);
    }
}

