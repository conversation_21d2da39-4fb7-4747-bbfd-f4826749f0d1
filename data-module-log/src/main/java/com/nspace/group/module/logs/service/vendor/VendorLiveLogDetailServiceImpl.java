package com.nspace.group.module.logs.service.vendor;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.logs.convert.vendor.VendorLiveLogDetailConvert;
import com.nspace.group.module.logs.dal.dataobject.vendor.VendorLiveLogDetailDO;
import com.nspace.group.module.logs.dal.mapper.vendor.VendorLiveLogDetailMapper;
import com.nspace.group.module.logs.enums.VendorLogStatusEnum;
import com.nspace.group.module.logs.service.vendor.dto.VendorLogDetailDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 云直播第三方日志投递-投递明细服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-05-12 14:32:16
 */
@Service
@DS("doris_nspace_log")
public class VendorLiveLogDetailServiceImpl implements VendorLiveLogDetailService {

    @Resource
    private VendorLiveLogDetailMapper deliveryDetailMapper;

    @Override
    public List<VendorLogDetailDTO> getDetails(LocalDateTime timeLowerLimit, Integer limit) {
        LambdaQueryWrapperX<VendorLiveLogDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.select(VendorLiveLogDetailDO::getId, VendorLiveLogDetailDO::getLogTime,
                        VendorLiveLogDetailDO::getLogJson, VendorLiveLogDetailDO::getLogStatus)
                .ge(timeLowerLimit != null, VendorLiveLogDetailDO::getCurTimestamp, timeLowerLimit)
                .ne(VendorLiveLogDetailDO::getLogStatus, VendorLogStatusEnum.SUCCESS.getStatus())
                .orderByAsc(VendorLiveLogDetailDO::getCurTimestamp);

        Page<VendorLiveLogDetailDO> page = new Page<>(1, limit);
        List<VendorLiveLogDetailDO> records = deliveryDetailMapper.selectPage(page.setSearchCount(false), queryWrapper).getRecords();
        return VendorLiveLogDetailConvert.INSTANCE.getVendorLogDetailDTOList(records);
    }
}

