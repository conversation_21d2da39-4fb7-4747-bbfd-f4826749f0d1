package com.nspace.group.module.logs.enums;

import com.nspace.group.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum VendorLogStatusEnum implements IntArrayValuable {
    SUCCESS(1, "成功"),
    FAILED(2, "失败");

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String desc;

    public boolean isSelf(Integer status) {
        return isVendorLogStatus(status) && this.getStatus().equals(status);
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(VendorLogStatusEnum::getStatus).toArray();

    public static boolean isVendorLogStatus(Integer status) {
        return Arrays.stream(ARRAYS).boxed().anyMatch(value -> value.equals(status));
    }


    @Override
    public int[] array() {
        return ARRAYS;
    }

}
