package com.nspace.group.module.logs.service.offlinelog.cdn;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.CDNOfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.CDNOfflineLogInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogInfoDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.CDNOfflineLogDetailInfoMapper;
import com.nspace.group.module.logs.dal.mapper.offlinelog.CDNOfflineLogInfoMapper;
import com.nspace.group.module.logs.enums.OfflineStatusEnum;
import com.nspace.group.module.logs.service.offlinelog.OfflinePersistService;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

@Service("cdn-offline-persist")
public class CDNOfflineLogPersistServiceImpl implements OfflinePersistService {

    @Resource
    private CDNOfflineLogInfoMapper cdnOfflineLogMapper;

    @Resource
    private CDNOfflineLogDetailInfoMapper detailInfoMapper;


    @Override
    public void persist(OfflineLogInfoDO logDO) {
        if (logDO instanceof OfflineLogDetailInfoDO) {
            CDNOfflineLogDetailInfoDO detailDO = new CDNOfflineLogDetailInfoDO();
            BeanUtils.copyProperties(logDO, detailDO);
            if (logDO.getId() == null) {
                detailInfoMapper.insert(detailDO);
            } else {
                detailInfoMapper.updateById(detailDO);
            }
            logDO.setId(detailDO.getId());
        } else if (logDO != null) {
            CDNOfflineLogInfoDO infoDO = new CDNOfflineLogInfoDO();
            BeanUtils.copyProperties(logDO, infoDO);
            if (logDO.getId() == null) {
                cdnOfflineLogMapper.insert(infoDO);
            } else {
                cdnOfflineLogMapper.updateById(infoDO);
            }
            logDO.setId(infoDO.getId());
        }

    }

    @Override
    public void batchUpdate(OfflineLogDTO dto, String message, OfflineStatusEnum status, List<Long> detailIds) {
        LambdaUpdateWrapper<CDNOfflineLogInfoDO> wrapper = Wrappers.lambdaUpdate(CDNOfflineLogInfoDO.class)
                .set(CDNOfflineLogInfoDO::getStatus, status)
                .set(CDNOfflineLogInfoDO::getMessage, message)
                .set(CDNOfflineLogInfoDO::getUpdateTime, LocalDateTime.now())
                .in(CDNOfflineLogInfoDO::getId, dto.getCurrentIds());
        cdnOfflineLogMapper.update(wrapper);
        List<Long> historyIds = dto.getHistoryIds();
        if (dto.isRemoveHistory() && CollectionUtil.isNotEmpty(historyIds)) {
            cdnOfflineLogMapper.deleteBatchIds(historyIds);
        }
        if (CollectionUtil.isNotEmpty(detailIds)) {
            if (status == OfflineStatusEnum.FAILED) {
                LambdaUpdateWrapper<CDNOfflineLogDetailInfoDO> detailWrapper = Wrappers
                        .lambdaUpdate(CDNOfflineLogDetailInfoDO.class)
                        .in(CDNOfflineLogDetailInfoDO::getId, detailIds);
                detailWrapper.setSql("retry_count = retry_count + 1");
                detailInfoMapper.update(detailWrapper);
            } else if (status == OfflineStatusEnum.SUCCESS) {
                detailInfoMapper.deleteBatchIds(detailIds);
            }
        }
    }

    @Override
    public void batchUpdateDetails(OfflineLogDTO dto, String message, OfflineStatusEnum status) {
        if (dto.isRemoveHistory() && CollectionUtil.isNotEmpty(dto.getHistoryIds())) {
          detailInfoMapper.deleteBatchIds(dto.getHistoryIds());
        }
        LambdaUpdateWrapper<CDNOfflineLogDetailInfoDO> wrapper = Wrappers
                .lambdaUpdate(CDNOfflineLogDetailInfoDO.class)
                .set(CDNOfflineLogDetailInfoDO::getStatus, status)
                .set(CDNOfflineLogDetailInfoDO::getMessage, message)
                .set(CDNOfflineLogDetailInfoDO::getUpdateTime, LocalDateTime.now())
                .in(CDNOfflineLogDetailInfoDO::getId, dto.getCurrentIds());
        detailInfoMapper.update(wrapper);
        cdnOfflineLogMapper.recoverDetails(dto.getCurrentIds());
    }

    @Override
    public List<Long> find(String batchNum, Long tenantId, String domain) {
        LambdaQueryWrapper<CDNOfflineLogInfoDO> wrapper = Wrappers.lambdaQuery(CDNOfflineLogInfoDO.class)
                .eq(CDNOfflineLogInfoDO::getBatchNum, batchNum)
                .eq(CDNOfflineLogInfoDO::getTenantId, tenantId)
                .eq(CDNOfflineLogInfoDO::getDomain, domain);
        return cdnOfflineLogMapper.selectList(wrapper)
                .stream().map(OfflineLogInfoDO::getId).collect(Collectors.toList());

    }

    @Override
    public List<? extends OfflineLogDetailInfoDO> findDetails(String batchNum, Long tenantId, String domain) {
        LambdaQueryWrapper<CDNOfflineLogDetailInfoDO> wrapper = Wrappers.lambdaQuery(CDNOfflineLogDetailInfoDO.class)
                .eq(CDNOfflineLogDetailInfoDO::getBatchNum, batchNum)
                .eq(CDNOfflineLogDetailInfoDO::getTenantId, tenantId)
                .eq(CDNOfflineLogDetailInfoDO::getDomain, domain);
        return detailInfoMapper.selectList(wrapper);
    }

    @Override
    public int cleanExpiredLogs() {
        LocalDateTime startOfDay = LocalDateTime.now().with(LocalTime.MIN);
        QueryWrapper<CDNOfflineLogInfoDO> wrapper = Wrappers.query(CDNOfflineLogInfoDO.class)
                .isNotNull("lifecycle")
                .apply("create_time + CAST(lifecycle || ' days' AS interval) < {0}", startOfDay);
        return cdnOfflineLogMapper.delete(wrapper);
    }


}
