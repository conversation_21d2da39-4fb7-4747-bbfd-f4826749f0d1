package com.nspace.group.module.logs.service.offlinelog.dto;

import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO;
import com.nspace.group.module.logs.enums.ExportTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class OfflineLogDTO {

    private String batchNum;

    private String logType;

    /**
     * 导出类型,全量导出
     */
    private ExportTypeEnum exportType = ExportTypeEnum.FULL;

    private Long tenantId;

    private String domain;

    private LocalDateTime startTimestamp;

    private LocalDateTime maxTimeStamp;


    /**
     * 执行批次数量,如果 executeBatch 不为空
     */
    private Integer executeBatch;

    /**
     * 子分批数量
     */
    private Integer subBatch;

    /**
     *
     */
    private Integer retryCount = 0;

    /**
     * 是否需要刷新当前批次的日志
     */
    private Boolean refresh;

    /**
     * 是否需要全量同步
     */
    private Boolean syncAll;

    private Integer lifeCycle = 30;

    private Long totalCount = null;

    /**
     * 文件起始位置
     */
    private Integer startIndex = 0;


    //历史需要删除的数据
    private List<Long> historyIds;

    //是否删除历史数据
    private boolean removeHistory = false;

    //当前需要更新的数据
    private List<Long> currentIds;

    public Boolean isRefresh() {
        return refresh;
    }

    public Boolean isSyncAll() {
        return syncAll;
    }

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;


    //创建新对象,避免多线程影响
    public static OfflineLogDTO newDTO(OfflineLogDTO dto) {
        OfflineLogDTO result = new OfflineLogDTO();
        result.setBatchNum(dto.getBatchNum());
        result.setLogType(dto.getLogType());
        result.setTenantId(dto.getTenantId());
        result.setDomain(dto.getDomain());
        result.setExportType(dto.getExportType());
        result.setStartTimestamp(dto.getStartTimestamp());
        result.setMaxTimeStamp(dto.getMaxTimeStamp());
        result.setLifeCycle(dto.getLifeCycle());
        result.setRetryCount(dto.getRetryCount());
        result.setTotalCount(dto.getTotalCount());
        result.setRemoveHistory(dto.isRemoveHistory());
        result.setHistoryIds(dto.getHistoryIds());
        result.setStartIndex(dto.getStartIndex());
        return result;
    }

    public static OfflineLogDTO from(OfflineLogDO logDO) {
        OfflineLogDTO dto = new OfflineLogDTO();
        dto.setBatchNum(logDO.getBatchNum());
        dto.setBatchNum(logDO.getBatchNum());
        dto.setLogType(logDO.getLogType());
        if (logDO.getExportType() != null) {
            dto.setExportType(logDO.getExportType());
        }
        dto.setTenantId(logDO.getTenantId());
        dto.setDomain(logDO.getDomain());
        dto.setStartTimestamp(logDO.getStartTimestamp());
        dto.setExecuteBatch(logDO.getExecuteBatch());
        dto.setLifeCycle(logDO.getLifeCycle());
        if (logDO.getRetryCount() != null) {
            dto.setRetryCount(logDO.getRetryCount());
        }
        return dto;
    }
}
