package com.nspace.group.module.logs.service.delivery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.logs.convert.delivery.DeliveryLiveLogDetailConvert;
import com.nspace.group.module.logs.dal.dataobject.delivery.DeliveryLiveLogDetailDO;
import com.nspace.group.module.logs.dal.mapper.delivery.DeliveryLiveLogDetailMapper;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 直播CDN日志投递-投递明细服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-05-12 14:32:16
 */
@Service
@DS("doris_nspace_log")
public class DeliveryLiveLogDetailServiceImpl implements DeliveryLiveLogDetailService {

    @Resource
    private DeliveryLiveLogDetailMapper deliveryDetailMapper;

    @Override
    public List<DeliveryLogDetailDTO> getDetails(String targetType, LocalDateTime timeLowerLimit, Integer limit) {
        LambdaQueryWrapperX<DeliveryLiveLogDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.select(DeliveryLiveLogDetailDO::getId, DeliveryLiveLogDetailDO::getLogTime,
                        DeliveryLiveLogDetailDO::getDeliveryVendor, DeliveryLiveLogDetailDO::getLogJson, DeliveryLiveLogDetailDO::getLogStatus)
                .ge(timeLowerLimit != null, DeliveryLiveLogDetailDO::getCurTimestamp, timeLowerLimit)
                .eq(DeliveryLiveLogDetailDO::getDeliveryVendor, LogDeliveryTargetEnum.valueOf(targetType).getVendorCode())
                .ne(DeliveryLiveLogDetailDO::getLogStatus, LogDeliveryStatusEnum.SUCCESS.getStatus())
                .orderByDesc(DeliveryLiveLogDetailDO::getLogTime);

        Page<DeliveryLiveLogDetailDO> page = new Page<>(1, limit);
        List<DeliveryLiveLogDetailDO> records = deliveryDetailMapper.selectPage(page.setSearchCount(false), queryWrapper).getRecords();
        return DeliveryLiveLogDetailConvert.INSTANCE.getDeliveryLogDetailDTOList(records);
    }
}

