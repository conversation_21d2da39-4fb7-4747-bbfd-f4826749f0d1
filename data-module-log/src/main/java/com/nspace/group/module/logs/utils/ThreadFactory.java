package com.nspace.group.module.logs.utils;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import com.nspace.group.module.logs.config.logs.OfflineLogConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.*;

@Slf4j
@Component("offline-thread-factory")
public class ThreadFactory implements ApplicationListener<ContextRefreshedEvent> {

    @Resource
    private OfflineLogConfiguration configuration;


    public ExecutorService buildExecutor(String spec, String namePrefix) {
        String[] parts = spec.split(",");
        int core = Integer.parseInt(parts[0]);
        int max = Integer.parseInt(parts[1]);
        int queue = Integer.parseInt(parts[2]);
        long keepAlive = parts.length > 3 && !parts[3].isEmpty() ? Long.parseLong(parts[3]) : 60L;
        BlockingQueue<Runnable> workQueue = queue <= 0
                ? new SynchronousQueue<>()
                : new LinkedBlockingQueue<>(queue);
        return ExecutorBuilder.create()
                .setCorePoolSize(core)
                .setMaxPoolSize(max)
                .setWorkQueue(workQueue)
                .setThreadFactory(ThreadFactoryBuilder.create().setNamePrefix(namePrefix).build())
                .setHandler(new ThreadPoolExecutor.CallerRunsPolicy())
                .setKeepAliveTime(keepAlive, TimeUnit.SECONDS)
                .build();
    }


    /**
     * doris 数据查询线程
     */
    private static ExecutorService FETCH_EXECUTORS;

    /**
     * 压缩线程池
     */
    private static ExecutorService COMPRESSOR_EXECUTORS;

    /**
     * minio上传 文件合并 线程池
     */
    private static ExecutorService IO_EXECUTORS;

    public static ExecutorService getFetchExecutors() {
        return FETCH_EXECUTORS;
    }

    public static ExecutorService getCompressorExecutors() {
        return COMPRESSOR_EXECUTORS;
    }

    public static ExecutorService getIOExecutors() {
        return IO_EXECUTORS;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        FETCH_EXECUTORS = buildExecutor(configuration.getFetcherSpec(), "doris-data-fetcher-");
        COMPRESSOR_EXECUTORS = buildExecutor(configuration.getCompressorSpec(), "gzip-compressor-");
        IO_EXECUTORS = buildExecutor(configuration.getIoExecutorSpec(), "io-executor-");
        log.info("离线导出线程池创建完成");
    }
}
