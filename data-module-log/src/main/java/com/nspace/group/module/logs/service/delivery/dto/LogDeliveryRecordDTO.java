package com.nspace.group.module.logs.service.delivery.dto;

import java.time.LocalDateTime;
import java.util.StringJoiner;

/**
 * LogDeliveryRecordDTO
 *
 * <AUTHOR>
 */
public class LogDeliveryRecordDTO {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 业务类型,云直播：LSS 通用CDN：CDN
     */
    private String bizType;

    /**
     * 日志开始时间
     */
    private LocalDateTime logStartTime;

    /**
     * 日志结束时间
     */
    private LocalDateTime logEndTime;

    /**
     * 日志开始ID
     */
    private Long logStartId;

    /**
     * 日志结束ID
     */
    private Long logEndId;

    /**
     * 日志类型
     */
    private String logType;

    /**
     * 失败后重试次数，默认值0
     */
    private Integer retryCount;

    /**
     * 投递条数
     */
    private Long deliveryCount;

    /**
     * 日志投递状态 0:初始值，1:成功，2:失败，3:重试
     */
    private Integer deliveryStatus;

    /**
     * 日志投递返回结果
     */
    private String deliveryResult;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public LocalDateTime getLogStartTime() {
        return logStartTime;
    }

    public void setLogStartTime(LocalDateTime logStartTime) {
        this.logStartTime = logStartTime;
    }

    public LocalDateTime getLogEndTime() {
        return logEndTime;
    }

    public void setLogEndTime(LocalDateTime logEndTime) {
        this.logEndTime = logEndTime;
    }

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Long getDeliveryCount() {
        return deliveryCount;
    }

    public void setDeliveryCount(Long deliveryCount) {
        this.deliveryCount = deliveryCount;
    }

    public Integer getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(Integer deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String getDeliveryResult() {
        return deliveryResult;
    }

    public void setDeliveryResult(String deliveryResult) {
        this.deliveryResult = deliveryResult;
    }

    public Long getLogStartId() {
        return logStartId;
    }

    public void setLogStartId(Long logStartId) {
        this.logStartId = logStartId;
    }

    public Long getLogEndId() {
        return logEndId;
    }

    public void setLogEndId(Long logEndId) {
        this.logEndId = logEndId;
    }

    @Override
    public String toString() {
        return new StringJoiner(",")
                .add("tenantId=" + tenantId)
                .add("domain=" + domain)
                .add("bizType=" + bizType)
                .add("logEndTime=" + logEndTime)
                .add("logType=" + logType)
                .toString();
    }
}
