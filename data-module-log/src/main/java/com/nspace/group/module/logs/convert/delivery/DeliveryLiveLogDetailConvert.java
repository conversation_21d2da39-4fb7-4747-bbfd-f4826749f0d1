package com.nspace.group.module.logs.convert.delivery;

import com.nspace.group.module.logs.dal.dataobject.delivery.DeliveryLiveLogDetailDO;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version :DeliveryLiveLogDetailConvert.java, v0.1 2025年05月12日 10:21 zhangxin Exp
 */
@Mapper
public interface DeliveryLiveLogDetailConvert {

    DeliveryLiveLogDetailConvert INSTANCE = Mappers.getMapper(DeliveryLiveLogDetailConvert.class);

    DeliveryLogDetailDTO getDeliveryLogDetailDTO(DeliveryLiveLogDetailDO record);

    List<DeliveryLogDetailDTO> getDeliveryLogDetailDTOList(List<DeliveryLiveLogDetailDO> records);
}
