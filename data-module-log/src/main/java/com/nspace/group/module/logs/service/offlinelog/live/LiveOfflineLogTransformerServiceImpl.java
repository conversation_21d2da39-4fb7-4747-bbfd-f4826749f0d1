package com.nspace.group.module.logs.service.offlinelog.live;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.nspace.group.module.logs.service.offlinelog.AbstractOfflineLogTransformerService;
import com.nspace.group.module.logs.utils.Constant;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static com.nspace.group.module.logs.utils.Constant.LIVE_FIELDS_SIZE;
import static com.nspace.group.module.logs.utils.Constant.LIVE_NUMBER_FIELDS;

@Service("live-offline-transformer")
public class LiveOfflineLogTransformerServiceImpl extends AbstractOfflineLogTransformerService {

    private final Map<Integer, String> defaultExpressions;

    {
        defaultExpressions = new HashMap<>();
//        defaultExpressions.put(1, "ISO8061(param[1])");
//        defaultExpressions.put(18, "ISO8061(param[18])");
    }


    @Override
    public Map<Integer, Expression> getDefaultExpressions() {
        return defaultExpressions.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey
                , item -> AviatorEvaluator.compile(item.getValue(), true)));
    }

    @Override
    @SneakyThrows
    public String[] transform0(ResultSet resultSet) {
        String[] result = new String[LIVE_FIELDS_SIZE + 1];
        for (int i = 0; i < LIVE_FIELDS_SIZE; i++) {
            String value = resultSet.getString(i + 1);
            if (value != null && !value.isEmpty()) {
                result[i] = value.replace("\r", "")
                        .replace("\n", "").replace("\r\n", "");
            } else {
                result[i] = LIVE_NUMBER_FIELDS.contains(i) ? "0" : "-";
            }
        }
        int round = resultSet.getInt(Constant.LIVE_ROUND_INDEX);
        int end = resultSet.getInt(Constant.LIVE_END_INDEX);
        /*
         * end=1 就是结束了
         * round=1 就是刚开始
         * 其他场景end=0 and round!=1
         */
        if (round != 1 && end == 0) {
            result[LIVE_FIELDS_SIZE] = "0";
        } else if (round == 1) {
            result[LIVE_FIELDS_SIZE] = "1";
        } else if (end == 1) {
            result[LIVE_FIELDS_SIZE] = "-1";
        }
        return result;
    }

}
