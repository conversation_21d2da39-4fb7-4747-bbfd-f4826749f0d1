package com.nspace.group.module.logs.dal.dataobject.delivery;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :DeliveryCdnLogDetailDO.java, v0.1 2025年05月12日 10:58 zhangxin Exp
 */
@TableName(value = "delivery_cdn_log_fail_detail")
public class DeliveryCdnLogDetailDO {

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * log日志时间
     */
    private LocalDateTime logTime;

    /**
     * 日志对应的json数据信息
     */
    private String logJson;

    /**
     * 日志接收状态 0:失败 1:成功
     */
    private Integer logStatus;

    /**
     * 第三方代码
     */
    private String deliveryVendor;

    /**
     * 投递次数
     */
    private Integer deliveryTimes;

    /**
     * 成功投递时间
     */
    private LocalDateTime deliveryTimestamp;

    /**
     * 数据写入时间
     */
    private LocalDateTime curTimestamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDateTime getLogTime() {
        return logTime;
    }

    public void setLogTime(LocalDateTime logTime) {
        this.logTime = logTime;
    }

    public String getLogJson() {
        return logJson;
    }

    public void setLogJson(String logJson) {
        this.logJson = logJson;
    }

    public Integer getLogStatus() {
        return logStatus;
    }

    public void setLogStatus(Integer logStatus) {
        this.logStatus = logStatus;
    }

    public String getDeliveryVendor() {
        return deliveryVendor;
    }

    public void setDeliveryVendor(String deliveryVendor) {
        this.deliveryVendor = deliveryVendor;
    }

    public Integer getDeliveryTimes() {
        return deliveryTimes;
    }

    public void setDeliveryTimes(Integer deliveryTimes) {
        this.deliveryTimes = deliveryTimes;
    }

    public LocalDateTime getDeliveryTimestamp() {
        return deliveryTimestamp;
    }

    public void setDeliveryTimestamp(LocalDateTime deliveryTimestamp) {
        this.deliveryTimestamp = deliveryTimestamp;
    }

    public LocalDateTime getCurTimestamp() {
        return curTimestamp;
    }

    public void setCurTimestamp(LocalDateTime curTimestamp) {
        this.curTimestamp = curTimestamp;
    }
}
