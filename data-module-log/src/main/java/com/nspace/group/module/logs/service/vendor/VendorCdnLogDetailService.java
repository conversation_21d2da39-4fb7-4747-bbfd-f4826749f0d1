package com.nspace.group.module.logs.service.vendor;

import com.nspace.group.module.logs.service.vendor.dto.VendorLogDetailDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 通用CDN第三方日志投递-投递明细服务接口
 *
 * <AUTHOR>
 * @since 2025-05-12 14:32:16
 */
public interface VendorCdnLogDetailService {

    /**
     * 获取第三方投递日志明细列表
     *
     * @param timeLowerLimit 创建时间下限
     * @param limit          批次限制
     * @return List<VendorLogDetailDTO>
     */
    List<VendorLogDetailDTO> getDetails(LocalDateTime timeLowerLimit, Integer limit);

}

