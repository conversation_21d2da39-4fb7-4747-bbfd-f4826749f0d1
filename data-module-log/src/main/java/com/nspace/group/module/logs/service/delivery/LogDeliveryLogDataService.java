package com.nspace.group.module.logs.service.delivery;

import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.List;
import java.util.Map;

/**
 * 日志投递数据处理 Service 接口
 *
 * <AUTHOR>
 */
public interface LogDeliveryLogDataService {

    /**
     * 序列化
     *
     * @param logDataList 日志数据
     * @return String
     */
    String serialize(List<Map<String, Object>> logDataList) throws JsonProcessingException;


    /**
     * 压缩
     *
     * @param logDataList 日志数据
     * @return byte[]
     */
    byte[] compress(List<Map<String, Object>> logDataList) throws JsonProcessingException;

    /**
     * 压缩字符串
     *
     * @param str 字符串
     * @return byte[]
     */
    byte[] compress(String str);
}