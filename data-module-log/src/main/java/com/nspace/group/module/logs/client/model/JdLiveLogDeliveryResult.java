package com.nspace.group.module.logs.client.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

/**
 * JdLiveLogDeliveryResult
 *
 * <AUTHOR>
 */
public class JdLiveLogDeliveryResult {

    /**
     * 响应代码
     */
    @JsonProperty("Code")
    private Integer code;

    /**
     * 请求ID
     */
    @JsonProperty("RequestID")
    private String requestId;
    /**
     * 响应消息
     */
    @JsonProperty("Message")
    private String msg;

    private Integer respCode;

    private String respMsg;

    private Map<String, List<String>> respHeaders;

    private String respBody;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getRespCode() {
        return respCode;
    }

    public void setRespCode(Integer respCode) {
        this.respCode = respCode;
    }

    public Map<String, List<String>> getRespHeaders() {
        return respHeaders;
    }

    public void setRespHeaders(Map<String, List<String>> respHeaders) {
        this.respHeaders = respHeaders;
    }

    public String getRespBody() {
        return respBody;
    }

    public void setRespBody(String respBody) {
        this.respBody = respBody;
    }

    public String getRespMsg() {
        return respMsg;
    }

    public void setRespMsg(String respMsg) {
        this.respMsg = respMsg;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @JsonIgnore
    public boolean isDeliverySuccessful() {
        return this.code != null && this.code.equals(200);
    }
}
