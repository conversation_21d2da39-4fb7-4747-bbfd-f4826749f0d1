package com.nspace.group.module.logs.biz.vendor;

import com.nspace.group.module.logs.service.vendor.dto.VendorLogDetailDTO;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 第三方日志投递明细接口
 *
 * <AUTHOR>
 * @since 2025-05-12 14:32:16
 */
public interface VendorLogDetailService {

    List<VendorLogDetailDTO> getDetails(String bizType, LocalDateTime timeLowerLimit, Integer limit);

    /**
     * 更新第三方日志投递明细列表
     *
     * @param bizType    业务类型
     * @param logDetails 第三方日志投递明细列表
     */
    void updateMany(String bizType, Collection<VendorLogDetailDTO> logDetails);

}
