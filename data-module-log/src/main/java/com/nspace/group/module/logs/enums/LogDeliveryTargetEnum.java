package com.nspace.group.module.logs.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum LogDeliveryTargetEnum {
    CDN_JD("CDN_JD", "jd", "通用CDN-京东"),
    LIVE_JD("LIVE_JD", "jd", "直播CDN-京东"),
    CDN_ALI("CDN_ALI", "ali", "通用CDN-阿里");

    /**
     * 名称
     */
    private final String name;

    /**
     * 三方代码
     */
    private final String vendorCode;

    /**
     * 描述
     */
    private final String desc;

    public boolean isSelf(String targetType) {
        return isLogDeliveryTarget(targetType) && this.getName().equals(targetType);
    }

    public static boolean isLogDeliveryTarget(String targetType) {
        return Arrays.stream(values()).map(LogDeliveryTargetEnum::getName).anyMatch(value -> value.equals(targetType));
    }

}
