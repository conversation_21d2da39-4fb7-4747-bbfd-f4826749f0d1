package com.nspace.group.module.logs.client.impl.factory;

import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.infra.client.factory.ApiClientFactory;

public class JdApiClientFactory implements ApiClientFactory {

    public static final JdApiClientFactory INSTANCE = new JdApiClientFactory();

    public ApiClient createApiClient(String apiBasePath, Authentication authentication) {
        return new ApiClient(apiBasePath, authentication);
    }

    public ApiClient createApiClient(String apiBasePath) {
        return new ApiClient(apiBasePath);
    }

}
