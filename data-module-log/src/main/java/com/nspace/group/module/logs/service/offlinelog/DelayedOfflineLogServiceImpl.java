package com.nspace.group.module.logs.service.offlinelog;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DomainStatDTO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.OfflineLogMapper;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import com.nspace.group.module.logs.utils.DTF;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DelayedOfflineLogServiceImpl implements DelayedOfflineLogService {
    @Resource
    private OfflineLogFactory offlineLogFactory;


    @Override
    public List<OfflineLogDTO> getDelayedLogs(String logType, LocalDateTime startTime, LocalDateTime endTime) {

        OfflineLogMapper offlineLogMapper = offlineLogFactory.getOfflineLogMapper(logType);
        List<String> batches = new ArrayList<>();
        LocalDateTime start = startTime;
        while (!start.isAfter(endTime)) {
            batches.add(start.format(DTF.yyyyMMddHH));
            start = start.plusHours(1);
        }
        List<DelayedOfflineLogDO> delayedLogs = offlineLogMapper.getHistoryLogs(batches);

        Map<Tuple2<String, String>, DelayedOfflineLogDO> syncedMap = delayedLogs.stream()
                .collect(Collectors.toMap(item -> Tuple.of(item.getBatchNum(), item.getDomain())
                        , Function.identity(), (v1, v2) -> v1));
        List<OfflineLogDO> dos = offlineLogMapper.selectByCurrent();
        dos.forEach(item -> {
            String domain = item.getDomain();
            batches.forEach(batch -> {
                Tuple2<String, String> key = Tuple.of(batch, domain);
                if (!syncedMap.containsKey(key)) {
                    DelayedOfflineLogDO delayedLogDO = new DelayedOfflineLogDO();
                    delayedLogDO.setBatchNum(batch);
                    delayedLogDO.setTenantId(item.getTenantId());
                    delayedLogDO.setDomain(domain);
                    delayedLogDO.setMaxTimeStamp(DTF.MIN);
                    delayedLogDO.setSyncedCount(0L);
                    delayedLogDO.setLifeCycle(item.getLifeCycle());
                    delayedLogs.add(delayedLogDO);
                    syncedMap.put(key, delayedLogDO);
                }
            });
        });
        if (CollUtil.isEmpty(delayedLogs)) {
            return Collections.emptyList();
        }
        //移除这段时间内没有数据的域名,减少查询的次数
        Set<String> domains = new HashSet<>();
        syncedMap.forEach((key, value) -> {
            domains.add(key._2);
        });
        Map<String, Long> resultMap = new HashMap<>();
        List<List<String>> partition = ListUtil.partition(new ArrayList<>(domains), 100);
        partition.forEach(domain -> {
            if (CollectionUtil.isNotEmpty(domain)) {
                List<DomainStatDTO> domainCountDOS = offlineLogMapper.getDomainStat(startTime, endTime, domain, null);
                domainCountDOS.forEach(item -> {
                    resultMap.put(item.getDomain(), item.getTotalCount());
                });
            }
        });
        delayedLogs.removeIf(item -> !resultMap.containsKey(item.getDomain()));
        List<DelayedOfflineLogDO> odsDelayedLogs = new ArrayList<>();
        ListUtil.partition(delayedLogs, 100)
                .forEach(item -> {
                    odsDelayedLogs.addAll(offlineLogMapper.selectDelayedLogs(startTime, endTime, item));
                });
        //将 doris 的数据和 已经同步的数据对比
        return odsDelayedLogs.stream()
                .filter(item -> syncedMap.containsKey(Tuple.of(item.getBatchNum(), item.getDomain())))
                .map(item -> {
                    OfflineLogDTO dto = new OfflineLogDTO();
                    DelayedOfflineLogDO syncDO = syncedMap.get(Tuple.of(item.getBatchNum(), item.getDomain()));
                    dto.setBatchNum(item.getBatchNum());
                    dto.setLogType(logType);
                    dto.setTenantId(syncDO.getTenantId());
                    dto.setDomain(syncDO.getDomain());
                    dto.setLifeCycle(syncDO.getLifeCycle());
                    dto.setMaxTimeStamp(item.getMaxTimeStamp());
                    //如果从未同步过,或者同步的数据不完成,则进行全量同步
                    if (!Objects.equals(syncDO.getSyncedCount(), item.getSyncedCount()) || syncDO.getSyncedCount() == 0) {
                        dto.setStartIndex(0);
                        dto.setSyncAll(Boolean.TRUE);
                        long totalCount = NumberUtil.add(item.getSyncedCount(), item.getUnsyncedCount()).longValue();
                        log.info("数据不一致,全量同步当前批次:{}-{},数据差异:{} 对比 {}", item.getBatchNum(), item.getDomain()
                                , syncDO.getSyncedCount(), totalCount);
                    } else if (item.getUnsyncedCount() > 0) {
                        dto.setStartIndex(syncDO.getStartIndex());
                        dto.setStartTimestamp(syncDO.getTimestamp());
                        dto.setRefresh(Boolean.TRUE);
                        log.info("数据不一致,增量同步当前批次:{}-{},数据差异:{}", item.getBatchNum(), item.getDomain(), item.getUnsyncedCount());
                    }
                    return dto;
                })
                .collect(Collectors.toList());
    }
}
