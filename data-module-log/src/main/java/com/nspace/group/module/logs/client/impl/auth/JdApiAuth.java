package com.nspace.group.module.logs.client.impl.auth;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.nspace.group.module.infra.client.auth.Authentication;
import lombok.Getter;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

@Getter
public class JdApiAuth implements Authentication {

    // 请求路径
    private final String path;
    // 双方约定的唯一标识
    private final String privateKey;
    // ak
    private final String accessKey;

    public JdApiAuth(String path, String privateKey, String accessKey) {
        if (StrUtil.isBlank(path)) {
            throw new RuntimeException("JdApiAuth_constructor,path_is_blank");
        }
        if (StrUtil.isBlank(privateKey)) {
            throw new RuntimeException("JdApiAuth_constructor,privateKey_is_blank");
        }
        if (StrUtil.isBlank(accessKey)) {
            throw new RuntimeException("JdApiAuth_constructor,accessKey_is_blank");
        }
        this.path = path;
        this.privateKey = privateKey;
        this.accessKey = accessKey;
    }

    public String generateAuthKey() {
        // 10 位整数，发送时间 + 设定有效时间
        String timestamp = String.valueOf(Instant.now().plus(5L, ChronoUnit.MINUTES).getEpochSecond());

        // 随机数
        String rand = RandomUtil.randomNumbers(3);
        String md5Hash = DigestUtil.md5Hex(String.join(StringPool.DASH,
                this.path, timestamp, rand, this.privateKey));
        return String.join(StringPool.DASH, timestamp, rand, md5Hash);
    }

}
