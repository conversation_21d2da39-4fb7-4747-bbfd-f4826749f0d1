package com.nspace.group.module.logs.client.api;

import com.google.common.reflect.TypeToken;
import com.nspace.group.module.infra.client.*;
import com.nspace.group.module.logs.client.impl.auth.JdApiAuth;
import com.nspace.group.module.logs.client.model.JdLiveLogDeliveryResult;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JdLogDeliveryApi {
    private ApiClient localVarApiClient;

    public JdLogDeliveryApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    /**
     * 为 postLogs 构建 Call
     *
     * @param logsInBytes 日志数据
     * @param _callback   Callback for upload/download progress
     * @return 待执行的 Call
     * @throws ApiException 请求体序列化失败抛出
     */
    public okhttp3.Call postLogsCall(byte[] logsInBytes, final ApiCallback _callback) throws ApiException {
        Object localVarPostBody = logsInBytes;

        // create path and map variables
        JdApiAuth authentication = (JdApiAuth) localVarApiClient.getAuthentication();
        String localVarPath = authentication.getPath();

        List<Pair> localVarQueryParams = new ArrayList<>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<>();
        Map<String, String> localVarHeaderParams = new HashMap<>();
        Map<String, String> localVarCookieParams = new HashMap<>();
        Map<String, Object> localVarFormParams = new HashMap<>();


        localVarQueryParams.addAll(localVarApiClient.parameterToPair("auth_key", authentication.generateAuthKey()));
        localVarQueryParams.addAll(localVarApiClient.parameterToPair("access_key", authentication.getAccessKey()));


        String localVarAccept = localVarApiClient.selectHeaderAccept(new String[]{"application/json"});
        localVarHeaderParams.put("Accept", localVarAccept);
        String localVarContentType = localVarApiClient.selectHeaderContentType(new String[]{"application/octet-stream"});
        localVarHeaderParams.put("Content-Type", localVarContentType);

        localVarHeaderParams.put("access_key", authentication.getAccessKey());
        localVarHeaderParams.put("Content-Encoding", "gzip");

        return localVarApiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call postLogsValidateBeforeCall(byte[] logsInBytes, final ApiCallback _callback) throws ApiException {


        okhttp3.Call localVarCall = postLogsCall(logsInBytes, _callback);
        return localVarCall;

    }

    /**
     * 获取 log detail
     *
     * @param logsInBytes 日志数据
     * @return DAGCollection
     * @throws ApiException API请求失败或响应体反序列化失败抛出
     */
    public JdLiveLogDeliveryResult postLogs(byte[] logsInBytes) throws ApiException {
        ApiResponse<JdLiveLogDeliveryResult> localVarResp = postLogsWithHttpInfo(logsInBytes);
        return localVarResp.getData();
    }

    public ApiResponse<JdLiveLogDeliveryResult> postLogsWithHttpInfo(byte[] logsInBytes) throws ApiException {
        okhttp3.Call localVarCall = postLogsValidateBeforeCall(logsInBytes, null);
        Type localVarReturnType = new TypeToken<JdLiveLogDeliveryResult>() {
        }.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * 获取 log detail (异步)
     *
     * @param logsInBytes 日志数据
     * @param _callback   The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException Api请求失败抛出，例如：请求体序列化失败
     */
    public okhttp3.Call postLogsAsync(byte[] logsInBytes, final ApiCallback<JdLiveLogDeliveryResult> _callback) throws ApiException {

        okhttp3.Call localVarCall = postLogsValidateBeforeCall(logsInBytes, _callback);
        Type localVarReturnType = new TypeToken<JdLiveLogDeliveryResult>() {
        }.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
