package com.nspace.group.module.logs.dal.dataobject.offlinelog;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableName;

@TableName(value = "cdn_offline_log_detail_info")
@KeySequence("cdn_offline_log_detail_info_id_seq")  // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
public class CDNOfflineLogDetailInfoDO extends OfflineLogDetailInfoDO {

}
