package com.nspace.group.module.logs.service.delivery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.logs.convert.delivery.DeliveryCdnLogDetailConvert;
import com.nspace.group.module.logs.dal.dataobject.delivery.DeliveryCdnLogDetailDO;
import com.nspace.group.module.logs.dal.mapper.delivery.DeliveryCdnLogDetailMapper;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.enums.LogDeliveryTargetEnum;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 通用CDN日志投递-投递明细服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-05-12 14:32:16
 */
@Service
@DS("doris_nspace_log")
public class DeliveryCdnLogDetailServiceImpl implements DeliveryCdnLogDetailService {

    @Resource
    private DeliveryCdnLogDetailMapper deliveryDetailMapper;

    @Override
    public List<DeliveryLogDetailDTO> getDetails(String targetType, LocalDateTime timeLowerLimit, Integer limit) {
        LambdaQueryWrapperX<DeliveryCdnLogDetailDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.select(DeliveryCdnLogDetailDO::getId, DeliveryCdnLogDetailDO::getLogTime,
                        DeliveryCdnLogDetailDO::getDeliveryVendor, DeliveryCdnLogDetailDO::getLogJson, DeliveryCdnLogDetailDO::getLogStatus)
                .ge(timeLowerLimit != null, DeliveryCdnLogDetailDO::getCurTimestamp, timeLowerLimit)
                .eq(DeliveryCdnLogDetailDO::getDeliveryVendor, LogDeliveryTargetEnum.valueOf(targetType).getVendorCode())
                .ne(DeliveryCdnLogDetailDO::getLogStatus, LogDeliveryStatusEnum.SUCCESS.getStatus())
                .orderByDesc(DeliveryCdnLogDetailDO::getLogTime);

        Page<DeliveryCdnLogDetailDO> page = new Page<>(1, limit);
        List<DeliveryCdnLogDetailDO> records = deliveryDetailMapper.selectPage(page.setSearchCount(false), queryWrapper).getRecords();
        return DeliveryCdnLogDetailConvert.INSTANCE.getDeliveryLogDetailDTOList(records);
    }
}

