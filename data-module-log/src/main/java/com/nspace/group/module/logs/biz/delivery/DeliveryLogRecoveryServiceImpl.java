package com.nspace.group.module.logs.biz.delivery;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.nspace.group.module.infra.design.chain.LogHandler;
import com.nspace.group.module.logs.biz.delivery.context.JdLiveRecoveryContext;
import com.nspace.group.module.logs.biz.delivery.strategy.composition.JdLiveLogRecovery;
import com.nspace.group.module.logs.convert.delivery.LogDeliveryConvert;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogRecoveryConfigDTO;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service(value = "deliveryLogRecoveryService")
public class DeliveryLogRecoveryServiceImpl implements DeliveryLogRecoveryService {

    @Resource
    private JdLiveLogRecovery jdLiveLogRecovery;

    @Override
    @Timed(value = "jd.live.log.recovery", description = "JD云直播失败日志补发计时")
    public void recoverJdLiveLogs(DeliveryLogRecoveryConfigDTO configDTO) {
        String targetType = configDTO.getTargetType();

        try {
            LogHandler logHandler = jdLiveLogRecovery.composeChain();
            JdLiveRecoveryContext logContext = LogDeliveryConvert.INSTANCE.getJdLiveRecoveryContext(configDTO);
            logHandler.handle(logContext);
        } catch (Exception e) {
            log.error("recoverJdLiveLogs,unknown_exception,target_type={},error={}",
                    targetType, ExceptionUtil.getRootCauseMessage(e));
        }
    }
}
