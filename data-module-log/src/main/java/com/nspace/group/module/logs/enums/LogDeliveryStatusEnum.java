package com.nspace.group.module.logs.enums;

import com.nspace.group.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum LogDeliveryStatusEnum implements IntArrayValuable {
    INIT(0, "初始"),
    SUCCESS(1, "成功"),
    FAILED(2, "失败"),
    RETRY(3, "重试"),
    DELAYED(4, "延迟补传"),
    STALE(-1, "过期");

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String desc;

    public boolean isSelf(Integer status) {
        return isLogDeliveryStatus(status) && this.getStatus().equals(status);
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(LogDeliveryStatusEnum::getStatus).toArray();

    public static boolean isLogDeliveryStatus(Integer status) {
        return Arrays.stream(ARRAYS).boxed().anyMatch(value -> value.equals(status));
    }


    @Override
    public int[] array() {
        return ARRAYS;
    }

}
