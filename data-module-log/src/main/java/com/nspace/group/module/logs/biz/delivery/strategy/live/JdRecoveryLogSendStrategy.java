package com.nspace.group.module.logs.biz.delivery.strategy.live;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.Lists;
import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import com.nspace.group.framework.common.util.json.JsonUtils;
import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.ApiException;
import com.nspace.group.module.infra.client.auth.Authentication;
import com.nspace.group.module.logs.biz.delivery.DeliveryLogDetailService;
import com.nspace.group.module.logs.biz.delivery.context.JdLiveRecoveryContext;
import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.infra.design.strategy.LogSendStrategy;
import com.nspace.group.module.logs.client.api.JdLogDeliveryApi;
import com.nspace.group.module.logs.client.impl.auth.JdApiAuth;
import com.nspace.group.module.logs.client.impl.factory.JdApiClientFactory;
import com.nspace.group.module.logs.client.model.JdLiveLogDeliveryResult;
import com.nspace.group.module.logs.convert.delivery.LiveCdnLogConvert;
import com.nspace.group.module.logs.enums.LogDeliveryStatusEnum;
import com.nspace.group.module.logs.service.delivery.LogDeliveryLogDataService;
import com.nspace.group.module.logs.service.delivery.dto.DeliveryLogDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component("jdLiveRecoveryLogSendStrategy")
public class JdRecoveryLogSendStrategy implements LogSendStrategy {

    @Resource(name = "deliveryThreadPool")
    private ThreadPoolTaskExecutor executorService;

    @Resource
    private DeliveryLogDetailService deliveryLogDetailService;

    @Resource
    private LogDeliveryLogDataService logDeliveryLogDataService;

    @Override
    public boolean send(LogContext logContext) {
        JdLiveRecoveryContext context = (JdLiveRecoveryContext) logContext;
        String targetType = context.getTargetType();
        Integer logLimit = context.getLogLimit();
        List<DeliveryLogDetailDTO> logDetails = context.getLogDetails();

        if (logDetails.isEmpty()) {
            log.info("no_logs_to_recover,target_type={}", targetType);
            return true;
        }
        logDetails.forEach(logDetail -> logDetail.setDeliveryTimes(context.getCurRetryCount()));

        AtomicInteger totalTaskCount = new AtomicInteger(0);
        ExecutorCompletionService<Boolean> completionService = new ExecutorCompletionService<>(executorService);

        Lists.partition(logDetails, logLimit).forEach(subLogDetails -> {
            completionService.submit(() -> send(context, subLogDetails));
            totalTaskCount.getAndIncrement();
        });
        Set<Boolean> sendResults = new HashSet<>();
        while (totalTaskCount.getAndDecrement() > 0) {
            try {
                Boolean result = completionService.take().get();
                sendResults.add(result);
            } catch (Exception e) {
                log.error("send,unknown_exception,error={}", ExceptionUtil.getRootCauseMessage(e));
                sendResults.add(false);
            }
        }
        return !sendResults.contains(false);
    }

    public boolean send(JdLiveRecoveryContext context, List<DeliveryLogDetailDTO> logDetails) {
        String apiBaseUrl = context.getApiBaseUrl();
        String apiPath = context.getApiPath();
        Integer curRetryCount = context.getCurRetryCount();

        boolean result = false;

        try {
            String jsonArrStr = logDetails.stream()
                    .map(DeliveryLogDetailDTO::getLogJson)
                    .collect(Collectors.joining(StringPool.COMMA, StringPool.LEFT_SQ_BRACKET, StringPool.RIGHT_SQ_BRACKET));
            List<Map<String, Object>> logMapList = LiveCdnLogConvert.INSTANCE.getLogMapList(jsonArrStr);
            byte[] compressedData = logDeliveryLogDataService.compress(logMapList);
            //获取ApiClient
            Authentication authentication = new JdApiAuth(apiPath, context.getPrivateKey(), context.getAccessKey());
            ApiClient apiClient = JdApiClientFactory.INSTANCE.createApiClient(apiBaseUrl, authentication);
            //创建JdLogDeliveryApi对象
            JdLogDeliveryApi logDeliveryApi = new JdLogDeliveryApi(apiClient);
            JdLiveLogDeliveryResult deliveryResult = sendDeliveryApiCall(logDeliveryApi, compressedData);
            log.info("try[{}],after_api_call,apiBaseUrl={},apiPath={},result={}",
                    curRetryCount, apiBaseUrl, apiPath, JsonUtils.toJsonString(deliveryResult));
            if (deliveryResult.isDeliverySuccessful()) {
                logDetails.forEach(deliveryRecord -> {
                    deliveryRecord.setLogStatus(LogDeliveryStatusEnum.SUCCESS.getStatus());
                    deliveryRecord.setDeliveryTimestamp(LocalDateTime.now());
                });
                log.info("try[{}],log_delivered,apiBaseUrl={},apiPath={}", curRetryCount, apiBaseUrl, apiPath);
                result = true;
            } else {
                log.error("try[{}],delivery_failure,apiBaseUrl={},apiPath={},error={}",
                        curRetryCount, apiBaseUrl, apiPath, deliveryResult.getRespMsg());
            }
            deliveryLogDetailService.updateMany(BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), logDetails);
        } catch (Exception e) {
            throw ExceptionUtil.wrapRuntime(ExceptionUtil.getRootCauseMessage(e));
        }
        return result;
    }

    private JdLiveLogDeliveryResult sendDeliveryApiCall(JdLogDeliveryApi logDeliveryApi, byte[] logData) {
        JdLiveLogDeliveryResult deliveryResult;
        try {
            deliveryResult = logDeliveryApi.postLogs(logData);
        } catch (ApiException e) {
            deliveryResult = LiveCdnLogConvert.INSTANCE.getErrorLogDeliveryResult(e);
        }
        return deliveryResult;
    }
}
