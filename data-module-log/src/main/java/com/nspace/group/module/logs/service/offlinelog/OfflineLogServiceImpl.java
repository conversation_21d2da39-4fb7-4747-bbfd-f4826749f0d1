package com.nspace.group.module.logs.service.offlinelog;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.nspace.group.framework.file.core.FileClient;
import com.nspace.group.module.logs.config.logs.OfflineLogConfiguration;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.DomainStatDTO;
import com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO;
import com.nspace.group.module.logs.dal.mapper.offlinelog.OfflineLogMapper;
import com.nspace.group.module.logs.enums.ExportTypeEnum;
import com.nspace.group.module.logs.service.offlinelog.dto.OfflineLogDTO;
import com.nspace.group.module.logs.service.offlinelog.dto.TaskDTO;
import com.nspace.group.module.logs.utils.Constant;
import com.nspace.group.module.logs.utils.DTF;
import com.nspace.group.module.logs.utils.ThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DependsOn("offline-thread-factory")
public class OfflineLogServiceImpl implements OfflineLogService {


    @Resource
    private OfflineLogFactory offlineLogFactory;

    @Resource
    private OfflineLogGenerateService generateService;

    @Resource
    private DelayedOfflineLogService delayedOfflineLogService;

    @Resource
    private FileClient fileClient;
    @Resource
    private OfflineLogConfiguration configuration;


    @Override
    public String dealOfflineLog(TaskDTO taskDTO) {
        String logType = taskDTO.getLogType();


        OfflineLogMapper logMapper = offlineLogFactory.getOfflineLogMapper(logType);
        Collection<OfflineLogDO> offlineLogDOS = logMapper.selectByCurrent();

        //创建 bucket lifecycle
        List<Integer> days = offlineLogDOS.stream().map(OfflineLogDO::getLifeCycle)
                .distinct().collect(Collectors.toList());
        try {
            fileClient.setBucketExpiration(logType, days);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        Set<OfflineLogDO> failedTask = logMapper.selectFailedDetailTask();
        //获取历史任务中执行失败的子批次
        failedTask.stream().filter(item -> item.getRetryCount() < 3)
                .map(item -> {
                    OfflineLogDTO dto = OfflineLogDTO.from(item);
                    dto.setRetryCount(item.getRetryCount() + 1);
                    dto.setLogType(logType);
                    return dto;
                })
                .filter(item -> StringUtils.isNoneBlank(item.getDomain()))
                .forEach(logDTO -> {
                    ThreadFactory.getFetchExecutors().execute(() -> {
                        generateService.generateOfflineLog(logDTO);
                    });
                });


        Integer offset = taskDTO.getOffset();

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime dateTime = now.minusMinutes(offset);
        String batchNum = dateTime.format(DTF.yyyyMMddHH);
        int minute = 60 / configuration.getTotalBatch();   // 每批次分钟数
        int executeBatch = dateTime.getMinute() / minute;

        /*
         * 查询 doris 判断本批次中的任务是否需要执行
         */
        List<String> domains = offlineLogDOS.stream().map(OfflineLogDO::getDomain)
                .distinct().collect(Collectors.toList());

        LocalDateTime startTime = dateTime.withMinute(executeBatch * minute).withSecond(0).withNano(0);
        LocalDateTime endTime = dateTime.withMinute((executeBatch + 1) * minute - 1).withSecond(59).withNano(0);

        Map<String, DomainStatDTO> resultMap = new HashMap<>();
        List<List<String>> partition = ListUtil.partition(domains, 100);
        partition.forEach(domain -> {
            if (CollectionUtil.isNotEmpty(domain)) {
                List<DomainStatDTO> dos = logMapper.getDomainStat(startTime, endTime, domain, null);
                dos.forEach(item -> {
                    resultMap.put(item.getDomain(), item);
                });
            }
        });

        //获取当前批次需要执行的任务
        offlineLogDOS.stream()
                .filter(item -> StringUtils.isNoneBlank(item.getDomain()))
                .filter(item -> resultMap.get(item.getDomain()) != null)
                .map(item -> {
                    OfflineLogDTO dto = OfflineLogDTO.from(item);
                    dto.setBatchNum(batchNum);
                    return dto;
                })
                .forEach(logDTO -> {
                    ThreadFactory.getFetchExecutors().execute(() -> {
                        DomainStatDTO statDTO = resultMap.get(logDTO.getDomain());
                        logDTO.setExecuteBatch(executeBatch);
                        logDTO.setTotalCount(statDTO.getTotalCount());
                        logDTO.setMaxTimeStamp(statDTO.getMaxTimeStamp());
                        generateService.generateOfflineLog(logDTO);
                    });
                });

        return Constant.SUCCESS;
    }


    @Override
    public String dealDelayedOfflineLog(TaskDTO taskDTO) {
        String logType = taskDTO.getLogType();

        Integer startOffset = taskDTO.getStartOffset();
        Integer endOffset = taskDTO.getEndOffset();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = now.minusHours(startOffset).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endTime = now.minusHours(endOffset).withMinute(59).withSecond(59).withNano(0);
        //查询 doris 数据 和 导出的日志数据是否存在差异,则会进行补充
        List<OfflineLogDTO> delayedLogs = delayedOfflineLogService.getDelayedLogs(logType, startTime, endTime);
        delayedLogs.forEach(delayedDTO -> {
            if (Boolean.TRUE.equals(delayedDTO.isRefresh())) {
                delayedDTO.setExportType(ExportTypeEnum.INCREMENT);
                ThreadFactory.getFetchExecutors().execute(() -> {
                    generateService.generateAllOfflineLog(delayedDTO, false);
                });
            } else if (Boolean.TRUE.equals(delayedDTO.isSyncAll())) {
                ThreadFactory.getFetchExecutors().execute(() -> {
                    generateService.generateAllOfflineLog(delayedDTO, true);
                });
            }
        });
        return Constant.SUCCESS;
    }


    @Override
    public String dealMergeFilesJob(TaskDTO taskDTO) {
        Integer offset = taskDTO.getOffset();
        String logType = taskDTO.getLogType();
        LocalDateTime dateTime = LocalDateTime.now().minusHours(offset);
        OfflineLogMapper logMapper = offlineLogFactory.getOfflineLogMapper(logType);
        List<OfflineLogDO> offlineLogDOS = logMapper.selectMergeTask(dateTime);
        offlineLogDOS.stream()
                .map(OfflineLogDTO::from)
                .forEach(dto -> ThreadFactory.getIOExecutors().execute(() -> {
                    generateService.mergeSubBatch(dto, false);
                }));
        OfflinePersistService persistService = offlineLogFactory.getPersistService(logType);
        int rows = persistService.cleanExpiredLogs();
        log.debug("Clean {}rows expired logs successfully", rows);
        return Constant.SUCCESS;
    }
}
