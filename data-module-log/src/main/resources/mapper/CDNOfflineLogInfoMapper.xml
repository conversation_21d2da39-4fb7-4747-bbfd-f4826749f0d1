<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nspace.group.module.logs.dal.mapper.offlinelog.CDNOfflineLogInfoMapper">


    <resultMap id="baseResultMap" type="com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDO">
        <result column="batch_num" property="batchNum"/>
        <result column="log_type" property="logType"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="domain" property="domain"/>
        <result column="total_batch" property="totalBatch"/>
        <result column="execute_batch" property="executeBatch"/>
        <result column="retry_count" property="retryCount"/>
        <result column="start_timestamp" property="startTimestamp"/>
    </resultMap>

    <resultMap id="delayedResultMap" type="com.nspace.group.module.logs.dal.dataobject.offlinelog.DelayedOfflineLogDO">
        <result property="batchNum" column="batch_num"/>
        <result property="domain" column="domain"/>
        <result property="syncedCount" column="synced_count"/>
        <result property="unsyncedCount" column="unsynced_count"/>
        <result property="maxTimeStamp" column="max_timestamp"/>
        <result property="lifeCycle" column="lifecycle"/>
        <result property="startIndex" column="start_index"/>
    </resultMap>

    <select id="getHistoryLogs" resultMap="delayedResultMap">
        select batch_num, tenant_id, domain, sum(rows) as synced_count,
        max(max_timestamp) as max_timestamp, max(lifecycle) as lifecycle,
        count(*) as start_index
        from cdn_offline_log_info
        where batch_num in
        <foreach collection="batches" item="batch" open="(" close=")" separator=",">
            #{batch}
        </foreach>
        and status = 'SUCCESS' and deleted = 0
        group by batch_num,tenant_id,domain
    </select>


    <select id="selectByCurrent" resultMap="baseResultMap">
        SELECT
            ucs.tenant_id,
            cd."domain" AS "domain",
            'cdn' AS log_type,
            COALESCE (ucs.ext_info :: JSONB -> 'offline_log' ->> 'lifecycle', '30') AS lifecycle
        FROM user_cloud_server ucs
        LEFT JOIN cdn_domain cd ON ucs.tenant_id = cd.tenant_id
        WHERE ucs."code" = 'gycdn' AND ucs.deleted = 0 and cd."domain" is not null
        AND ((ucs.ext_info :: JSONB ->> 'offline_log') = '1' OR (ucs.ext_info :: JSONB -> 'offline_log' ->> 'enable') = 'true')
        group by ucs.tenant_id,"domain",lifecycle
    </select>

    <select id="getDomainStat" resultType="com.nspace.group.module.logs.dal.dataobject.offlinelog.DomainStatDTO">
        select domain,count(*) as total_count,max(cur_timestamp) as max_timestamp
        FROM tb_ods_general_cdn_request_log
        WHERE log_time BETWEEN #{start} AND #{end}
        AND domain in
        <foreach collection="domains" item="domain" open="(" close=")" separator=",">
            #{domain}
        </foreach>
        <if test="timestamp !=null ">
            and cur_timestamp &lt; #{timestamp}
        </if>
        and internal = 0
        group by domain
    </select>

    <select id="selectDelayedLogs" resultMap="delayedResultMap">
        SELECT
            t.batch_num,
            t.domain,
            SUM(IF(c.cur_timestamp &lt;= t.threshold, 1, 0))   AS synced_count,
            SUM(IF(c.cur_timestamp &gt;  t.threshold, 1, 0))   AS unsynced_count,
            max(c.cur_timestamp) as max_timestamp
        FROM
        (
        <foreach collection="logs" item="item" separator="UNION ALL">
            SELECT
            #{item.batchNum} as batch_num,
            #{item.domain} as domain,
            #{item.maxTimeStamp} as threshold
        </foreach>
        ) AS t
        INNER JOIN tb_ods_general_cdn_request_log AS c ON c.domain = t.domain AND DATE_FORMAT(c.log_time, '%Y%m%d%H') = t.batch_num
        WHERE c.log_time BETWEEN #{start} AND #{end} AND c.internal = 0
        GROUP BY t.batch_num, t.domain
    </select>


    <select id="selectFailedDetailTask" resultMap="baseResultMap">
        select batch_num, 'cdn' as log_type,tenant_id,"domain",total_batch,execute_batch,status
        ,max(retry_count) as retry_count,min(start_timestamp) as start_timestamp,max(lifecycle) as lifecycle
        from cdn_offline_log_detail_info
        where status = 'FAILED' and retry_count &lt;= 3 and  deleted = 0
        AND create_time >= NOW() - INTERVAL '1 day'
        group by batch_num,log_type,tenant_id,"domain",total_batch,execute_batch,status
    </select>

    <select id="selectMergeTask" resultMap="baseResultMap">
        select batch_num, 'cdn' as log_type,tenant_id,"domain",max(lifecycle) as lifecycle
        from cdn_offline_log_detail_info
        where status = 'SUCCESS' and retry_count &lt; 3 and deleted = 0 and create_time &gt; #{time}
        group by batch_num,log_type,tenant_id,"domain"
    </select>

    <select id="findAllDetails" resultType="com.nspace.group.module.logs.dal.dataobject.offlinelog.OfflineLogDetailInfoDO">
        select id,log_name,"rows",status,start_time,end_time from cdn_offline_log_detail_info
        where batch_num = #{batchNum} and tenant_id = #{tenantId}
        and "domain" = #{domain} and export_type = #{exportType} and execute_batch = #{executeBatch}
        AND create_time >= NOW() - INTERVAL '1 day'
    </select>


    <update id="recoverDetails">
        update cdn_offline_log_detail_info set deleted = 0 where id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </update>
</mapper>