--
-- PostgreSQL database dump
--

-- Dumped from database version 14.1
-- Dumped by pg_dump version 14.1

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: root; Type: SCHEMA; Schema: -; Owner: root
--

CREATE SCHEMA root;


ALTER SCHEMA root OWNER TO root;

SET default_tablespace = '';

SET default_table_access_method = heap;


--
-- Name: live_offline_log_info; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.live_offline_log_info
(
    id          bigint                 NOT NULL,
    tenant_id   bigint   DEFAULT 0     NOT NULL,
    domain      character varying(128) NOT NULL,
    end_time    timestamp(0) without time zone NOT NULL,
    max_id      bigint,
    type        character varying(128) NOT NULL,
    url         character varying(500) NOT NULL,
    file_name   character varying(512) NOT NULL,
    file_size   bigint,
    status      smallint               NOT NULL,
    creator     character varying(64),
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updater     character varying(64),
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted     smallint DEFAULT 0     NOT NULL
);


ALTER TABLE root.live_offline_log_info OWNER TO root;

--
-- Name: TABLE live_offline_log_info; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.live_offline_log_info IS '直播日志-离线日志信息表';


--
-- Name: COLUMN live_offline_log_info.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.id IS '编号';


--
-- Name: COLUMN live_offline_log_info.tenant_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.tenant_id IS '租户ID';


--
-- Name: COLUMN live_offline_log_info.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.domain IS '域名';


--
-- Name: COLUMN live_offline_log_info.end_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.end_time IS '结束时间';


--
-- Name: COLUMN live_offline_log_info.max_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.max_id IS 'ID最大值';


--
-- Name: COLUMN live_offline_log_info.type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.type IS '日志类型 1、PUSH, 2、PULL';


--
-- Name: COLUMN live_offline_log_info.url; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.url IS '文件下载链接';


--
-- Name: COLUMN live_offline_log_info.file_name; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.file_name IS '文件名称';


--
-- Name: COLUMN live_offline_log_info.file_size; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.file_size IS '文件大小';


--
-- Name: COLUMN live_offline_log_info.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.status IS '文件状态';


--
-- Name: COLUMN live_offline_log_info.creator; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.creator IS '创建者';


--
-- Name: COLUMN live_offline_log_info.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.create_time IS '创建时间';


--
-- Name: COLUMN live_offline_log_info.updater; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.updater IS '更新者';


--
-- Name: COLUMN live_offline_log_info.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.update_time IS '更新时间';


--
-- Name: COLUMN live_offline_log_info.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_offline_log_info.deleted IS '是否删除';


--
-- Name: live_offline_log_info_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE
SEQUENCE root.live_offline_log_info_id_seq
    START
WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE
1;


ALTER TABLE root.live_offline_log_info_id_seq OWNER TO root;

--
-- Name: live_offline_log_info_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER
SEQUENCE root.live_offline_log_info_id_seq OWNED BY root.live_offline_log_info.id;


--
-- Name: live_offline_log_info id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_offline_log_info ALTER COLUMN id SET DEFAULT nextval('root.live_offline_log_info_id_seq'::regclass);


--
-- Data for Name: live_offline_log_info; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.live_offline_log_info (id, tenant_id, domain, end_time, type, url, file_name, file_size, status, creator, create_time, updater, update_time, deleted) FROM stdin;
\
.


--
-- Name: live_offline_log_info_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.live_offline_log_info_id_seq', 1, false);


--
-- Name: live_offline_log_info live_offline_log_info_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_offline_log_info
    ADD CONSTRAINT live_offline_log_info_pkey PRIMARY KEY (id);


--
-- Name: live_offline_log_info_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX live_offline_log_info_idx ON root.live_offline_log_info USING btree (tenant_id, domain, type);


--
-- Name: live_log_delivery_record; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.live_log_delivery_record
(
    id              bigint                 NOT NULL,
    tenant_id       bigint   DEFAULT 0     NOT NULL,
    domain          character varying(128) NOT NULL,
    biz_type        character varying(32)  NOT NULL,
    log_start_time  timestamp(0) without time zone NOT NULL,
    log_end_time    timestamp(0) without time zone NOT NULL,
    log_start_id    bigint                 NOT NULL,
    log_end_id      bigint                 NOT NULL,
    log_type        character varying(128) NOT NULL,
    retry_count     smallint DEFAULT 0     NOT NULL,
    actual_count    bigint   DEFAULT 0     NOT NULL,
    delivery_count  bigint   DEFAULT 0     NOT NULL,
    delivery_status smallint DEFAULT 0     NOT NULL,
    delivery_result character varying(1024),
    deleted         smallint DEFAULT 0     NOT NULL,
    create_time     timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time     timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE root.live_log_delivery_record OWNER TO root;

--
-- Name: TABLE live_log_delivery_record; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.live_log_delivery_record IS '直播日志投递记录表';


--
-- Name: COLUMN live_log_delivery_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.id IS '自增id';


--
-- Name: COLUMN live_log_delivery_record.tenant_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.tenant_id IS '租户ID';


--
-- Name: COLUMN live_log_delivery_record.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.domain IS '域名';


--
-- Name: COLUMN live_log_delivery_record.biz_type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.biz_type IS '业务类型,云直播：LSS';


--
-- Name: COLUMN live_log_delivery_record.log_start_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_start_time IS '日志开始时间';


--
-- Name: COLUMN live_log_delivery_record.log_end_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_end_time IS '日志结束时间';


--
-- Name: COLUMN live_log_delivery_record.log_start_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_start_id IS '日志开始ID';


--
-- Name: COLUMN live_log_delivery_record.log_end_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_end_id IS '日志结束ID';


--
-- Name: COLUMN live_log_delivery_record.log_type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.log_type IS '日志类型';


--
-- Name: COLUMN live_log_delivery_record.retry_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.retry_count IS '失败后重试次数，默认值0';


--
-- Name: COLUMN live_log_delivery_record.actual_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.actual_count IS '实际条数';


--
-- Name: COLUMN live_log_delivery_record.delivery_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.delivery_count IS '投递条数';


--
-- Name: COLUMN live_log_delivery_record.delivery_status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.delivery_status IS '日志投递状态 0:初始值，1:成功，3：重试，-1：过期';


--
-- Name: COLUMN live_log_delivery_record.delivery_result; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.delivery_result IS '日志投递返回结果';


--
-- Name: COLUMN live_log_delivery_record.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.deleted IS '是否删除';


--
-- Name: COLUMN live_log_delivery_record.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.create_time IS '创建时间';


--
-- Name: COLUMN live_log_delivery_record.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_record.update_time IS '更新时间';


--
-- Name: live_log_delivery_record_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE
SEQUENCE root.live_log_delivery_record_id_seq
    START
WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE
1;


ALTER TABLE root.live_log_delivery_record_id_seq OWNER TO root;

--
-- Name: live_log_delivery_record_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER
SEQUENCE root.live_log_delivery_record_id_seq OWNED BY root.live_log_delivery_record.id;

--
-- Name: live_log_delivery_record_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.live_log_delivery_record_id_seq', 1, false);


--
-- Name: live_log_delivery_record live_log_delivery_record_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_log_delivery_record
    ADD CONSTRAINT live_log_delivery_record_pkey PRIMARY KEY (id);


--
-- Name: live_log_delivery_record_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX live_log_delivery_record_idx ON root.live_log_delivery_record USING btree (tenant_id, domain, biz_type, log_type);


--
-- Name: live_log_delivery_record id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_log_delivery_record ALTER COLUMN id SET DEFAULT nextval('root.live_log_delivery_record_id_seq'::regclass);


--
-- Name: live_log_delivery_detail; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.live_log_delivery_detail
(
    id          bigint                 NOT NULL,
    domain      character varying(128) NOT NULL,
    log_id      bigint                 NOT NULL,
    log_time    timestamp(0) without time zone NOT NULL,
    status      smallint               NOT NULL,
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE root.live_log_delivery_detail OWNER TO root;

--
-- Name: TABLE live_log_delivery_detail; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.live_log_delivery_detail IS '直播日志投递明细表';


--
-- Name: COLUMN live_log_delivery_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.id IS '自增id';


--
-- Name: COLUMN live_log_delivery_detail.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.domain IS '域名';


--
-- Name: COLUMN live_log_delivery_detail.log_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.log_id IS '日志ID';


--
-- Name: COLUMN live_log_delivery_detail.log_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.log_time IS '日志打印时间, ISO 8601标准格式';


--
-- Name: COLUMN live_log_delivery_detail.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.status IS '日志投递状态 1:成功 2:失败 4：延迟补传';


--
-- Name: COLUMN live_log_delivery_detail.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.create_time IS '创建时间';


--
-- Name: COLUMN live_log_delivery_detail.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.live_log_delivery_detail.update_time IS '更新时间';


--
-- Name: live_log_delivery_detail live_log_delivery_detail_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.live_log_delivery_detail
    ADD CONSTRAINT live_log_delivery_detail_pkey PRIMARY KEY (id);


--
-- Name: live_log_delivery_detail_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE
SEQUENCE root.live_log_delivery_detail_id_seq
    START
WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE
1;


ALTER TABLE root.live_log_delivery_detail_id_seq OWNER TO root;

--
-- Name: live_log_delivery_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER
SEQUENCE root.live_log_delivery_detail_id_seq OWNED BY root.live_log_delivery_detail.id;

--
-- Name: live_log_delivery_detail_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.live_log_delivery_detail_id_seq', 1, false);

CREATE INDEX live_log_delivery_detail_create_time_domain_idx ON root.live_log_delivery_detail USING BTREE (create_time, domain);

CREATE INDEX live_log_delivery_detail_log_time_domain_idx ON root.live_log_delivery_detail USING BTREE (log_time, domain);


--
-- Name: cdn_log_delivery_record; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.cdn_log_delivery_record
(
    id              bigint                 NOT NULL,
    tenant_id       bigint   DEFAULT 0     NOT NULL,
    domain          character varying(128) NOT NULL,
    biz_type        character varying(32)  NOT NULL,
    log_start_time  timestamp(0) without time zone NOT NULL,
    log_end_time    timestamp(0) without time zone NOT NULL,
    log_start_id    bigint                 NOT NULL,
    log_end_id      bigint                 NOT NULL,
    log_type        character varying(128) NOT NULL,
    retry_count     smallint DEFAULT 0     NOT NULL,
    actual_count    bigint   DEFAULT 0     NOT NULL,
    delivery_count  bigint   DEFAULT 0     NOT NULL,
    delivery_status smallint DEFAULT 0     NOT NULL,
    delivery_result character varying(1024),
    deleted         smallint DEFAULT 0     NOT NULL,
    create_time     timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time     timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE root.cdn_log_delivery_record OWNER TO root;

--
-- Name: TABLE cdn_log_delivery_record; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.cdn_log_delivery_record IS '通用CDN日志投递记录表';


--
-- Name: COLUMN cdn_log_delivery_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.id IS '自增id';


--
-- Name: COLUMN cdn_log_delivery_record.tenant_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.tenant_id IS '租户ID';


--
-- Name: COLUMN cdn_log_delivery_record.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.domain IS '域名';


--
-- Name: COLUMN cdn_log_delivery_record.biz_type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.biz_type IS '业务类型,通用CDN：CDN';


--
-- Name: COLUMN cdn_log_delivery_record.log_start_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_start_time IS '日志开始时间';


--
-- Name: COLUMN cdn_log_delivery_record.log_end_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_end_time IS '日志结束时间';


--
-- Name: COLUMN cdn_log_delivery_record.log_start_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_start_id IS '日志开始ID';


--
-- Name: COLUMN cdn_log_delivery_record.log_end_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_end_id IS '日志结束ID';


--
-- Name: COLUMN cdn_log_delivery_record.log_type; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.log_type IS '日志类型';


--
-- Name: COLUMN cdn_log_delivery_record.retry_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.retry_count IS '失败后重试次数，默认值0';


--
-- Name: COLUMN cdn_log_delivery_record.actual_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.actual_count IS '实际条数';


--
-- Name: COLUMN cdn_log_delivery_record.delivery_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.delivery_count IS '投递条数';


--
-- Name: COLUMN cdn_log_delivery_record.delivery_status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.delivery_status IS '日志投递状态 0:初始值，1:成功，3：重试，-1：过期';


--
-- Name: COLUMN cdn_log_delivery_record.delivery_result; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.delivery_result IS '日志投递返回结果';


--
-- Name: COLUMN cdn_log_delivery_record.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.deleted IS '是否删除';


--
-- Name: COLUMN cdn_log_delivery_record.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.create_time IS '创建时间';


--
-- Name: COLUMN cdn_log_delivery_record.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_record.update_time IS '更新时间';


--
-- Name: cdn_log_delivery_record_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE
SEQUENCE root.cdn_log_delivery_record_id_seq
    START
WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE
1;


ALTER TABLE root.cdn_log_delivery_record_id_seq OWNER TO root;

--
-- Name: cdn_log_delivery_record_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER
SEQUENCE root.cdn_log_delivery_record_id_seq OWNED BY root.cdn_log_delivery_record.id;

--
-- Name: cdn_log_delivery_record_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.cdn_log_delivery_record_id_seq', 1, false);


--
-- Name: cdn_log_delivery_record cdn_log_delivery_record_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.cdn_log_delivery_record
    ADD CONSTRAINT cdn_log_delivery_record_pkey PRIMARY KEY (id);


--
-- Name: cdn_log_delivery_record_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX cdn_log_delivery_record_idx ON root.cdn_log_delivery_record USING btree (tenant_id, domain, biz_type, log_type);


--
-- Name: cdn_log_delivery_record id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.cdn_log_delivery_record ALTER COLUMN id SET DEFAULT nextval('root.cdn_log_delivery_record_id_seq'::regclass);


--
-- Name: cdn_log_delivery_detail; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.cdn_log_delivery_detail
(
    id          bigint                 NOT NULL,
    domain      character varying(128) NOT NULL,
    log_id      bigint                 NOT NULL,
    log_time    timestamp(0) without time zone NOT NULL,
    status      smallint               NOT NULL,
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE root.cdn_log_delivery_detail OWNER TO root;

--
-- Name: TABLE cdn_log_delivery_detail; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.cdn_log_delivery_detail IS '通用CDN日志投递明细表';


--
-- Name: COLUMN cdn_log_delivery_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.id IS '自增id';


--
-- Name: COLUMN cdn_log_delivery_detail.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.domain IS '域名';


--
-- Name: COLUMN cdn_log_delivery_detail.log_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.log_id IS '日志ID';


--
-- Name: COLUMN cdn_log_delivery_detail.log_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.log_time IS '日志打印时间, ISO 8601标准格式';


--
-- Name: COLUMN cdn_log_delivery_detail.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.status IS '日志投递状态 1:成功 2:失败 4：延迟补传';


--
-- Name: COLUMN cdn_log_delivery_detail.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.create_time IS '创建时间';


--
-- Name: COLUMN cdn_log_delivery_detail.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.cdn_log_delivery_detail.update_time IS '更新时间';


--
-- Name: cdn_log_delivery_detail cdn_log_delivery_detail_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.cdn_log_delivery_detail
    ADD CONSTRAINT cdn_log_delivery_detail_pkey PRIMARY KEY (id);


--
-- Name: cdn_log_delivery_detail_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE
SEQUENCE root.cdn_log_delivery_detail_id_seq
    START
WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE
1;


ALTER TABLE root.cdn_log_delivery_detail_id_seq OWNER TO root;

--
-- Name: cdn_log_delivery_detail_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER
SEQUENCE root.cdn_log_delivery_detail_id_seq OWNED BY root.cdn_log_delivery_detail.id;

--
-- Name: cdn_log_delivery_detail_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.cdn_log_delivery_detail_id_seq', 1, false);

CREATE INDEX cdn_log_delivery_detail_create_time_domain_idx ON root.cdn_log_delivery_detail USING BTREE (create_time, domain);

CREATE INDEX cdn_log_delivery_detail_log_time_domain_idx ON root.cdn_log_delivery_detail USING BTREE (log_time, domain);

CREATE TABLE "root"."cdn_offline_log_info"
(
    "id"              INT8                                        NOT NULL,
    "tenant_id"       INT8                                        NOT NULL DEFAULT 0,
    "domain"          VARCHAR(128) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_num"       VARCHAR(10) COLLATE "pg_catalog"."default"  NOT NULL,
    "export_type"     VARCHAR(32) COLLATE "pg_catalog"."default",
    "bucket"          VARCHAR(500) COLLATE "pg_catalog"."default",
    "log_name"        VARCHAR(512) COLLATE "pg_catalog"."default",
    "file_size"       INT8,
    "rows"            INT4,
    "status"          VARCHAR(32) COLLATE "pg_catalog"."default",
    "message"         TEXT COLLATE "pg_catalog"."default",
    "lifecycle"       INT2,
    "retry_count"     INT2,
    "start_timestamp" TIMESTAMP(6),
    "max_timestamp"   TIMESTAMP(6),
    "create_time"     TIMESTAMP(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time"     TIMESTAMP(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted"         INT2                                        NOT NULL DEFAULT 0
);
ALTER TABLE "root"."cdn_offline_log_info" OWNER TO "root";
COMMENT ON COLUMN "root"."cdn_offline_log_info"."id" IS '编号';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."domain" IS '域名';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."batch_num" IS '任务批次';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."export_type" IS '导出类型: 全量,增量';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."bucket" IS '文件存储桶';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."log_name" IS '文件名称';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."file_size" IS '文件大小';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."rows" IS '文件行数';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."status" IS '状态';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."message" IS '错误信息';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."lifecycle" IS '生命周期';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."retry_count" IS '重试次数';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."start_timestamp" IS '批次执行开始时间';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."max_timestamp" IS '批次最大记录数';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "root"."cdn_offline_log_info"."deleted" IS '是否删除';
COMMENT ON TABLE "root"."cdn_offline_log_info" IS '离线日志-CDN离线日志信息记录表';
ALTER TABLE ONLY root.cdn_offline_log_info ADD CONSTRAINT cdn_offline_log_info_pkey PRIMARY KEY (id);


CREATE INDEX idx_cdn_batch_domain
    ON root.live_offline_log_detail_info (tenant_id, batch_num, domain);

CREATE INDEX idx_cdn_log_create_time
    ON root.live_offline_log_detail_info (create_time);
--
-- Name: cdn_offline_log_info_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--
CREATE
SEQUENCE root.cdn_offline_log_info_id_seq START
WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE
1;
ALTER TABLE root.cdn_offline_log_info_id_seq OWNER TO root;

--
-- Name: cdn_offline_log_info_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--
ALTER
SEQUENCE root.cdn_offline_log_info_id_seq OWNED BY root.cdn_log_delivery_detail.id;

--
-- Name: cdn_log_delivery_detail_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--
SELECT pg_catalog.setval('root.cdn_offline_log_info_id_seq', 1, FALSE);



CREATE TABLE "root"."live_offline_log_info"
(
    "id"              INT8                                        NOT NULL,
    "tenant_id"       INT8                                        NOT NULL DEFAULT 0,
    "domain"          VARCHAR(128) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_num"       VARCHAR(10) COLLATE "pg_catalog"."default"  NOT NULL,
    "export_type"     VARCHAR(32) COLLATE "pg_catalog"."default",
    "bucket"          VARCHAR(500) COLLATE "pg_catalog"."default",
    "log_name"        VARCHAR(512) COLLATE "pg_catalog"."default",
    "file_size"       INT8,
    "rows"            INT4,
    "status"          VARCHAR(32) COLLATE "pg_catalog"."default",
    "message"         TEXT COLLATE "pg_catalog"."default",
    "lifecycle"       INT2,
    "retry_count"     INT2,
    "start_timestamp" TIMESTAMP(6),
    "max_timestamp"   TIMESTAMP(6),
    "create_time"     TIMESTAMP(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time"     TIMESTAMP(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted"         INT2                                        NOT NULL DEFAULT 0
);
ALTER TABLE "root"."live_offline_log_info" OWNER TO "root";
COMMENT ON COLUMN "root"."live_offline_log_info"."id" IS '编号';
COMMENT ON COLUMN "root"."live_offline_log_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "root"."live_offline_log_info"."domain" IS '域名';
COMMENT ON COLUMN "root"."live_offline_log_info"."batch_num" IS '任务批次';
COMMENT ON COLUMN "root"."live_offline_log_info"."export_type" IS '导出类型: 全量,增量';
COMMENT ON COLUMN "root"."live_offline_log_info"."bucket" IS '文件存储桶';
COMMENT ON COLUMN "root"."live_offline_log_info"."log_name" IS '文件名称';
COMMENT ON COLUMN "root"."live_offline_log_info"."file_size" IS '文件大小';
COMMENT ON COLUMN "root"."live_offline_log_info"."rows" IS '文件行数';
COMMENT ON COLUMN "root"."live_offline_log_info"."status" IS '状态';
COMMENT ON COLUMN "root"."live_offline_log_info"."message" IS '错误信息';
COMMENT ON COLUMN "root"."live_offline_log_info"."lifecycle" IS '生命周期';
COMMENT ON COLUMN "root"."live_offline_log_info"."retry_count" IS '重试次数';
COMMENT ON COLUMN "root"."live_offline_log_info"."start_timestamp" IS '批次执行开始时间';
COMMENT ON COLUMN "root"."live_offline_log_info"."max_timestamp" IS '批次最大记录数';
COMMENT ON COLUMN "root"."live_offline_log_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "root"."live_offline_log_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "root"."live_offline_log_info"."deleted" IS '是否删除';
COMMENT ON TABLE "root"."cdn_offline_log_info" IS '离线日志-直播离线日志信息记录表' ;
ALTER TABLE ONLY root.live_offline_log_info ADD CONSTRAINT live_offline_log_info_pkey PRIMARY KEY (id);


CREATE INDEX idx_live_domain_batch_num
    ON root.live_offline_log_detail_info (tenant_id, batch_num, domain);

CREATE INDEX idx_live_log_create_time
    ON root.live_offline_log_detail_info (create_time);
--
-- Name: live_offline_log_info_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--
CREATE
SEQUENCE root.live_offline_log_info_id_seq START
WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE
1;
ALTER TABLE root.live_offline_log_info_id_seq OWNER TO root;

--
-- Name: live_offline_log_info_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--
ALTER
SEQUENCE root.live_offline_log_info_id_seq OWNED BY root.cdn_log_delivery_detail.id;

	--
	-- Name: live_offline_log_info_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
	--
SELECT pg_catalog.setval('root.live_offline_log_info_id_seq', 1, FALSE);


CREATE TABLE "root"."offline_log_expression"
(
    "id"          int8                                        NOT NULL DEFAULT 0,
    "log_type"    varchar(32) COLLATE "pg_catalog"."default"  NOT NULL,
    "tenant_id"   int8                                        NOT NULL DEFAULT 0,
    "domain"      varchar(128) COLLATE "pg_catalog"."default" NOT NULL,
    "field_index" int2                                        NOT NULL DEFAULT 0,
    "expression"  varchar(256) COLLATE "pg_catalog"."default" NOT NULL,
    "creator"     varchar(64) COLLATE "pg_catalog"."default",
    "create_time" timestamp(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater"     varchar(64) COLLATE "pg_catalog"."default",
    "update_time" timestamp(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted"     int2                                        NOT NULL DEFAULT 0
)
;

ALTER TABLE "root"."offline_log_expression"
    OWNER TO "root";
COMMENT ON COLUMN "root"."offline_log_expression"."id" IS '编号';
COMMENT ON COLUMN "root"."offline_log_expression"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "root"."offline_log_expression"."domain" IS '域名';
COMMENT ON COLUMN "root"."offline_log_expression"."field_index" IS '字段索引';
COMMENT ON COLUMN "root"."offline_log_expression"."expression" IS 'avaitor 表达式';
COMMENT ON COLUMN "root"."offline_log_expression"."creator" IS '创建者';
COMMENT ON COLUMN "root"."offline_log_expression"."create_time" IS '创建时间';
COMMENT ON COLUMN "root"."offline_log_expression"."updater" IS '更新者';
COMMENT ON COLUMN "root"."offline_log_expression"."update_time" IS '更新时间';
COMMENT ON TABLE "root"."offline_log_expression" IS '离线日志-离线日志表达式表';

ALTER TABLE ONLY root.offline_log_expression
    ADD CONSTRAINT offline_log_expression_pkey PRIMARY KEY (id);


--
-- Name: offline_log_expression_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE
SEQUENCE root.offline_log_expression_id_seq
    START
WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE CACHE
1;


ALTER TABLE root.offline_log_expression_id_seq OWNER TO root;

--
-- Name: offline_log_expression_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER
SEQUENCE root.offline_log_expression_id_seq OWNED BY root.cdn_log_delivery_detail.id;

--
-- Name: offline_log_expression_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.offline_log_expression_id_seq', 1, false);


CREATE TABLE "root"."cdn_offline_log_detail_info"
(
    "id"              INT8                                        NOT NULL,
    "tenant_id"       INT8                                        NOT NULL DEFAULT 0,
    "domain"          VARCHAR(128) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_num"       VARCHAR(10) COLLATE "pg_catalog"."default"  NOT NULL,
    "export_type"     VARCHAR(32) COLLATE "pg_catalog"."default",
    "bucket"          VARCHAR(500) COLLATE "pg_catalog"."default",
    "log_name"        VARCHAR(512) COLLATE "pg_catalog"."default",
    "file_size"       INT8,
    "rows"            INT4,
    "status"          VARCHAR(32) COLLATE "pg_catalog"."default",
    "message"         TEXT COLLATE "pg_catalog"."default",
    "lifecycle"       INT2,
    "execute_batch"   INT2,
    "start_time"      TIMESTAMP(6),
    "end_time"        TIMESTAMP(6),
    "retry_count"     INT2,
    "start_timestamp" TIMESTAMP(6),
    "max_timestamp"   TIMESTAMP(6),
    "create_time"     TIMESTAMP(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time"     TIMESTAMP(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted"         INT2                                        NOT NULL DEFAULT 0
);
ALTER TABLE "root"."cdn_offline_log_detail_info" OWNER TO "root";
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."id" IS '编号';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."domain" IS '域名';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."batch_num" IS '任务批次';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."export_type" IS '导出类型: 全量,增量';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."bucket" IS '文件存储桶';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."log_name" IS '文件名称';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."file_size" IS '文件大小';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."rows" IS '文件行数';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."status" IS '状态';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."message" IS '错误信息';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."lifecycle" IS '生命周期';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."execute_batch" IS '执行批次';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."start_time" IS '查询起始时间';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."end_time" IS '查询结束时间';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."retry_count" IS '重试次数';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."start_timestamp" IS '批次执行开始时间';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."max_timestamp" IS '批次最大记录数';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "root"."cdn_offline_log_detail_info"."deleted" IS '是否删除';
COMMENT ON TABLE "root"."cdn_offline_log_detail_info" IS '离线日志-CDN离线日志信息记录表';
ALTER TABLE ONLY root.cdn_offline_log_detail_info ADD CONSTRAINT cdn_offline_log_detail_info_pkey PRIMARY KEY (id);

CREATE INDEX idx_cdn_detail_batch_domain
    ON root.live_offline_log_detail_info (tenant_id, batch_num, domain);

CREATE INDEX idx_cdn_detail_log_create_time
    ON root.live_offline_log_detail_info (create_time);

--
-- Name: cdn_offline_log_detail_info_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--
CREATE
SEQUENCE root.cdn_offline_log_detail_info_id_seq START
WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE
1;
ALTER TABLE root.cdn_offline_log_detail_info_id_seq OWNER TO root;

--
-- Name: cdn_offline_log_detail_info_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--
ALTER
SEQUENCE root.cdn_offline_log_detail_info_id_seq OWNED BY root.cdn_log_delivery_detail.id;

--
-- Name: cdn_offline_log_detail_info_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--
SELECT pg_catalog.setval('root.cdn_offline_log_detail_info_id_seq', 1, FALSE);

CREATE TABLE "root"."live_offline_log_detail_info"
(
    "id"              INT8                                        NOT NULL,
    "tenant_id"       INT8                                        NOT NULL DEFAULT 0,
    "domain"          VARCHAR(128) COLLATE "pg_catalog"."default" NOT NULL,
    "batch_num"       VARCHAR(10) COLLATE "pg_catalog"."default"  NOT NULL,
    "export_type"     VARCHAR(32) COLLATE "pg_catalog"."default",
    "bucket"          VARCHAR(500) COLLATE "pg_catalog"."default",
    "log_name"        VARCHAR(512) COLLATE "pg_catalog"."default",
    "file_size"       INT8,
    "rows"            INT4,
    "status"          VARCHAR(32) COLLATE "pg_catalog"."default",
    "message"         TEXT COLLATE "pg_catalog"."default",
    "lifecycle"       INT2,
    "execute_batch"   INT2,
    "start_time"      TIMESTAMP(6),
    "end_time"        TIMESTAMP(6),
    "retry_count"     INT2,
    "start_timestamp" TIMESTAMP(6),
    "max_timestamp"   TIMESTAMP(6),
    "create_time"     TIMESTAMP(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "update_time"     TIMESTAMP(6)                                NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted"         INT2                                        NOT NULL DEFAULT 0
);
ALTER TABLE "root"."live_offline_log_detail_info" OWNER TO "root";
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."id" IS '编号';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."domain" IS '域名';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."batch_num" IS '任务批次';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."export_type" IS '导出类型: 全量,增量';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."bucket" IS '文件桶';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."log_name" IS '文件名称';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."file_size" IS '文件大小';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."rows" IS '文件行数';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."status" IS '状态';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."message" IS '错误信息';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."lifecycle" IS '生命周期';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."execute_batch" IS '执行批次';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."start_time" IS '查询起始时间';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."end_time" IS '查询结束时间';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."retry_count" IS '重试次数';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."start_timestamp" IS '批次执行开始时间';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."max_timestamp" IS '批次最大记录数';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "root"."live_offline_log_detail_info"."deleted" IS '是否删除';
COMMENT ON TABLE "root"."live_offline_log_detail_info" IS '离线日志-CDN离线日志信息记录表';
ALTER TABLE ONLY root.live_offline_log_detail_info ADD CONSTRAINT live_offline_log_detail_info_pkey PRIMARY KEY (id);

CREATE INDEX idx_live_detail_batch_domain
    ON root.live_offline_log_detail_info (tenant_id, batch_num, domain);

CREATE INDEX idx_live_detail_log_create_time
    ON root.live_offline_log_detail_info (create_time);

--
-- Name: live_offline_log_detail_info_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--
CREATE
SEQUENCE root.live_offline_log_detail_info_id_seq START
WITH 1 INCREMENT BY 1 NO MINVALUE NO MAXVALUE CACHE
1;
ALTER TABLE root.live_offline_log_detail_info_id_seq OWNER TO root;

--
-- Name: live_offline_log_detail_info_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--
ALTER
SEQUENCE root.live_offline_log_detail_info_id_seq OWNED BY root.cdn_log_delivery_detail.id;

--
-- Name: live_offline_log_detail_info_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--
SELECT pg_catalog.setval('root.live_offline_log_detail_info_id_seq', 1, FALSE);