<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.nspace.group.module.infra.dal.mapper.detail.OdsBillingLogStreamPullMapper">
    <select id="getLogDetails"
            resultType="com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO"
            parameterType="com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO">
        SELECT
        *
        FROM
        tb_ods_billing_log_stream_pull
        <where>
            domain = #{domain}
            <if test="curTimestampStart != null and curTimestampEnd != null">
                <![CDATA[
                        AND cur_timestamp <= #{curTimestampEnd}
                        AND ((cur_timestamp > #{curTimestampStart}) OR (cur_timestamp = #{curTimestampStart} AND id > #{minId}))
                    ]]>
            </if>
            <if test="startTime != null and endTime != null">
                <![CDATA[
                     AND log_time <= #{endTime}
                     AND ((log_time > #{startTime}) OR (log_time = #{startTime} AND id > #{minId}))
                    ]]>
            </if>
            AND internal = 0
        </where>
        ORDER BY log_time ASC
        LIMIT #{pageSize}
    </select>
</mapper>