package com.nspace.group.module.infra.client;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.lang.reflect.Type;

public class JSON {
    private JsonMapper jsonMapper;

    public JSON() {
        jsonMapper = JsonMapper.builder()
                .addModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .serializationInclusion(JsonInclude.Include.NON_NULL)
                .build();
    }

    /**
     * Get JsonMapper.
     *
     * @return JsonMapper
     */
    public JsonMapper getJsonMapper() {
        return jsonMapper;
    }

    /**
     * Set JsonMapper.
     *
     * @param jsonMapper JsonMapper
     * @return JSON
     */
    public JSON setJsonMapper(JsonMapper jsonMapper) {
        this.jsonMapper = jsonMapper;
        return this;
    }

    /**
     * 将Object传参序列化为JSON字符
     *
     * @param obj Object
     * @return JSON字符串
     */
    public String serialize(Object obj) {
        try {
            return jsonMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e.getLocalizedMessage());
        }
    }

    /**
     * 将JSON反序列化为Java对象
     *
     * @param <T>        Type
     * @param body       JSON字符串
     * @param returnType Java对象类型
     * @return 反序列化后的对象
     */
    @SuppressWarnings("unchecked")
    public <T> T deserialize(String body, Type returnType) {
        try {
            return (T) jsonMapper.readValue(body, jsonMapper.constructType(returnType));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            if (returnType.equals(String.class)) {
                return (T) body;
            } else {
                throw new RuntimeException(e.getLocalizedMessage());
            }
        }
    }

}
