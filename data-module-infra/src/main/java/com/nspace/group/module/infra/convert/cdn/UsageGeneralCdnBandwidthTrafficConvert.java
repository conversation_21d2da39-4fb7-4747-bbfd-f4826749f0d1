package com.nspace.group.module.infra.convert.cdn;

import com.nspace.group.module.infra.dal.dataobject.cdn.UsageGeneralCdnBandwidthTrafficDO;
import com.nspace.group.module.infra.service.cdn.dto.UsageGeneralCdnBandwidthTrafficDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :UsageGeneralCdnBandwidthTrafficConvert.java, v0.1 2024年12月05日 10:21 zhangxin Exp
 */
@Mapper
public interface UsageGeneralCdnBandwidthTrafficConvert {

    UsageGeneralCdnBandwidthTrafficConvert INSTANCE = Mappers.getMapper(UsageGeneralCdnBandwidthTrafficConvert.class);

    @Mapping(target = "traffic", source = "flow")
    @Mapping(target = "bandwidth", constant = "0.0")
    UsageGeneralCdnBandwidthTrafficDTO getTrafficDTO(Long tenantId, LocalDateTime windowStart, String domain,
                                                     String region, String billingRegion, Integer dataPlatform,
                                                     Long flow, LocalDateTime windowEnd);

    @Mapping(target = "curTimestamp", ignore = true)
    UsageGeneralCdnBandwidthTrafficDO toBandwidthTrafficDO(UsageGeneralCdnBandwidthTrafficDTO data);

    @Mapping(target = "traffic", constant = "0L")
    UsageGeneralCdnBandwidthTrafficDTO getBandwidthDTO(Long tenantId, LocalDateTime windowStart, String domain,
                                                       String region, String billingRegion, Integer dataPlatform,
                                                       BigDecimal bandwidth, LocalDateTime windowEnd);
}