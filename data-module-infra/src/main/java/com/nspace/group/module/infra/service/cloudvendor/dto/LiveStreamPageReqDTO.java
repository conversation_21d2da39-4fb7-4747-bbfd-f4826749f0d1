package com.nspace.group.module.infra.service.cloudvendor.dto;

import com.nspace.group.framework.common.pojo.PageParam;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 查询供应商对应的直播流信息
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/1
 * @time：11:17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class LiveStreamPageReqDTO extends PageParam {
    private String domain;
    private Long tenantId;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
}
