package com.nspace.group.module.infra.dal.dataobject.detail;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * 原始ods数据记录-拉流计费日志表实体类
 *
 * <AUTHOR>
 * @since 2024-12-27 10:52:05
 */
@TableName("tb_ods_billing_log_stream_pull")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
@Builder
public class OdsBillingLogStreamPullDO {

    @TableId(type = IdType.NONE)
    private Long id;
    //日志打印时间, ISO 8601标准格式
    private Date logTime;
    //域名，例如play.test.com
    private String domain;
    //数据写入时间
    private Date curTimestamp;
    //机器名，例如czct-ms-001
    private String hostname;
    //唯一请求标识符，由16字节随机生成，十六进制表示
    private String requestId;
    //第几轮打印，从0开始
    private Long round;
    //是否是结束打印: 0-否，1-是
    private Integer end;
    //分类，pull:拉流，push:推流
    private String category;
    //流协议，例如rtmp，http_flv
    private String streamProtocol;
    //是否是内部请求: 0-否，1-是
    private Integer internal;
    //这个周期的时间长度, 单位秒
    private Long duration;
    //从建联到现在经历的时间，单位秒
    private Long totalDuration;
    //用户地址，例如*************:2932
    private String clientAddr;
    //服务端地址，例如************:80
    private String serverAddr;
    //协议scheme，例如http, https, rtmp, rtmps
    private String scheme;
    //HTTP请求方法，例如GET, POST
    private String httpMethod;
    //请求URI，包括参数，例如/app/test.flv?myt=2
    private String requestUri;
    //rewrite后的URI，例如/live/test.flv
    private String rewriteUri;
    //响应状态码，使用HTTP的状态码定义
    private Integer status;
    //总发送字节数
    private Long bytesSent;
    //这个周期发送的字节数
    private Long intervalBytesSent;
    //这个周期接收的字节数
    private Long intervalBytesRecv;
    //总接收字节数
    private Long bytesRecv;
    //连接创建时间
    private Date connectTime;
    //从收到请求到接收到第一个字节的时间，单位秒，精度到毫秒
    private Float firstByteRecvTime;
    //协议版本，例如“HTTP/1.0”, “HTTP/1.1”, “HTTP/2.0”, “HTTP/3.0”
    private String serverProtocol;
    //视频帧率，这个周期的平均帧率
    private Long videoFps;
    //视频码率，这个周期的平均码率，单位: bit/s
    private Long videoBps;
    //音频帧率，这个周期的平均帧率
    private Long audioFps;
    //音频码率，这个周期的平均码率，单位: bit/s
    private Long audioBps;
    //卡顿次数
    private Long discontinuousCount;
    //卡顿时间（毫秒）
    private Long discontinuousTime;
    //回源帧率
    private Integer sourceStreamFps;
    //客户端IP对应的国家【第三方数据】
    private String country;
    //客户端IP对应的省份【第三方数据】
    private String province;
    //客户端IP对应的运营商名称【第三方数据】
    private String ispName;
    //数据来源标识，默认0，内部数据【第三方数据】
    private Integer dataPlatform;
    //日志的目的或者用途，默认0，计费【第三方数据】
    private Integer logPurpose;
    //HTTP请求头中的referer
    private String referer;
    //建立连接到第一个GOP发出的时间，单位ms
    private Long firstGopSentTimeMs;
    //上游处理机器信息
    private String via;
    //HTTP请求头中的user agent
    private String userAgent;
    //I帧的平均间隔，单位ms
    private Long avgGopSizeMs;
    //视频帧时间戳之间的最大间隔
    private Long videoMaxGapMs;
    //错误描述
    private String err;
    //上游节点信息
    private String upstreamNode;
}

