package com.nspace.group.module.infra.service.cdn;

import com.nspace.group.module.infra.dal.dataobject.cdn.OdsGeneralCdnRequestLogDO;
import com.nspace.group.module.infra.dal.mapper.cdn.OdsGeneralCdnRequestLogMapper;
import com.nspace.group.module.infra.service.cdn.dto.GeneralCdnRequestLogReqDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 原始ods数据记录-通用cdn请求日志的服务接口实现类
 *
 * <AUTHOR>
 * @since 2025-03-19 09:38:23
 */
@Service
public class OdsGeneralCdnRequestLogServiceImpl implements OdsGeneralCdnRequestLogService {

    @Resource
    OdsGeneralCdnRequestLogMapper cdnRequestLogMapper;

    @Override
    public List<OdsGeneralCdnRequestLogDO> getRequestLogList(GeneralCdnRequestLogReqDTO reqDTO) {
        return cdnRequestLogMapper.getRequestLogList(reqDTO);
    }

    @Override
    public List<OdsGeneralCdnRequestLogDO> getDelayedRequestLogList(List<Long> logIds) {
        return cdnRequestLogMapper.getDelayedRequestLogList(logIds);
    }
}




