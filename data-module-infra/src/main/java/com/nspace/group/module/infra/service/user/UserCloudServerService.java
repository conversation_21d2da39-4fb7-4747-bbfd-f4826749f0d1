package com.nspace.group.module.infra.service.user;

import com.nspace.group.framework.common.pojo.PageParam;
import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveDomainDO;
import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;

import java.util.List;

/**
 * 用户开通的云服务相关配置
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/12/16
 * @time：17:32
 */

public interface UserCloudServerService {
    PageResult<LiveDomainDO> getUserCloudDomainList(PageParam pageInfo, String cloudType, String domainType);

    PageResult<LogDeliveryDomainDTO> getLogDeliveryDomainList(PageParam pageInfo, String serviceType, String domainType, String deliveryTarget);

    List<LogDeliveryDomainDTO> getLogDeliveryConfigList(String serviceType, String deliveryTarget);
}
