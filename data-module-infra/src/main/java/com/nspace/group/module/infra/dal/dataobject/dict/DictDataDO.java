package com.nspace.group.module.infra.dal.dataobject.dict;

import com.baomidou.mybatisplus.annotation.*;
import com.nspace.group.framework.common.enums.CommonStatusEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 字典数据表
 *
 * <AUTHOR>
 */
@TableName("system_dict_data")
@Data
public class DictDataDO {

    /**
     * 字典数据编号
     */
    @TableId
    private Long id;
    /**
     * 字典排序
     */
    private Integer sort;
    /**
     * 字典标签
     */
    private String label;
    /**
     * 字典值
     */
    private String value;
    /**
     * 字典类型
     *
     * 冗余 {@link DictDataDO#getDictType()}
     */
    private String dictType;
    /**
     * 状态
     *
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 颜色类型
     *
     * 对应到 element-ui 为 default、primary、success、info、warning、danger
     */
    private String colorType;
    /**
     * css 样式
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String cssClass;
    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     */
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     */
    private String updater;
    /**
     * 是否删除
     */
    @TableLogic(value = "0", delval = "1")
    private Boolean deleted;

}
