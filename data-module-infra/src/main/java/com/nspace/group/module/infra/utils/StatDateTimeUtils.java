package com.nspace.group.module.infra.utils;

import cn.hutool.core.date.LocalDateTimeUtil;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class StatDateTimeUtils {
    /**
     * 判断开始时间和结束时间对应的时间间隔是否超过小时数
     *
     * @param startTime
     * @param endTime
     * @param seconds
     * @return
     */
    public static boolean isExpiresHours(LocalDateTime startTime, LocalDateTime endTime, Integer seconds) {
        return LocalDateTimeUtil.between(startTime, endTime, ChronoUnit.SECONDS) > seconds;
    }

    /**
     * 生成指定开始时间和结束时间内的时间点列表
     *
     * @param startTimestamp    开始时间
     * @param endTimestamp      结束时间
     * @param intervalInSeconds 时间点间隔（分钟）
     * @return 时间点列表
     * @throws IllegalArgumentException 如果开始时间晚于结束时间或时间间隔不合法
     */

    public static List<Long> generateTimePoints(long startTimestamp, long endTimestamp, int intervalInSeconds) throws IllegalArgumentException {
        ZonedDateTime startZonedDateTime = convertToZonedDateTime(startTimestamp);
        ZonedDateTime endZonedDateTime = convertToZonedDateTime(endTimestamp);
        if (startZonedDateTime.isAfter(endZonedDateTime)) {
            throw new IllegalArgumentException("Start time is after end time.");
        }
        if (intervalInSeconds <= 0) {
            throw new IllegalArgumentException("Interval must be greater than zero.");
        }
        List<Long> timePoints = new ArrayList<>();
        long currentTimeSecond = alignToInterval(startZonedDateTime, intervalInSeconds);
        long startZonedSecond = startZonedDateTime.toInstant().getEpochSecond();
        long endZonedSecond = endZonedDateTime.toInstant().getEpochSecond();
        if ((currentTimeSecond - startZonedSecond) >= intervalInSeconds) {
            timePoints.add(startZonedSecond);
        }
        while (currentTimeSecond <= endZonedSecond) {
            timePoints.add(currentTimeSecond);
            currentTimeSecond += intervalInSeconds;
        }
        return timePoints;
    }

    private static long alignToInterval(ZonedDateTime startZonedDateTime, int intervalInSeconds) {
        ZonedDateTime nextDivisibleTime = startZonedDateTime;
        int hoursToAdd = 0;
        int nextMinute = 0;
        if (!isOneDay(intervalInSeconds)) {
            int intervalInMinutes = intervalInSeconds / 60;
            int currentMinute = nextDivisibleTime.getMinute();
            nextMinute = ((currentMinute / intervalInMinutes)) * intervalInMinutes;
            hoursToAdd = (nextMinute >= 60) ? 1 : 0;
            nextMinute %= 60;
        }
        ZonedDateTime zonedDateTime = nextDivisibleTime.plusHours(hoursToAdd).withMinute(nextMinute).withSecond(0).withNano(0);
        return zonedDateTime.toInstant().getEpochSecond();
    }

    private static boolean isOneDay(int intervalInSeconds) {
        return intervalInSeconds == 86400;
    }

    private static boolean isOneHour(int intervalInSeconds) {
        return intervalInSeconds == 3600;
    }

    /**
     * 获取时间对应的差值-单位秒
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static Integer getTimeDiffSeconds(LocalDateTime startTime, LocalDateTime endTime) {
        return (int) LocalDateTimeUtil.between(startTime, endTime, ChronoUnit.SECONDS);
    }

    public static LocalDateTime convertUnixSecondToLocalDateTime(long unixTimestampSecond) {
        Instant instant = Instant.ofEpochSecond(unixTimestampSecond);
        ZoneId zoneId = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zoneId);
    }

    public static long convertToUnixTimestampSeconds(LocalDateTime dateTime, ZoneId originalZoneId) {
        LocalDateTime truncatedDateTime = dateTime.withSecond(0).withNano(0);
        ZonedDateTime zonedDateTime = truncatedDateTime.atZone(originalZoneId).withZoneSameInstant(ZoneId.systemDefault());
        Instant instant = zonedDateTime.toInstant();
        return instant.getEpochSecond();
    }

    public static ZonedDateTime convertToZonedDateTime(long unixTimestampSeconds) {
        Instant instantFromSeconds = Instant.ofEpochSecond(unixTimestampSeconds);
        ZoneId zoneId = ZoneId.systemDefault();
        return instantFromSeconds.atZone(zoneId);
    }

    public static LocalDateTime convertToSystemLocalDateTime(LocalDateTime dateTime, ZoneId originalZoneId) {
        LocalDateTime truncatedDateTime = dateTime.withSecond(0).withNano(0);
        ZonedDateTime zonedDateTime = truncatedDateTime.atZone(originalZoneId).withZoneSameInstant(ZoneId.systemDefault());
        return zonedDateTime.toLocalDateTime();
    }

    public static long toUnixTimestampInSeconds(LocalDateTime localDateTime) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = localDateTime.atZone(zoneId);
        Instant instant = zonedDateTime.toInstant();
        return instant.getEpochSecond();
    }
}
