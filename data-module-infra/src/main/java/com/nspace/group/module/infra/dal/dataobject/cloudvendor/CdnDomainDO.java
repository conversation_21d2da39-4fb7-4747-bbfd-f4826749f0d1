package com.nspace.group.module.infra.dal.dataobject.cloudvendor;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 *  通用CDN域名
 *
 */
@TableName("cdn_domain")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CdnDomainDO implements Serializable {
    /**
     * 主键 ID
     */
    private Long id;
    /**
     * 多租户编号
     */
    private Long tenantId;
    /**
     * 用户侧域名
     */
    private String domain;

    /**
     * cname域名
     */
    private String cname;

    /**
     * 域名所属用户ID
     */
    private Long userId;

    /**
     * 域名类型
     */
    private String type;

    /**
     * 状态 0、正常；1、停用；
     */
    private Integer status;

    /**
     * 域名归属权验证状态，0、已验证；1、待验证
     */
    private Integer ownerCheckStatus;

    /**
     * 状态 0、已生效；1、未生效
     */
    private Integer cnameStatus;

    /**
     * cname状态更新时间
     */
    private LocalDateTime cnameStatusTime;

    /**
     * 备注
     */
    private String memo;

    /**
     * 区域
     */
    private String region;

    /**
     * 扩展字段
     */
    private String extInfo;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     */
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     */
    private String updater;
    /**
     * 是否删除
     */
    private Boolean deleted;
}
