package com.nspace.group.module.infra.dal.mapper.cloudvendor;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveDomainDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 直播域名
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：17:41
 */

@DS("nspace_controller")
@Mapper
public interface LiveDomainMapper extends BaseMapperX<LiveDomainDO> {
}
