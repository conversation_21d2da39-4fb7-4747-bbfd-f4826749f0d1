package com.nspace.group.module.infra.service.analysis.usage;

import com.nspace.group.module.infra.dal.dataobject.analysis.usage.UsagePullBandwidhTrafficDO;
import com.nspace.group.module.infra.dal.mapper.analysis.usage.UsagePullBandwidhTrafficMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * 计费用量-拉流相关数据-业务实现
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：10:39
 */

@Service
public class UsagePullServiceImpl implements UsagePullService {

    @Resource
    private UsagePullBandwidhTrafficMapper usagePullBandwidhTrafficMapper;

    @Override
    public void insertBatchPullStream(Collection<UsagePullBandwidhTrafficDO> pullDOS) {
        usagePullBandwidhTrafficMapper.insertBatch(pullDOS);
    }
}
