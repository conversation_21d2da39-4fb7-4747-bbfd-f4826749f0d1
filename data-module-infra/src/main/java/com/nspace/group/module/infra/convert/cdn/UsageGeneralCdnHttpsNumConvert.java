package com.nspace.group.module.infra.convert.cdn;

import com.nspace.group.module.infra.dal.dataobject.cdn.UsageGeneralCdnHttpsNumDO;
import com.nspace.group.module.infra.service.cdn.dto.UsageGeneralCdnHttpsNumDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :UsageGeneralCdnHttpsNumConvert.java, v0.1 2025年05月28日 17:48 zhangxin Exp
 */
@Mapper
public interface UsageGeneralCdnHttpsNumConvert {

    UsageGeneralCdnHttpsNumConvert INSTANCE = Mappers.getMapper(UsageGeneralCdnHttpsNumConvert.class);

    @Mapping(target = "curTimestamp", ignore = true)
    UsageGeneralCdnHttpsNumDO toHttpsNumDO(UsageGeneralCdnHttpsNumDTO data);

    UsageGeneralCdnHttpsNumDTO getHttpsNumDTO(Long tenantId, LocalDateTime windowStart, String domain,
                                              String region, String billingRegion, Integer dataPlatform,
                                              Long httpsNum, LocalDateTime windowEnd);
}