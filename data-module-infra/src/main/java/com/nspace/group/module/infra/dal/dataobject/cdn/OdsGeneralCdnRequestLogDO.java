package com.nspace.group.module.infra.dal.dataobject.cdn;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 原始ods数据记录-通用cdn请求日志
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "tb_ods_general_cdn_request_log")
public class OdsGeneralCdnRequestLogDO {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 日志打印时间, ISO 8601标准格式
     */
    private LocalDateTime logTime;

    /**
     * 域名，例如play.test.com
     */
    private String domain;

    /**
     * 请求方法，例如GET, POST
     */
    private String method;

    /**
     * 请求协议scheme，例如http, https, rtmp, rtmps
     */
    private String scheme;

    /**
     * 请求资源
     */
    private String uri;

    /**
     * 请求参数，例如/app/test.flv?myt=2
     */
    private String uriParam;

    /**
     * 用户地址，例如*************:2932
     */
    private String clientAddr;

    /**
     * 代理IP，例如************:80
     */
    private String proxyIp;

    /**
     * 和CDN节点建立连接的IP，例如************
     */
    private String remoteIp;

    /**
     * 和CDN节点建立客户端连接的端口 80
     */
    private Integer remotePort;

    /**
     * 请求大小
     */
    private Long requestSize;

    /**
     * 请求响应时间，单位：毫秒
     */
    private Long requestTime;

    /**
     * 请求返回大小，单位：字节
     */
    private Long responseSize;

    /**
     * 响应状态码，使用HTTP的状态码定义
     */
    private Integer returnCode;

    /**
     * 应答头里表示的range信息（由源站创建），如bytes：0~99/200
     */
    private String sentHttpContentRange;

    /**
     * 服务的CDN节点IP，例如*************
     */
    private String serverAddr;

    /**
     * 服务的CDN节点服务端口。80
     */
    private Integer serverPort;

    /**
     * 实际发送body大小，单位：字节
     */
    private Long bodyBytesSent;

    /**
     * 请求的资源类型
     */
    private String contentType;

    /**
     * 命中信息（直播，动态加速除外），取值为HIT（命中）、MISS（未命中）
     */
    private String hitInfo;

    /**
     * 用户请求中Header头的range字段取值，如bytes：0~100
     */
    private String httpRange;

    /**
     * HTTP请求头中的user agent
     */
    private String userAgent;

    /**
     * 唯一请求标识符，由16字节随机生成，十六进制表示
     */
    private String requestId;

    /**
     * 上游处理机器信息
     */
    private String via;

    /**
     * 请求头中X-Forwarded-For字段
     */
    private String xforwordfor;

    /**
     * 是否内部
     */
    private Integer internal;

    /**
     * 数据来源 0:内部，1:阿里
     */
    private Integer dataPlatform;

    /**
     * 数据写入时间
     */
    private LocalDateTime curTimestamp;

    /**
     * client_geo
     */
    private String clientGeo;

    /**
     * service_geo
     */
    private String serviceGeo;

    /**
     * http_referer
     */
    private String httpReferer;

    /**
     * response_fbt_time
     */
    private Long responseFbtTime;

    /**
     * sent_http_content_length
     */
    private Long sentHttpContentLength;

    /**
     * dy_user_info
     */
    private String dyUserInfo;

    /**
     * country
     */
    private String country;

    /**
     * server_protocol
     */
    private String serverProtocol;

    /**
     * quic
     */
    private String quic;

    /**
     * last_modified
     */
    private String lastModified;

}