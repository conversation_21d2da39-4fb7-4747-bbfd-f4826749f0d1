package com.nspace.group.module.infra.service.job;

import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.framework.job.core.service.JobLogFrameworkService;
import com.nspace.group.module.infra.dal.dataobject.job.JobLogDO;

/**
 *  获得定时任务日志信息
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：16:53
 */
public interface JobLogService extends JobLogFrameworkService {
    /**
     * 获得定时任务
     *
     * @param id 编号
     * @return 定时任务
     */
    JobLogDO getJobLog(Long id);

    /**
     * 清理 exceedDay 天前的任务日志
     *
     * @param exceedDay 超过多少天就进行清理
     * @param deleteLimit 清理的间隔条数
     */
    Integer cleanJobLog(Integer exceedDay, Integer deleteLimit);

}
