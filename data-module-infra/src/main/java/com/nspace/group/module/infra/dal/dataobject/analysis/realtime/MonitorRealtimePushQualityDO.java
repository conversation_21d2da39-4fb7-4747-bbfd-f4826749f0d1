package com.nspace.group.module.infra.dal.dataobject.analysis.realtime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nspace.group.module.infra.dal.dataobject.analysis.base.BaseAnalysisDO;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 资源监控数据-实时监控-推流质量监控
 *
 * @author：<EMAIL>
 * @date： 2024/10/30
 * @time：11:30
 */

@TableName("tb_monitor_realtime_push_quality")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
public class MonitorRealtimePushQualityDO extends BaseAnalysisDO {
    /**
     * appname
     */
    private String appName;

    /**
     * 流名称
     */
    private String streamName;

    /**
     * 视频帧率, 这个周期的平均帧率
     */
    private Long videoFps;

    /**
     * 视频码率, 这个周期的平均码率，单位: bit/s
     */
    private Long videoBps;

    /**
     * 音频帧率, 这个周期的平均帧率
     */
    private Long audioFps;

    /**
     * 音频码率, 这个周期的平均码率，单位: bit/s
     */
    private Long audioBps;

    /**
     * uri 参数
     */
    private String requestUri;
}
