package com.nspace.group.module.infra.dal.mapper.analysis.realtime;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.infra.dal.dataobject.analysis.realtime.MonitorRealtimePushDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 数据监控-实时监控-推流带宽/流量
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：10:09
 */
@DS("nspace_analysis")
@Mapper
public interface MonitorRealtimePushMapper extends BaseMapperX<MonitorRealtimePushDO> {
}
