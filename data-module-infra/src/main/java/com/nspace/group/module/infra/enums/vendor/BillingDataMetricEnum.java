package com.nspace.group.module.infra.enums.vendor;

import com.nspace.group.framework.common.enums.BusinessTypeEnum;
import lombok.Getter;

import java.util.Arrays;

/**
 * 第三方计费数据指标类型枚举
 */
public enum BillingDataMetricEnum {
    PULL_TRAFFIC(BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), "直播拉流流量"),
    PULL_BANDWIDTH_PEAK(BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), "直播拉流峰值带宽"),
    PULL_BANDWIDTH_95(BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), "直播拉流95带宽"),
    PUSH_TRAFFIC(BusinessTypeEnum.BUSINESS_TYPE_LSS.getCode(), "直播推流流量"),
    TRAFFIC(BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), "CDN流量"),
    BANDWIDTH_PEAK(BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), "CDN带宽峰值"),
    BANDWIDTH_95(BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), "CDN月结95带宽"),
    HTTPS_NUM(BusinessTypeEnum.BUSINESS_TYPE_CDN.getCode(), "CDN静态HTTPS请求数");

    /**
     * 业务类型
     */
    @Getter
    private final String bizType;

    /**
     * 描述
     */
    @Getter
    private final String desc;

    BillingDataMetricEnum(String bizType, String desc) {
        this.bizType = bizType;
        this.desc = desc;
    }

    public boolean isSelf(String metricType) {
        return isBillingDataMetric(metricType) && this.name().equals(metricType);
    }

    public static boolean isBillingDataMetric(String metricType) {
        return Arrays.stream(values()).map(BillingDataMetricEnum::name).anyMatch(value -> value.equals(metricType));
    }

}
