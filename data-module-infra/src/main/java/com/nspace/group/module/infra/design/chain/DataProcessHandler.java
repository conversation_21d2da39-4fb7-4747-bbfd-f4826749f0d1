package com.nspace.group.module.infra.design.chain;

import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.infra.design.strategy.DataProcessStrategy;

public class DataProcessHandler extends AbstractDataHandler {
    private final DataProcessStrategy strategy;

    public DataProcessHandler(DataProcessStrategy strategy) {
        this.strategy = strategy;
    }

    @Override
    public boolean handle(DataContext dataContext) {
        strategy.process(dataContext);
        return doNext(dataContext);
    }
}
