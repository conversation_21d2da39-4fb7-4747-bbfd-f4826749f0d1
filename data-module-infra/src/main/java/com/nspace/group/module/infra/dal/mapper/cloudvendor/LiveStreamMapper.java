package com.nspace.group.module.infra.dal.mapper.cloudvendor;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveStreamDO;
import com.nspace.group.module.infra.service.cloudvendor.dto.LiveStreamPageReqDTO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;

/**
 * 直播流信息
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/1
 * @time：10:32
 */
@DS("nspace_controller")
@Mapper
public interface LiveStreamMapper extends BaseMapperX<LiveStreamDO> {
    default PageResult<LiveStreamDO> selectPage(LiveStreamPageReqDTO req) {
        LambdaQueryWrapperX<LiveStreamDO> queryWrapper = new LambdaQueryWrapperX<LiveStreamDO>()
                .eqIfPresent(LiveStreamDO::getDeleted, 0)
                .eqIfPresent(LiveStreamDO::getDomain, req.getDomain())
                .eqIfPresent(LiveStreamDO::getTenantId, req.getTenantId());
        queryWrapper.and(wrapper ->
                wrapper.eq(LiveStreamDO::getStatus, "ONLINE")
                        .or(subWrapper -> subWrapper.eq(LiveStreamDO::getStatus, "OFF")
                                .ge(LiveStreamDO::getEndTime, LocalDateTime.now().minusMinutes(30)))
        );
        return selectPage(req, queryWrapper);
    }
}