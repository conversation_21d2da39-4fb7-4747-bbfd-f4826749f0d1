package com.nspace.group.module.infra.service.detail.dto;

import com.nspace.group.framework.common.pojo.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 数据明细-数据明细查询条件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StreamDetailReqDTO extends PageParam {

    //接收传过来的ID
    private Long id;

    //日记记录时间范围-开始时间
    private LocalDateTime startTime;
    //日记记录时间范围-结束时间
    private LocalDateTime endTime;
    //域名信息
    private String domain;
    //租户信息
    private String tenantId;
    //主键搜索范围 开始id
    private long minId;
    //日记写入时间范围-开始时间 单位ms
    private LocalDateTime curTimestampStart;
    //日记写入时间范围-结束时间 单位ms
    private LocalDateTime curTimestampEnd;
    //日志类型 1、PUSH, 2、PULL
    private String type;
}
