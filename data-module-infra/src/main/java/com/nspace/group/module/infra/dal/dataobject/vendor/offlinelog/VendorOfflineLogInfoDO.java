package com.nspace.group.module.infra.dal.dataobject.vendor.offlinelog;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :VendorOfflineLogInfoDO.java, v0.1 2024年12月05日 18:23 zhangxin Exp
 */
@TableName(value = "vendor_offline_log_info")
@KeySequence("vendor_offline_log_info_id_seq")  // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
public class VendorOfflineLogInfoDO {
    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 三方平台code
     */
    private String platform;

    /**
     * 文件下载链接
     */
    private String fileUrl;

    /**
     * 下载链接中解析出的时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime fileTime;

    /**
     * 时间
     */
    private LocalDateTime generateTime;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小，字节
     */
    private Long fileSize;

    /**
     * 状态 0、正常；1、失败；2；处理中；
     */
    private Integer status;

    /**
     * 是否删除
     */
    private Boolean deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public LocalDateTime getGenerateTime() {
        return generateTime;
    }

    public void setGenerateTime(LocalDateTime generateTime) {
        this.generateTime = generateTime;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public LocalDateTime getFileTime() {
        return fileTime;
    }

    public void setFileTime(LocalDateTime fileTime) {
        this.fileTime = fileTime;
    }
}
