package com.nspace.group.module.infra.client.factory;

import com.nspace.group.module.infra.client.ApiClient;
import com.nspace.group.module.infra.client.auth.Authentication;

/**
 * API客户端的工厂接口
 *
 * <AUTHOR>
 * @version :ApiClientFactory.java, v0.1 2024年11月29日 10:39 luke Exp
 */
public interface ApiClientFactory {

    /**
     * 获得需要鉴权的API Client
     *
     * @param apiBasePath API请求基础路径
     * @param authentication 鉴权参数
     * @return API Client
     */
    ApiClient createApiClient(String apiBasePath, Authentication authentication);

    /**
     * 获得简单的API Client
     *
     * @param apiBasePath API请求基础路径
     * @return API Client
     */
    ApiClient createApiClient(String apiBasePath);
}
