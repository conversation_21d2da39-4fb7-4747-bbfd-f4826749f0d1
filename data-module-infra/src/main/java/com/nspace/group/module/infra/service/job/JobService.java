package com.nspace.group.module.infra.service.job;

import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.module.infra.dal.dataobject.job.JobDO;
import com.nspace.group.module.infra.service.job.dto.JobPageReqDTO;
import com.nspace.group.module.infra.service.job.dto.JobSaveReqDTO;
import org.quartz.SchedulerException;

import javax.validation.Valid;

/**
 * 定时任务 Service 接口
 *
 * @author：yang<PERSON><EMAIL>
 * @date： 2024/10/31
 * @time：16:55
 */
public interface JobService {

    /**
     * 创建定时任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createJob(@Valid JobSaveReqDTO createReqVO) throws SchedulerException;

    /**
     * 更新定时任务
     *
     * @param updateReqVO 更新信息
     */
    void updateJob(@Valid JobSaveReqDTO updateReqVO) throws SchedulerException;

    /**
     * 更新定时任务的状态
     *
     * @param id 任务编号
     * @param status 状态
     */
    void updateJobStatus(Long id, Integer status) throws SchedulerException;

    /**
     * 触发定时任务
     *
     * @param id 任务编号
     */
    void triggerJob(Long id) throws SchedulerException;

    /**
     * 删除定时任务
     *
     * @param id 编号
     */
    void deleteJob(Long id) throws SchedulerException;

    /**
     * 获得定时任务
     *
     * @param id 编号
     * @return 定时任务
     */
    JobDO getJob(Long id);


    PageResult<JobDO> getJobPage(JobPageReqDTO pageReqVO);
}
