package com.nspace.group.module.infra.dal.mapper.cloudvendor;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.VendorAccountDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 第三方云服务账号
 *
 * @author：yangguow<PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：15:10
 */
@DS("nspace_controller")
@Mapper
public interface VendorAccountMapper extends BaseMapperX<VendorAccountDO> {
}
