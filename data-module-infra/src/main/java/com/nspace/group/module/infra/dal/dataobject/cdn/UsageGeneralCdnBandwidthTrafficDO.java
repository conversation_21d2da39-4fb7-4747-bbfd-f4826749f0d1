package com.nspace.group.module.infra.dal.dataobject.cdn;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 计费用量-通用cdn流量和带宽统计
 *
 * <AUTHOR>
 * @since 2025-03-13 17:04:32
 */
@Data
@TableName("tb_usage_general_cdn_bandwidth_traffic")
public class UsageGeneralCdnBandwidthTrafficDO {

    //窗口开始时间
    @TableId(type = IdType.AUTO)
    private LocalDateTime windowStart;
    //租户ID
    private Long tenantId;
    //区域
    private String region;
    //流域名
    private String domain;
    //数据来源
    private Integer dataPlatform;
    //窗口结束时间
    private LocalDateTime windowEnd;
    //带宽值(bps)
    private BigDecimal bandwidth;
    //流量(B)
    private Long traffic;
    //计费区域
    private String billingRegion;
    //数据写入时间
    private LocalDateTime curTimestamp;

}

