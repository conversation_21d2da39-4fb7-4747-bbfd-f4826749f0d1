package com.nspace.group.module.infra.dal.mapper.analysis.realtime;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.infra.dal.dataobject.analysis.realtime.MonitorRealtimePushQualityDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 资源监控数据-实时监控-推流质量监控
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：10:11
 */
@DS("nspace_analysis")
@Mapper
public interface MonitorRealtimePushQualityMapper extends BaseMapperX<MonitorRealtimePushQualityDO> {
}
