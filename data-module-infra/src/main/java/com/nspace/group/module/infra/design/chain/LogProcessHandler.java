package com.nspace.group.module.infra.design.chain;

import com.nspace.group.module.infra.design.context.DataContext;
import com.nspace.group.module.infra.design.strategy.LogDataProcessStrategy;

public class LogProcessHandler extends AbstractDataHandler {
    private final LogDataProcessStrategy strategy;

    public LogProcessHandler(LogDataProcessStrategy strategy) {
        this.strategy = strategy;
    }

    @Override
    public boolean handle(DataContext dataContext) {
        strategy.process(dataContext);
        return doNext(dataContext);
    }
}
