package com.nspace.group.module.infra.dal.dataobject.cloudvendor;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 第三方云服务账号
 *
 * @author：<EMAIL>
 * @date： 2024/10/31
 * @time：14:57
 */
@TableName("vendor_account")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VendorAccountDO implements Serializable {
    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 账号
     */
    private String account;

    /**
     * 平台名称
     */
    private String platform;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 秘钥 ID
     */
    private String secretId;

    /**
     * 秘钥 Key
     */
    private String secretKey;

    /**
     * 接入点
     */
    private String endpoint;

    /**
     * 状态，默认为 'NORMAL'
     */
    private String status;

    /**
     * 绑定的租户 ID
     */
    private Long bindTenantId;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 扩展信息
     */
    private String extInfo;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updater;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标识，0 表示未删除
     */
    private Short deleted;
}
