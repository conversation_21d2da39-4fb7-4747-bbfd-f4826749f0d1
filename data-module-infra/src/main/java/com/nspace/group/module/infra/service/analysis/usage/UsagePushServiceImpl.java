package com.nspace.group.module.infra.service.analysis.usage;

import com.nspace.group.module.infra.dal.dataobject.analysis.usage.UsagePushBandwidhTrafficDO;
import com.nspace.group.module.infra.dal.mapper.analysis.usage.UsagePushBandwidhTrafficMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * 计费用量-推流相关数据-业务实现
 *
 * @author：<EMAIL>
 * @date： 2024/10/31
 * @time：10:40
 */

@Service
public class UsagePushServiceImpl implements UsagePushService {
    @Resource
    private UsagePushBandwidhTrafficMapper usagePushBandwidhTrafficMapper;

    @Override
    public void insertBatchPushStream(List<UsagePushBandwidhTrafficDO> pushDOS) {
        usagePushBandwidhTrafficMapper.insertBatch(pushDOS);
    }
}
