package com.nspace.group.module.infra.service.analysis.usage;

import com.nspace.group.module.infra.dal.dataobject.analysis.usage.UsagePushBandwidhTrafficDO;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 计费用量-推流相关数据
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：10:34
 */
public interface UsagePushService {
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void insertBatchPushStream(List<UsagePushBandwidhTrafficDO> pushDOS);
}
