package com.nspace.group.module.infra.dal.mapper.analysis.usage;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.infra.dal.dataobject.analysis.usage.UsagePullBandwidhTrafficDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 计费用量-拉流带宽和流量统计
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：10:12
 */
@DS("nspace_analysis")
@Mapper
public interface UsagePullBandwidhTrafficMapper extends BaseMapperX<UsagePullBandwidhTrafficDO> {
}
