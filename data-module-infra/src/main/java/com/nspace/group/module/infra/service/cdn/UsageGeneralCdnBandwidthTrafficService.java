package com.nspace.group.module.infra.service.cdn;

import com.nspace.group.module.infra.service.cdn.dto.UsageGeneralCdnBandwidthTrafficDTO;

import java.util.List;

/**
 * 计费用量-通用cdn流量和带宽统计服务接口
 *
 * <AUTHOR>
 * @since 2025-03-13 17:04:42
 */
public interface UsageGeneralCdnBandwidthTrafficService {
    void saveTrafficData(List<UsageGeneralCdnBandwidthTrafficDTO> dataList);

    void saveBandwidthData(List<UsageGeneralCdnBandwidthTrafficDTO> dataList);
}

