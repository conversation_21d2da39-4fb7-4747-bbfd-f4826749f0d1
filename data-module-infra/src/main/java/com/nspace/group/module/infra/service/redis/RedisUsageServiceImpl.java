package com.nspace.group.module.infra.service.redis;

import com.nspace.group.framework.common.util.number.NumberUtils;
import com.nspace.group.framework.common.util.object.ObjectUtils;
import com.nspace.group.framework.common.util.string.StrUtils;
import com.nspace.group.module.infra.constant.RedisKeyConstants;
import com.nspace.group.module.infra.dal.dataobject.redis.usage.UsageRedisDAO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 计费用量-redis操作
 *
 * @author：yang<PERSON><EMAIL>
 * @date： 2024/11/22
 * @time：16:11
 */
@Slf4j
@Service
public class RedisUsageServiceImpl implements RedisUsageService {

    @Resource
    private UsageRedisDAO usageRedisDAO;

    private static final Map<String, Long> EMPTY_DIFF_MAP = Map.of("diffBandwidth", 0L, "diffFlux", 0L);

    /**
     * redis 过期时间
     */
    private static final int DEFAULT_EXPIRE_TIME = 86400;

    /**
     * 计算差异并更新 Redis
     */
    @Override
    public Map<String, Long> calculateDiffAndUpdateRedis(String hashKey, long newBandwidth, long newFlux) {
        if (ObjectUtils.isNullOrEmpty(hashKey)) {
            return EMPTY_DIFF_MAP;
        }
        Map<Object, Object> redisData = usageRedisDAO.entries(hashKey);

        long oldBandwidth = getLong(redisData, "bandwidth");
        long oldFlux = getLong(redisData, "flux");

        long diffBandwidth = Math.max(0, newBandwidth - oldBandwidth);
        long diffFlux = Math.max(0, newFlux - oldFlux);

        if (diffBandwidth > 0 || diffFlux > 0) {
            usageRedisDAO.putAll(hashKey, Map.of("bandwidth", newBandwidth, "flux", newFlux), DEFAULT_EXPIRE_TIME);
        }

        return Map.of("diffBandwidth", diffBandwidth, "diffFlux", diffFlux);
    }

    /**
     * 获取 Redis 数据的长整型值，避免重复转换
     */
    private long getLong(Map<Object, Object> redisData, String key) {
        return NumberUtils.parseLong(redisData.getOrDefault(key, "0").toString());
    }

    /**
     * 获取拉流的 Redis 键
     */
    @Override
    public String getPullKey(Integer platform, String domain, String usageTime, String region) {
        String s = StrUtils.hashString(domain, 32);
        return String.format(RedisKeyConstants.FUSION_USAGE_PULL_KEY, platform, s, region, usageTime);
    }

    /**
     * 获取推流的 Redis 键
     */
    @Override
    public String getPushKey(Integer platform, String domain, String usageTime, String region) {
        String s = StrUtils.hashString(domain, 32);
        return String.format(RedisKeyConstants.FUSION_USAGE_PUSH_KEY, platform, s, region, usageTime);
    }
}
