package com.nspace.group.module.infra.enums.analysis;


import lombok.Getter;

/**
 * 日志用途定义
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/26
 * @time：18:08
 */
@Getter
public enum LogPurposeEnum {
    ALL(0, "计费用量以及监控"),
    MONITOR(1, "监控"),
    USAGE(2, "计费用量"),
    ;

    private final Integer code;
    private final String desc;

    LogPurposeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}

