package com.nspace.group.module.infra.service.detail;

import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.infra.service.detail.dto.LiveCdnLogReqDTO;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;

import java.util.List;

/**
 * 原始ods数据记录-推流计费日志表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-18 16:46:39
 */
public interface OdsBillingLogStreamPushService {

    List<OdsBillingLogStreamPushDO> getPushDetailList(StreamDetailReqDTO reqDTO);

    List<OdsBillingLogStreamPushDO> getPushLogList(LiveCdnLogReqDTO reqDTO);

    List<OdsBillingLogStreamPushDO> getDelayedPushLogList(List<Long> logIds);
}

