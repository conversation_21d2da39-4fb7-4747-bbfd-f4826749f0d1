package com.nspace.group.module.infra.dal.dataobject.redis.usage;

import com.nspace.group.framework.common.util.object.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.nspace.group.framework.common.util.object.ObjectUtils.isNullOrEmpty;

/**
 * Redis 操作工具类 - 用于计费用量操作
 * 提供线程安全、高性能的操作接口，减少代码冗余
 *
 * @author：
 * @date： 2024/11/22
 * @time：11:09
 */
@Component
@Slf4j
public class UsageRedisDAO {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 保存一个 Key-Value 对到 Redis，带过期时间
     *
     * @param key        Redis 键
     * @param value      Redis 值
     * @param expireTime 过期时间（秒）
     */
    public void set(String key, String value, Long expireTime) {
        if (isNullOrEmpty(key, value)) {
            log.warn("Invalid Redis set operation: key or value is null/empty. key={}, value={}", key, value);
            return;
        }
        try {
            stringRedisTemplate.opsForValue().set(key, value, expireTime, TimeUnit.SECONDS);
            log.debug("Redis set operation succeeded. key={}, value={}, expireTime={}s", key, value, expireTime);
        } catch (Exception e) {
            log.error("Redis set operation failed. key={}, value={}", key, value, e);
        }
    }

    /**
     * 获取指定 Key 的值
     *
     * @param key Redis 键
     * @return 值，如果不存在则返回 null
     */
    public String get(String key) {
        if (isNullOrEmpty(key)) {
            log.warn("Invalid Redis get operation: key is null/empty");
            return null;
        }
        try {
            return stringRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            log.error("Redis get operation failed. key={}", key, e);
            return null;
        }
    }

    /**
     * 保存一个 Hash 到 Redis 并设置过期时间
     *
     * @param key        Redis 键
     * @param values     Hash 数据
     * @param expireTime 过期时间（秒）
     */
    public void putAll(String key, Map<String, Object> values, long expireTime) {
        if (ObjectUtils.isNullOrEmpty(key) || ObjectUtils.isNullOrEmpty(values)) {
            log.warn("Invalid Redis putAll operation: key or values are null/empty. key={}, values={}", key, values);
            return;
        }
        try {
            Map<String, String> stringMap = values.entrySet().stream()
                    .collect(Collectors.toMap(
                            entry -> String.valueOf(entry.getKey()),
                            entry -> String.valueOf(entry.getValue())
                    ));
            stringRedisTemplate.opsForHash().putAll(key, stringMap);
            setExpireTime(key, expireTime);
            log.debug("Redis putAll operation succeeded. key={}, values={}, expireTime={}s", key, values, expireTime);
        } catch (Exception e) {
            log.error("Redis putAll operation failed. key={}, values={}", key, values, e);
        }
    }

    /**
     * 获取指定 Key 的 Hash 所有字段和值
     *
     * @param key Redis 键
     * @return Hash 的所有字段和值，若不存在返回空 Map
     */
    public Map<Object, Object> entries(String key) {
        if (isNullOrEmpty(key)) {
            log.warn("Invalid Redis entries operation: key is null/empty");
            return Map.of();
        }
        try {
            return stringRedisTemplate.opsForHash().entries(key);
        } catch (Exception e) {
            log.error("Redis entries operation failed. key={}", key, e);
            return Map.of();
        }
    }

    /**
     * 设置指定 Key 的过期时间
     *
     * @param key        Redis 键
     * @param expireTime 过期时间（秒）
     */
    private void setExpireTime(String key, Long expireTime) {
        if (expireTime == null || expireTime <= 0) {
            log.warn("Invalid expireTime for Redis key: {}. expireTime={}", key, expireTime);
            return;
        }
        try {
            stringRedisTemplate.expire(key, expireTime, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Redis setExpireTime operation failed. key={}, expireTime={}", key, expireTime, e);
        }
    }
}