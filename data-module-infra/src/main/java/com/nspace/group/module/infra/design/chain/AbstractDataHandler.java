package com.nspace.group.module.infra.design.chain;

import com.nspace.group.module.infra.design.context.DataContext;

public abstract class AbstractDataHandler implements DataHandler {
    private DataHandler nextLogHandler;

    @Override
    public void setNext(DataHandler nextHandler) {
        this.nextLogHandler = nextHandler;
    }

    protected boolean doNext(DataContext logContext) {
        if (nextLogHandler != null) {
            return nextLogHandler.handle(logContext);
        }
        return false;
    }
}
