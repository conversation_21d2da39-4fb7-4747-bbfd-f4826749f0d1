package com.nspace.group.module.infra.service.dict;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.common.enums.CommonStatusEnum;
import com.nspace.group.module.infra.dal.dataobject.dict.DictDataDO;
import com.nspace.group.module.infra.dal.mapper.dict.DictDataMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

/**
 * 字典数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@DS("nspace_controller")
public class DictDataServiceImpl implements DictDataService {

    /**
     * 排序 dictType > sort
     */
    private static final Comparator<DictDataDO> COMPARATOR_TYPE_AND_SORT = Comparator
            .comparing(DictDataDO::getDictType)
            .thenComparingInt(DictDataDO::getSort);

    @Resource
    private DictDataMapper dictDataMapper;

    @Override
    public List<DictDataDO> getEnabledDictDataListByType(String dictType) {
        List<DictDataDO> list = dictDataMapper.selectListByTypeAndStatus(dictType, CommonStatusEnum.ENABLE.getStatus());
        list.sort(COMPARATOR_TYPE_AND_SORT);
        return list;
    }
}
