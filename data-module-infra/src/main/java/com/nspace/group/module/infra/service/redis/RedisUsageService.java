package com.nspace.group.module.infra.service.redis;

import java.util.Map;

/**
 * 计费用量-redis操作
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/22
 * @time：16:08
 */

public interface RedisUsageService {
    Map<String, Long> calculateDiffAndUpdateRedis(String hashKey, long newBandwidth, long newFlux);

    String getPullKey(Integer platform, String domain, String usageTime, String region);

    String getPushKey(Integer platform, String domain, String usageTime, String region);
}
