package com.nspace.group.module.infra.dal.dataobject.analysis.realtime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.nspace.group.module.infra.dal.dataobject.analysis.base.BaseAnalysisDO;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

/**
 * 数据监控-实时监控-推流带宽/流量
 *
 * @author：<EMAIL>
 * @date： 2024/10/30
 * @time：11:30
 */

@TableName("tb_monitor_realtime_push_stream")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
public class MonitorRealtimePushDO extends BaseAnalysisDO {
    /**
     * appname
     */
    private String appName;

    /**
     * 流名称
     */
    private String streamName;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 运营商英文名称
     */
    private String ispName;

    /**
     * 带宽值(bps)
     */
    private Long bandwidth;

    /**
     * 流量(B)
     */
    private Long traffic;

    /**
     * uri 参数
     */
    private String requestUri;

    /**
     * 省份名称
     */
    private String provinceCode;

    /**
     * 运营商英文名称
     */
    private String ispCode;
}
