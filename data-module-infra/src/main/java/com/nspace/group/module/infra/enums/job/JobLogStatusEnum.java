package com.nspace.group.module.infra.enums.job;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 任务日志状态枚举
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：16:48
 */
@Getter
@AllArgsConstructor
public enum JobLogStatusEnum {

    RUNNING(0), // 运行中
    SUCCESS(1), // 成功
    FAILURE(2); // 失败
    /**
     * 状态
     */
    private final Integer status;
}

