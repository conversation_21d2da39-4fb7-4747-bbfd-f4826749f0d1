package com.nspace.group.module.infra.dal.dataobject.analysis.base;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数据分析基础类
 *
 * @author：yangguow<PERSON>@gycloud.com
 * @date： 2024/10/30
 * @time：11:01
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public abstract class BaseAnalysisDO implements Serializable {
    /**
     * 多租户编号
     */
    protected Long tenantId;
    /**
     * 域名
     */
    protected String domain;
    /**
     * 窗口开始时间
     */
    @TableId(value = "window_start", type = IdType.NONE)
    protected LocalDateTime windowStart;
    /**
     * 区域 默认CN
     */
    protected String region;
    /**
     * 窗口结束时间
     */
    protected LocalDateTime windowEnd;

    /**
     * 数据来源，默认值为0
     */
    private Integer dataPlatform = 0;
}
