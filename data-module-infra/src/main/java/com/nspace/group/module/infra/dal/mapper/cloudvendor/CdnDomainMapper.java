package com.nspace.group.module.infra.dal.mapper.cloudvendor;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.CdnDomainDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通用CDN域名Mapper
 *
 */

@DS("nspace_controller")
@Mapper
public interface CdnDomainMapper extends BaseMapperX<CdnDomainDO> {
}
