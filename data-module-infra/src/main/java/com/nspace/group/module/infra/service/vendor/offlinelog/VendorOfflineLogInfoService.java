package com.nspace.group.module.infra.service.vendor.offlinelog;


import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogInfoDTO;

import java.util.List;

/**
 * 第三方离线日志文件信息 Service 接口
 *
 * <AUTHOR>
 */
public interface VendorOfflineLogInfoService {


    /**
     * 根据Url列表获取之前处理过的日志文件信息
     *
     * @param tenantId   租户ID
     * @param domain     域名
     * @param platform   三方平台code
     * @param urlsForDomain 文件链接列表
     * @return List <VendorOfflineLogInfoDTO>
     */
    List<VendorOfflineLogInfoDTO> getLogInfosByUrls(Long tenantId, String domain, String platform, List<String> urlsForDomain);

    /**
     * 批量保存日志文件处理记录
     *
     * @param logInfos 日志文件信息DTO列表
     */
    void saveLogInfos(List<VendorOfflineLogInfoDTO> logInfos);

    /**
     * 保存日志文件处理记录
     *
     * @param logInfo 日志文件信息DTO
     */
    void saveLogInfo(VendorOfflineLogInfoDTO logInfo);
}
