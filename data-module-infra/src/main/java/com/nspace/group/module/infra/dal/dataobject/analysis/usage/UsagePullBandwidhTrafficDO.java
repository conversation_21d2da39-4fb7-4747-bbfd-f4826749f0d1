package com.nspace.group.module.infra.dal.dataobject.analysis.usage;

import com.baomidou.mybatisplus.annotation.TableName;
import com.nspace.group.module.infra.dal.dataobject.analysis.base.BaseAnalysisDO;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 计费用量-拉流带宽和流量统计
 *
 * @author：<EMAIL>
 * @date： 2024/10/30
 * @time：12:07
 */
@TableName("tb_usage_stream_pull_bandwidh_traffic")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@SuperBuilder
public class UsagePullBandwidhTrafficDO extends BaseAnalysisDO {

    /**
     * 数据汇总日期
     */
    private LocalDate curDate;
    /**
     * 直播类型，1:标准直播，2:超低延时直播
     */
    private Integer liveType;

    /**
     * 带宽值(bps)
     */
    private Long bandwidth;

    /**
     * 流量(B)
     */
    private Long traffic;

    /**
     * 计费区域信息
     */
    protected String billingRegion;
}
