package com.nspace.group.module.infra.design.context;

import cn.hutool.core.lang.Assert;

/**
 * 日志投递上下文
 *
 * <AUTHOR>
 */
public abstract class LogContext {

    // 日志投递目标
    private final String targetType;

    // 单次投递数量限制
    private final Integer logLimit;

    // 批次查询限制
    private final Integer batchLimit;

    // 重试次数
    private final Integer retryCount;

    // 双方约定的唯一标识
    private final String privateKey;
    // ak
    private final String accessKey;

    protected LogContext(String targetType, Integer logLimit, Integer batchLimit, Integer retryCount,
                         String privateKey, String accessKey) {
        Assert.notBlank(targetType);
        Assert.notBlank(privateKey);
        Assert.notBlank(accessKey);
        this.targetType = targetType;
        this.logLimit = logLimit;
        this.batchLimit = batchLimit;
        this.retryCount = retryCount;
        this.privateKey = privateKey;
        this.accessKey = accessKey;
    }

    public String getTargetType() {
        return targetType;
    }

    public Integer getLogLimit() {
        return logLimit;
    }

    public Integer getBatchLimit() {
        return batchLimit;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public Integer getRetryCount() {
        return retryCount;
    }
}
