package com.nspace.group.module.infra.design.chain;

import com.nspace.group.module.infra.design.context.LogContext;

abstract class AbstractLogHandler implements LogHandler {
    private LogHandler nextLogHandler;

    @Override
    public void setNext(LogHandler nextLogHandler) {
        this.nextLogHandler = nextLogHandler;
    }

    protected boolean doNext(LogContext logContext) {
        if (nextLogHandler != null) {
            return nextLogHandler.handle(logContext);
        }
        return false;
    }
}
