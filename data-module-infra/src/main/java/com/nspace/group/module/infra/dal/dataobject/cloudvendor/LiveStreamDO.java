package com.nspace.group.module.infra.dal.dataobject.cloudvendor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 直播流信息
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/1
 * @time：10:28
 */
@TableName("live_stream")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LiveStreamDO implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String domain;
    private String playDomain;
    private String appName;
    private String streamName;
    private String status;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private short deleted;
    private long tenantId;
}
