package com.nspace.group.module.infra.constant;

/**
 * 腾讯云数据同步常用key
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/11/22
 * @time：10:41
 */
public class RedisKeyConstants {
    /**
     * 计费用量-拉流数据
     */
    public static final String FUSION_USAGE_PULL_KEY = "dw:u:pull:%s:%s:%s:%s"; // dtworks:tx:domain:20241122110500
    /**
     * 计费用量-推流数据
     */
    public static final String FUSION_USAGE_PUSH_KEY = "dw:u:push:%s:%s:%s:%s"; // dtworks:tx:push:domain:20241122110500
    /**
     * 实时监控-拉流质量数据
     */
    public static final String FUSION_REALTIME_PUSH_QUALITY_KEY = "dw:r:push:%s:%s";
    /**
     * 日志投递-云服务配置
     */
    public static final String LOG_DELIVERY_CONFIG_PREFIX = "dw:log:delivery:config";
    /**
     * 计费用量-第三方数据
     */
    public static final String FUSION_USAGE_DATA_KEY = "dw:u:%s:%s:%s"; //dw:u:ct:metricType:tenant_id+domain
}