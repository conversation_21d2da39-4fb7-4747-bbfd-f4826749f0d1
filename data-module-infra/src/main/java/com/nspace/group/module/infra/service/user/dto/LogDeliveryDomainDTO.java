package com.nspace.group.module.infra.service.user.dto;


import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  日志投递域名配置DTO
 *
 */
@Data
@NoArgsConstructor
public class LogDeliveryDomainDTO {

    /**
     * 多租户编号
     */
    private Long tenantId;
    /**
     * 用户侧域名
     */
    private String domain;

    /**
     * 域名类型 PUSH、推流；PULL、播流
     */
    private String type;

    /**
     * 投递Url
     */
    private String endpoint;

    /**
     * 投递path
     */
    private String path;

    /**
     * access_key
     */
    private String ak;

    /**
     * secret_key
     */
    private String sk;

    /**
     * 单次投递限制
     */
    private Integer limit;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 服务编码
     */
    private String serviceCode;

    //------阿里云日志投递相关------
    /**
     * 日志投递项目
     */
    private String project;

    /**
     * 日志投递日志库
     */
    private String logStore;

}
