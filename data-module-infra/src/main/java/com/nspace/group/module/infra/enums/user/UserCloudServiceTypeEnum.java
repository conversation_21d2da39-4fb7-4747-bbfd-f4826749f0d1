package com.nspace.group.module.infra.enums.user;

import java.util.Arrays;

/**
 * 用户开通云服务类型
 *
 */
public enum UserCloudServiceTypeEnum {

    GYLSS("gylss", "直播云服务"),
    GYCDN("gycdn", "CDN");

    private final String code;
    private final String name;

    UserCloudServiceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public boolean isSelf(String type) {
        return isUserCloudServiceType(type) && this.getCode().equals(type);
    }

    public static boolean isUserCloudServiceType(String type) {
        return Arrays.stream(values()).map(UserCloudServiceTypeEnum::getCode).anyMatch(value -> value.equals(type));
    }

    public String getCode() {
        return code;
    }


    public String getName() {
        return name;
    }
}