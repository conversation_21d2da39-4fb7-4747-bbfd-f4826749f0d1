package com.nspace.group.module.infra.dal.mapper.detail;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.service.detail.dto.LiveCdnLogReqDTO;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 原始ods数据记录-拉流计费日志表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-18 16:41:50
 */
@Mapper
@DS("nspace_analysis")
public interface OdsBillingLogStreamPullMapper extends BaseMapperX<OdsBillingLogStreamPullDO> {

    List<OdsBillingLogStreamPullDO> getLogDetails(StreamDetailReqDTO reqDTO);

    default List<OdsBillingLogStreamPullDO> getPullLogList(LiveCdnLogReqDTO reqDTO) {
        String domain = reqDTO.getDomain();
        LocalDateTime logEndTime = reqDTO.getLogEndTime();
        Integer logLimit = reqDTO.getLogLimit();
        LambdaQueryWrapperX<OdsBillingLogStreamPullDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(OdsBillingLogStreamPullDO::getDomain, domain)
                .eq(OdsBillingLogStreamPullDO::getInternal, 0)
                .gt(OdsBillingLogStreamPullDO::getLogTime, logEndTime)
                .le(OdsBillingLogStreamPullDO::getLogTime, reqDTO.getTimeUpperLimit())
                .and(wrapper -> wrapper
                        .ne(OdsBillingLogStreamPullDO::getStreamProtocol, "hls")
                        .or()
                        .nested(i -> i.gt(OdsBillingLogStreamPullDO::getBytesSent, 0)
                                .eq(OdsBillingLogStreamPullDO::getStreamProtocol, "hls"))
                )
                .orderByAsc(OdsBillingLogStreamPullDO::getLogTime);
        Page<OdsBillingLogStreamPullDO> page = new Page<>(1, logLimit);
        return selectPage(page.setSearchCount(false), queryWrapper).getRecords();
    }

    default List<OdsBillingLogStreamPullDO> getDelayedPullLogList(List<Long> logIds) {
        LambdaQueryWrapperX<OdsBillingLogStreamPullDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(OdsBillingLogStreamPullDO::getId, logIds);
        return selectList(queryWrapper);
    }
}

