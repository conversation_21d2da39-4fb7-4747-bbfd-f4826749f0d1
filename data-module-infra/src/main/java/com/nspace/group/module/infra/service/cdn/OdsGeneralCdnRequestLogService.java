package com.nspace.group.module.infra.service.cdn;

import com.nspace.group.module.infra.dal.dataobject.cdn.OdsGeneralCdnRequestLogDO;
import com.nspace.group.module.infra.service.cdn.dto.GeneralCdnRequestLogReqDTO;

import java.util.List;

/**
 * 原始ods数据记录-通用cdn请求日志的服务接口
 *
 * <AUTHOR>
 * @since 2025-03-19 09:38:23
 */
public interface OdsGeneralCdnRequestLogService {

    List<OdsGeneralCdnRequestLogDO> getRequestLogList(GeneralCdnRequestLogReqDTO reqDTO);

    List<OdsGeneralCdnRequestLogDO> getDelayedRequestLogList(List<Long> logIds);
}
