package com.nspace.group.module.infra.service.cloudvendor;

import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveStreamDO;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.VendorAccountDO;
import com.nspace.group.module.infra.service.cloudvendor.dto.LiveStreamPageReqDTO;
import com.nspace.group.module.infra.service.cloudvendor.dto.VendorAccountWithDomainsDTO;

import java.util.List;
import java.util.Map;

/**
 * 获取云厂商账号信息
 *
 * @author：yang<PERSON><EMAIL>
 * @date： 2024/10/31
 * @time：15:15
 */
public interface VendorAccountService {
    /**
     * 获取指定平台和业务类型的账号列表
     *
     * @param platform 云平台类型（如阿里云、腾讯云）
     * @param bizType  业务类型
     * @return 云厂商账号列表
     */
    List<VendorAccountDO> getAccountList(List<String> platform, String bizType);

    /**
     * 获取指定平台账号及关联域名信息
     *
     * @param platform 租户ID
     * @param bizType  业务类型
     * @param domainType 域名类型
     * @return 包含域名信息的云厂商账号列表
     */
    List<VendorAccountWithDomainsDTO> getVendorAccountDomains(List<String> platform, String bizType, String domainType);

    /**
     * 获取指定平台账号对应直播流信息
     * @param req
     * @return
     */
    PageResult<LiveStreamDO> getVendorAccountStreamList(LiveStreamPageReqDTO req);

    /**
     * 获取账号或者域名对应的流信息
     * @param domainType
     * @param tenantId
     * @return
     */
    Map<String, String> getVendorAccountStreamMap(String domainType, Long tenantId);

    /**
     * 获取云厂商与dataPlatform对应关系
     *
     * @return 三方平台与dataPlatform对应关系
     */
    Map<String, Integer> getVendorPlatformNumMap();
}