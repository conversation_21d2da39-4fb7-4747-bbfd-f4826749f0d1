package com.nspace.group.module.infra.mq.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 推拉流相关日志信息
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2025/1/7
 * @time：16:00
 */

@Data
@Slf4j
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OdsStreamLogMessage extends JsonMessage {


    /**
     * 机器名
     */
    private String hostname;
    /**
     * 请求ID，16进制字符串
     */
    private String requestId;
    /**
     * 请求时间，ISO 8601 standard format, example: 1970-09-28T12:00:00+06:00
     */
    private String logTime;
    /**
     * 第几轮打印，大于等于0
     */
    private Integer round;
    /**
     * 是否是结束的那一次，0或1
     */
    private Integer end;
    /**
     * 分类，pull:拉流，push:推流
     */
    private String category;

    /**
     * 流协议，rtmp，http_flv
     */
    private String streamProtocol;

    /**
     * 是否是内部请求，0或1
     */
    private Integer internal;

    /**
     * 当前周期时间长度, 单位s, 例如60
     */
    private Integer duration;

    /**
     * 从建联到现在经历的时间，单位s, 比如29393
     */
    private Long totalDuration;

    /**
     * 用户地址, 例如*************:2932
     */
    private String clientAddr;

    /**
     * 服务端地址, 例如************:80。依赖LoadBalancer传递，目前获取不到
     */
    private String serverAddr;

    /**
     * 协议scheme, 例如http,https,rtmp,rtmps
     */
    private String scheme;

    /**
     * 请求方式 GET, POST, ...
     */
    private String httpMethod;

    /**
     * 域名，例如play.test.com
     */
    private String domain;

    /**
     * 请求uri,包含args. 例如 /app/test.flv?myt=2
     */
    private String requestUri;

    /**
     * response状态码，使用http的状态码定义
     */
    private Integer status;

    /**
     * 总发送字节数
     */
    private Long bytesSent;

    /**
     * 这个周期发送的字节数
     */
    private Long intervalBytesSent;

    /**
     * 这个周期接收的字节数
     */
    private Long intervalBytesRecv;

    /**
     * 总接收字节数
     */
    private Long bytesRecv;

    /**
     * 请求时间
     */
    private String connectTime;

    /**
     * 从收到请求，到接收到第一个byte，中间消耗的时间，单位秒，精度到毫秒. 例如: 3.234
     */
    private Float firstByteRecvTime;

    /**
     * 协议版本, 例如“HTTP/1.0”, “HTTP/1.1”, “HTTP/2.0”, or “HTTP/3.0”
     */
    private String serverProtocol;

    /**
     * 视频帧率, 这个周期的平均帧率
     */
    private Long videoFps;

    /**
     * 视频码率, 这个周期的平均码率，单位: bit/s
     */
    private Long videoBps;

    /**
     * 音频帧率, 这个周期的平均帧率
     */
    private Long audioFps;
    /**
     * 音频码率, 这个周期的平均码率，单位: bit/s
     */
    private Long audioBps;

    /**
     * 客户端ip对应的国家
     */
    private String country;

    /**
     * 客户端ip对应的省份
     */
    private String province;

    /**
     * 客户端ip对应的运营商名称
     */
    private String ispName;

    /**
     * 平台类型：0:NSPACE 1:腾讯云 2:中国电信 3:七牛云
     */
    private Integer dataPlatform;

    /**
     * 日志用途  @see com.nspace.group.module.infra.enums.analysis.LogPurposeEnum
     */
    private Integer logPurpose;

    /**
     * referer
     */
    private String referer;

    /**
     * 建立连接到第一个gop发出的时间，单位ms
     */
    private Integer firstGopSentTimeMs;

    private String via;

    /**
     * user_agent
     */
    private String userAgent;

    /**
     * rewrite 过的uri
     */
    private String rewriteUri;

    /**
     * 卡顿次数
     */
    private Integer discontinuousCount;

    /**
     * 卡顿时间
     */
    private Integer discontinuousTime;
    /**
     * 回源帧率
     */
    private Float sourceStreamFps;

    /**
     * I帧的平均间隔，单位ms
     */
    private Integer avgGopSizeMs;

    private Integer videoMaxGapMs;

    /**
     * 视频丢帧率
     */
    private Double videoDroppedRatio;

    private String err;

    /**
     * 上游节点信息
     */
    private String upstreamNode;
    /**
     * 视频编码格式
     */

    private String vCodec;

    /**
     * 音频编码格式
     */
    private String aCodec;
}
