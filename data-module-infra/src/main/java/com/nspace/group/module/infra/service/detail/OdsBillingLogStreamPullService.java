package com.nspace.group.module.infra.service.detail;

import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.service.detail.dto.LiveCdnLogReqDTO;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;

import java.util.List;

/**
 * 原始ods数据记录-拉流计费日志表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-18 16:42:04
 */
public interface OdsBillingLogStreamPullService {

    List<OdsBillingLogStreamPullDO> getPullDetailList(StreamDetailReqDTO reqDTO);

    List<OdsBillingLogStreamPullDO> getPullLogList(LiveCdnLogReqDTO reqDTO);

    List<OdsBillingLogStreamPullDO> getDelayedPullLogList(List<Long> logIds);
}

