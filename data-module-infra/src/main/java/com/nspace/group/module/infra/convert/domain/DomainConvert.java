package com.nspace.group.module.infra.convert.domain;

import com.nspace.group.module.infra.dal.dataobject.cloudvendor.CdnDomainDO;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveDomainDO;
import com.nspace.group.module.infra.service.cloudvendor.dto.GenericDomainDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @version :DomainConvert.java, v0.1 2025年05月26日 10:21 zhangxin Exp
 */
@Mapper
public interface DomainConvert {

    DomainConvert INSTANCE = Mappers.getMapper(DomainConvert.class);

    GenericDomainDTO fromLiveDomainDO(LiveDomainDO domainDO);

    GenericDomainDTO fromCdnDomainDO(CdnDomainDO domainDO);

}