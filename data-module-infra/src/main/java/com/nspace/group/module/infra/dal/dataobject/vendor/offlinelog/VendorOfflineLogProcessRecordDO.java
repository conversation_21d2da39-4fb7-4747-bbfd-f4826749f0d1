package com.nspace.group.module.infra.dal.dataobject.vendor.offlinelog;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version :VendorOfflineLogProcessRecordDO.java, v0.1 2025年01月07日 18:23 zhangxin Exp
 */
@TableName(value = "vendor_offline_log_process_record")
@KeySequence("vendor_offline_log_process_record_id_seq")  // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
public class VendorOfflineLogProcessRecordDO {
    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 多租户编号
     */
    private Long tenantId;

    /**
     * 域名
     */
    private String domain;

    /**
     * 三方平台code
     */
    private String platform;

    /**
     * 开始时间点
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 状态 0、正常；1、失败；
     */
    private Integer status;

    /**
     * 是否删除
     */
    private Boolean deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
}
