package com.nspace.group.module.infra.client;

import java.util.List;
import java.util.Map;

/**
 * API请求响应
 *
 * @param <T> 响应体反序列化类型
 */
public class ApiResponse<T> {
    final private int statusCode;
    final private Map<String, List<String>> headers;
    final private T data;

    /**
     * @param statusCode Http响应状态码
     * @param headers Http响应头
     */
    public ApiResponse(int statusCode, Map<String, List<String>> headers) {
        this(statusCode, headers, null);
    }

    /**
     * @param statusCode Http响应状态码
     * @param headers Http响应头
     * @param data 响应体返序列化后的对象
     */
    public ApiResponse(int statusCode, Map<String, List<String>> headers, T data) {
        this.statusCode = statusCode;
        this.headers = headers;
        this.data = data;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public Map<String, List<String>> getHeaders() {
        return headers;
    }

    public T getData() {
        return data;
    }
}
