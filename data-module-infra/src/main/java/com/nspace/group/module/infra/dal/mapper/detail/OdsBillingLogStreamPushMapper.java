package com.nspace.group.module.infra.dal.mapper.detail;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.infra.service.detail.dto.LiveCdnLogReqDTO;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 原始ods数据记录-推流计费日志表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-18 16:46:39
 */
@Mapper
@DS("nspace_analysis")
public interface OdsBillingLogStreamPushMapper extends BaseMapperX<OdsBillingLogStreamPushDO> {
    List<OdsBillingLogStreamPushDO> getLogDetails(StreamDetailReqDTO reqDTO);

    default List<OdsBillingLogStreamPushDO> getPushLogList(LiveCdnLogReqDTO reqDTO) {
        String domain = reqDTO.getDomain();
        LocalDateTime logEndTime = reqDTO.getLogEndTime();
        Integer logLimit = reqDTO.getLogLimit();
        LambdaQueryWrapperX<OdsBillingLogStreamPushDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(OdsBillingLogStreamPushDO::getDomain, domain)
                .eq(OdsBillingLogStreamPushDO::getInternal, 0)
                .gt(OdsBillingLogStreamPushDO::getLogTime, logEndTime)
                .le(OdsBillingLogStreamPushDO::getLogTime, reqDTO.getTimeUpperLimit())
                .orderByAsc(OdsBillingLogStreamPushDO::getLogTime);
        Page<OdsBillingLogStreamPushDO> page = new Page<>(1, logLimit);
        return selectPage(page.setSearchCount(false), queryWrapper).getRecords();
    }

    default List<OdsBillingLogStreamPushDO> getDelayedPushLogList(List<Long> logIds) {
        LambdaQueryWrapperX<OdsBillingLogStreamPushDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(OdsBillingLogStreamPushDO::getId, logIds);
        return selectList(queryWrapper);
    }
}