package com.nspace.group.module.infra.service.detail;

import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPullDO;
import com.nspace.group.module.infra.dal.mapper.detail.OdsBillingLogStreamPullMapper;
import com.nspace.group.module.infra.service.detail.dto.LiveCdnLogReqDTO;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 原始ods数据记录-拉流计费日志表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-18 16:42:12
 */
@Service
public class OdsBillingLogStreamPullServiceImpl implements OdsBillingLogStreamPullService {

    @Resource
    OdsBillingLogStreamPullMapper pullDetailMapper;

    @Override
    public List<OdsBillingLogStreamPullDO> getPullDetailList(StreamDetailReqDTO reqDTO) {
        return pullDetailMapper.getLogDetails(reqDTO);
    }

    @Override
    public List<OdsBillingLogStreamPullDO> getPullLogList(LiveCdnLogReqDTO reqDTO) {
        return pullDetailMapper.getPullLogList(reqDTO);
    }

    @Override
    public List<OdsBillingLogStreamPullDO> getDelayedPullLogList(List<Long> logIds) {
        return pullDetailMapper.getDelayedPullLogList(logIds);
    }
}

