package com.nspace.group.module.infra.design.context;

import java.time.ZoneOffset;

/**
 * 数据上下文
 *
 * <AUTHOR>
 */
public abstract class DataContext {


    private final ZoneOffset defaultZoneOffset;


    protected DataContext(ZoneOffset defaultZoneOffset) {
        this.defaultZoneOffset = defaultZoneOffset;
    }

    public ZoneOffset getDefaultZoneOffset() {
        return defaultZoneOffset;
    }

}
