package com.nspace.group.module.infra.client;

import java.util.List;
import java.util.Map;

/**
 * 异步调用 API 回调.
 *
 * @param <T> 响应返回类型
 */
public interface ApiCallback<T> {
    /**
     * API请求失败回调
     *
     * @param e 导致请求失败的报错
     * @param statusCode 响应状态码（没有则默认为0）
     * @param responseHeaders 响应头
     */
    void onFailure(ApiException e, int statusCode, Map<String, List<String>> responseHeaders);

    /**
     * API请求成功回调
     *
     * @param result 响应体反序列化后的对象
     * @param statusCode 响应状态码
     * @param responseHeaders 响应头
     */
    void onSuccess(T result, int statusCode, Map<String, List<String>> responseHeaders);

    /**
     * API上传过程回调
     *
     * @param bytesWritten 已上传字节数
     * @param contentLength 请求体content length
     * @param done write end
     */
    void onUploadProgress(long bytesWritten, long contentLength, boolean done);

    /**
     * API下载过程回调
     *
     * @param bytesRead 已读取字节数
     * @param contentLength 响应体content length
     * @param done 读取结束
     */
    void onDownloadProgress(long bytesRead, long contentLength, boolean done);
}
