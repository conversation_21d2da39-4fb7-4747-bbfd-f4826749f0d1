package com.nspace.group.module.infra.service.vendor.offlinelog;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nspace.group.module.infra.convert.vendor.offlinelog.VendorOfflineLogInfoConvert;
import com.nspace.group.module.infra.dal.dataobject.vendor.offlinelog.VendorOfflineLogInfoDO;
import com.nspace.group.module.infra.dal.mapper.vendor.offlinelog.VendorOfflineLogInfoMapper;
import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 第三方离线日志文件信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class VendorOfflineLogInfoServiceImpl implements VendorOfflineLogInfoService {

    @Autowired
    VendorOfflineLogInfoMapper offlineLogInfoMapper;


    @Override
    public List<VendorOfflineLogInfoDTO> getLogInfosByUrls(Long tenantId, String domain, String platform, List<String> urlsForDomain) {
        List<VendorOfflineLogInfoDO> dos = CollectionUtil.isEmpty(urlsForDomain) ? Collections.emptyList()
                : offlineLogInfoMapper.selectList(new LambdaQueryWrapper<VendorOfflineLogInfoDO>()
                .select(VendorOfflineLogInfoDO::getId, VendorOfflineLogInfoDO::getFileUrl, VendorOfflineLogInfoDO::getStatus)
                .eq(VendorOfflineLogInfoDO::getTenantId, tenantId).eq(VendorOfflineLogInfoDO::getDomain, domain)
                .eq(VendorOfflineLogInfoDO::getPlatform, platform).eq(VendorOfflineLogInfoDO::getDeleted, 0)
                .in(VendorOfflineLogInfoDO::getFileUrl, urlsForDomain));
        return VendorOfflineLogInfoConvert.INSTANCE.getOfflineLogInfoDTOList(dos);
    }

    @Override
    public void saveLogInfos(List<VendorOfflineLogInfoDTO> logInfos) {
        Map<Boolean, List<VendorOfflineLogInfoDO>> insertUpdateLogInfoDOListMap = logInfos.stream()
                .map(VendorOfflineLogInfoConvert.INSTANCE::getOfflineLogInfoDO)
                .collect(Collectors.partitioningBy(logInfo -> Objects.isNull(logInfo.getId())));
        List<VendorOfflineLogInfoDO> insertLogInfoDOS = insertUpdateLogInfoDOListMap.get(Boolean.TRUE);
        List<VendorOfflineLogInfoDO> updateLogInfoDOS = insertUpdateLogInfoDOListMap.get(Boolean.FALSE);
        if (!insertLogInfoDOS.isEmpty()) {
            offlineLogInfoMapper.insertBatch(insertLogInfoDOS);
        }
        if (!updateLogInfoDOS.isEmpty()) {
            offlineLogInfoMapper.updateBatch(updateLogInfoDOS, 1000);
        }
        log.info("saveLogInfos,insert_count={},update_count={}", insertLogInfoDOS.size(), updateLogInfoDOS.size());
    }

    @Override
    public void saveLogInfo(VendorOfflineLogInfoDTO logInfo) {
        VendorOfflineLogInfoDO offlineLogInfoDO = VendorOfflineLogInfoConvert.INSTANCE.getOfflineLogInfoDO(logInfo);
        offlineLogInfoMapper.insertOrUpdate(offlineLogInfoDO);
    }
}
