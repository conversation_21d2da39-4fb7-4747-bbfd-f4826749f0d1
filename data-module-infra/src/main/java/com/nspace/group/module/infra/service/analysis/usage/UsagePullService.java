package com.nspace.group.module.infra.service.analysis.usage;

import com.nspace.group.module.infra.dal.dataobject.analysis.usage.UsagePullBandwidhTrafficDO;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

/**
 * 计费用量-拉流相关数据
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：10:33
 */
public interface UsagePullService {
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    void insertBatchPullStream(Collection<UsagePullBandwidhTrafficDO> pullDOS);
}
