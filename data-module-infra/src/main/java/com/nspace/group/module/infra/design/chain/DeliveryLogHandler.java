package com.nspace.group.module.infra.design.chain;

import com.nspace.group.module.infra.design.context.LogContext;
import com.nspace.group.module.infra.design.strategy.LogDeliveryStrategy;

public class DeliveryLogHandler extends AbstractLogHandler {

    private final LogDeliveryStrategy strategy;

    public DeliveryLogHandler(LogDeliveryStrategy strategy) {
        this.strategy = strategy;
    }

    @Override
    public boolean handle(LogContext logContext) {
        strategy.deliver(logContext);
        return doNext(logContext);
    }
}