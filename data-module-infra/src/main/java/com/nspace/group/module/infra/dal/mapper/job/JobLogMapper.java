package com.nspace.group.module.infra.dal.mapper.job;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.infra.dal.dataobject.job.JobLogDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;

/**
 * 获取任务日志信息
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：16:59
 */
@DS("nspace_data_works")
@Mapper
public interface JobLogMapper extends BaseMapperX<JobLogDO> {
    /**
     * 物理删除指定时间之前的日志
     *
     * @param createTime 最大时间
     * @param limit 删除条数，防止一次删除太多
     * @return 删除条数
     */
//    @Delete("DELETE FROM infra_job_log WHERE create_time < #{createTime} LIMIT #{limit}")
    @Delete("delete from infra_job_log where id in (select id from infra_job_log where create_time < #{createTime} LIMIT #{limit});")
    Integer deleteByCreateTimeLt(@Param("createTime") LocalDateTime createTime, @Param("limit") Integer limit);

}
