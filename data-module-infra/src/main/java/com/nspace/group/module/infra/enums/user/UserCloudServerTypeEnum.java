package com.nspace.group.module.infra.enums.user;

/**
 * 用户开通云服务标识
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/12/16
 * @time：17:48
 */
public enum UserCloudServerTypeEnum {

    OFFLINE_LOG("offline_log", "离线日志", 1),
    LOG_DELIVERY("log_delivery", "日志投递", 1),
    ;

    private final String code;
    private final String desc;
    /**
     * 状态：0-关闭，1-开启
     */
    private final Integer status;

    UserCloudServerTypeEnum(String code, String desc, Integer status) {
        this.code = code;
        this.desc = desc;
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public Integer getStatus() {
        return status;
    }

    /**
     * 根据 code 获取枚举对象
     */
    public static UserCloudServerTypeEnum fromCode(String code) {
        for (UserCloudServerTypeEnum type : UserCloudServerTypeEnum.values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据code 获取status值
     */
    public static Integer getStatusByCode(String code) {
        for (UserCloudServerTypeEnum type : UserCloudServerTypeEnum.values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type.getStatus();
            }
        }
        return 1;
    }
}