package com.nspace.group.module.infra.dal.mapper.job;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.nspace.group.framework.common.pojo.PageResult;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.infra.dal.dataobject.job.JobDO;
import com.nspace.group.module.infra.service.job.dto.JobPageReqDTO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 获取任务日志信息
 *
 * @author：yang<PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2024/10/31
 * @time：17:00
 */
@DS("nspace_data_works")
@Mapper
public interface JobMapper extends BaseMapperX<JobDO> {

    default JobDO selectByHandlerName(String handlerName) {
        return selectOne(JobDO::getHandlerName, handlerName);
    }

    default PageResult<JobDO> selectPage(JobPageReqDTO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<JobDO>()
                .likeIfPresent(JobDO::getName, reqVO.getName())
                .eqIfPresent(JobDO::getStatus, reqVO.getStatus())
                .likeIfPresent(JobDO::getHandlerName, reqVO.getHandlerName())
        );
    }

}
