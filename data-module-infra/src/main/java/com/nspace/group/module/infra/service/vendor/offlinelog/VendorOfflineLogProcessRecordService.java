package com.nspace.group.module.infra.service.vendor.offlinelog;


import com.nspace.group.module.infra.service.vendor.offlinelog.dto.VendorOfflineLogProcessRecordDTO;

import java.util.List;

/**
 * 第三方离线日志处理 Service 接口
 *
 * <AUTHOR>
 */
public interface VendorOfflineLogProcessRecordService {

    /**
     * 生成租户域名对应的本次运行记录
     *
     * @param tenantId       租户ID
     * @param domain         域名
     * @param platform       三方平台code
     * @param interval       日志时间间隔
     * @param offset         开始时间偏移量
     * @param startTimestamp 开始时间戳
     * @return VendorOfflineLogProcessRecordDTO
     */
    VendorOfflineLogProcessRecordDTO getNextProcessRecord(Long tenantId, String domain, String platform, Integer interval, Integer offset, Long startTimestamp);

    /**
     * 获取之前处理过的记录
     *
     * @param tenantId 租户ID
     * @param domain   域名
     * @param platform 三方平台code
     * @return List < VendorOfflineLogProcessRecordDTO>
     */
    List<VendorOfflineLogProcessRecordDTO> getFailedProcessRecords(Long tenantId, String domain, String platform);

    /**
     * 批量保存日志文件处理记录
     *
     * @param processRecords 日志文件处理DTO列表
     */
    void saveLogInfos(List<VendorOfflineLogProcessRecordDTO> processRecords);

    /**
     * 保存日志文件处理记录
     *
     * @param processRecord 日志文件处理DTO
     */
    void saveProcessRecord(VendorOfflineLogProcessRecordDTO processRecord);
}
