package com.nspace.group.module.infra.service.cdn;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.nspace.group.module.infra.convert.cdn.UsageGeneralCdnBandwidthTrafficConvert;
import com.nspace.group.module.infra.dal.dataobject.cdn.UsageGeneralCdnBandwidthTrafficDO;
import com.nspace.group.module.infra.dal.handler.StreamLoadHandler;
import com.nspace.group.module.infra.dal.mapper.cdn.UsageGeneralCdnBandwidthTrafficMapper;
import com.nspace.group.module.infra.service.cdn.dto.UsageGeneralCdnBandwidthTrafficDTO;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 计费用量-通用cdn流量和带宽统计服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-13 17:04:47
 */
@Service
@Slf4j
@DS("nspace_analysis")
public class UsageGeneralCdnBandwidthTrafficServiceImpl implements UsageGeneralCdnBandwidthTrafficService {

    private final ZoneOffset defaultZoneOffset = ZoneOffset.ofHours(8);

    private final JsonMapper jsonMapper = JsonMapper.builder()
            .addModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .propertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .build();

    @Resource(name = "nspaceAnalysisStreamLoader")
    private StreamLoadHandler streamLoadHandler;

    @Resource
    private UsageGeneralCdnBandwidthTrafficMapper cdnBandwidthTrafficMapper;


    @Override
    public void saveTrafficData(List<UsageGeneralCdnBandwidthTrafficDTO> dataList) {
        List<UsageGeneralCdnBandwidthTrafficDO> apiDOList = dataList.stream()
                .map(UsageGeneralCdnBandwidthTrafficConvert.INSTANCE::toBandwidthTrafficDO)
                .collect(Collectors.toList());
        Map<String, UsageGeneralCdnBandwidthTrafficDO> aggKeyApiDataMap = generateAggKeyDataMap(apiDOList);

        List<UsageGeneralCdnBandwidthTrafficDO> dbDOList = getDbBandwidthTrafficData(dataList);
        Map<String, UsageGeneralCdnBandwidthTrafficDO> aggKeyDbDataMap = generateAggKeyDataMap(dbDOList);

        List<UsageGeneralCdnBandwidthTrafficDO> saveDOList = new ArrayList<>();
        aggKeyApiDataMap.forEach((aggKey, apiData) -> {
            if (!aggKeyDbDataMap.containsKey(aggKey)) {
                saveDOList.add(apiData);
            }
        });
        aggKeyDbDataMap.forEach((aggKey, dbData) -> {
            if (aggKeyApiDataMap.containsKey(aggKey)) {
                UsageGeneralCdnBandwidthTrafficDO apiData = aggKeyApiDataMap.get(aggKey);
                // traffic = -(traffic(db) - traffic)
                // traffic(db) = traffic(db) + traffic
                dbData.setTraffic(-(dbData.getTraffic() - apiData.getTraffic()));
                dbData.setBandwidth(new BigDecimal("0.0"));
                dbData.setCurTimestamp(null);
                saveDOList.add(dbData);
            }
        });
        streamLoadData(saveDOList);
    }


    @Override
    public void saveBandwidthData(List<UsageGeneralCdnBandwidthTrafficDTO> dataList) {
        List<UsageGeneralCdnBandwidthTrafficDO> apiDOList = dataList.stream()
                .map(UsageGeneralCdnBandwidthTrafficConvert.INSTANCE::toBandwidthTrafficDO)
                .collect(Collectors.toList());
        Map<String, UsageGeneralCdnBandwidthTrafficDO> aggKeyApiDataMap = generateAggKeyDataMap(apiDOList);

        List<UsageGeneralCdnBandwidthTrafficDO> dbDOList = getDbBandwidthTrafficData(dataList);
        Map<String, UsageGeneralCdnBandwidthTrafficDO> aggKeyDbDataMap = generateAggKeyDataMap(dbDOList);

        List<UsageGeneralCdnBandwidthTrafficDO> saveDOList = new ArrayList<>();
        aggKeyApiDataMap.forEach((aggKey, apiData) -> {
            if (!aggKeyDbDataMap.containsKey(aggKey)) {
                saveDOList.add(apiData);
            }
        });
        aggKeyDbDataMap.forEach((aggKey, dbData) -> {
            if (aggKeyApiDataMap.containsKey(aggKey)) {
                UsageGeneralCdnBandwidthTrafficDO apiData = aggKeyApiDataMap.get(aggKey);
                // bandwidth = -(bandwidth(db) - bandwidth)
                // bandwidth(db) = bandwidth(db) + bandwidth
                dbData.setBandwidth(dbData.getBandwidth().subtract(apiData.getBandwidth()).negate());
                dbData.setTraffic(0L);
                dbData.setCurTimestamp(null);
                saveDOList.add(dbData);
            }
        });
        streamLoadData(saveDOList);
    }

    private List<UsageGeneralCdnBandwidthTrafficDO> getDbBandwidthTrafficData(List<UsageGeneralCdnBandwidthTrafficDTO> dataList) {
        LambdaQueryWrapper<UsageGeneralCdnBandwidthTrafficDO> queryWrapper = new LambdaQueryWrapper<>();
        Set<LocalDateTime> windowStarts = new HashSet<>();
        Set<Long> tenantIds = new HashSet<>();
        Set<String> regions = new HashSet<>();
        Set<String> domains = new HashSet<>();
        Set<Integer> dataPlatforms = new HashSet<>();
        dataList.forEach(data -> {
            windowStarts.add(data.getWindowStart());
            tenantIds.add(data.getTenantId());
            regions.add(data.getRegion());
            domains.add(data.getDomain());
            dataPlatforms.add(data.getDataPlatform());
        });
        queryWrapper.in(windowStarts.size() > 1, UsageGeneralCdnBandwidthTrafficDO::getWindowStart, windowStarts)
                .eq(windowStarts.size() == 1, UsageGeneralCdnBandwidthTrafficDO::getWindowStart, new ArrayList<>(windowStarts).get(0))
                .in(tenantIds.size() > 1, UsageGeneralCdnBandwidthTrafficDO::getTenantId, tenantIds)
                .eq(tenantIds.size() == 1, UsageGeneralCdnBandwidthTrafficDO::getTenantId, new ArrayList<>(tenantIds).get(0))
                .in(regions.size() > 1, UsageGeneralCdnBandwidthTrafficDO::getRegion, regions)
                .eq(regions.size() == 1, UsageGeneralCdnBandwidthTrafficDO::getRegion, new ArrayList<>(regions).get(0))
                .in(domains.size() > 1, UsageGeneralCdnBandwidthTrafficDO::getDomain, domains)
                .in(domains.size() == 1, UsageGeneralCdnBandwidthTrafficDO::getDomain, new ArrayList<>(domains).get(0))
                .in(dataPlatforms.size() > 1, UsageGeneralCdnBandwidthTrafficDO::getDataPlatform, dataPlatforms)
                .eq(dataPlatforms.size() == 1, UsageGeneralCdnBandwidthTrafficDO::getDataPlatform, new ArrayList<>(dataPlatforms).get(0));
        return cdnBandwidthTrafficMapper.selectList(queryWrapper);
    }

    private Map<String, UsageGeneralCdnBandwidthTrafficDO> generateAggKeyDataMap(List<UsageGeneralCdnBandwidthTrafficDO> dataList) {
        return dataList.stream()
                .collect(Collectors.toMap(data -> {
                    long windowStart = data.getWindowStart().toEpochSecond(defaultZoneOffset);
                    Long tenantId = data.getTenantId();
                    String region = data.getRegion();
                    String domain = data.getDomain();
                    Integer dataPlatform = data.getDataPlatform();
                    return StringPool.EMPTY + windowStart + tenantId + region + domain + dataPlatform;
                }, data -> data));
    }

    private void streamLoadData(Collection<UsageGeneralCdnBandwidthTrafficDO> doListToSave) {
        try {
            Map<String, String> loadResultMap = streamLoadHandler.sendData("tb_usage_general_cdn_bandwidth_traffic", jsonMapper.writeValueAsString(doListToSave));
            if ("Success".equals(loadResultMap.get("Status"))) {
                log.info("stream_load_success,NumberLoadedRows={},LoadTimeMs={}", loadResultMap.get("NumberLoadedRows"), loadResultMap.get("LoadTimeMs"));
            } else {
                log.error("stream_load_failed,Message={},ErrorURL={}", loadResultMap.get("Message"), loadResultMap.get("ErrorURL"));
                throw new RuntimeException("stream_load_failed");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

