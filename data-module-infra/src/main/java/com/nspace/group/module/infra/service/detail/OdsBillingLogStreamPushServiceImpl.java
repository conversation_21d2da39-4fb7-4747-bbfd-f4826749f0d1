package com.nspace.group.module.infra.service.detail;

import com.nspace.group.module.infra.dal.dataobject.detail.OdsBillingLogStreamPushDO;
import com.nspace.group.module.infra.dal.mapper.detail.OdsBillingLogStreamPushMapper;
import com.nspace.group.module.infra.service.detail.dto.LiveCdnLogReqDTO;
import com.nspace.group.module.infra.service.detail.dto.StreamDetailReqDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据明细-推流计费数据明细表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-18 16:46:39
 */
@Service
public class OdsBillingLogStreamPushServiceImpl implements OdsBillingLogStreamPushService {

    @Resource
    OdsBillingLogStreamPushMapper pushDetailMapper;

    @Override
    public List<OdsBillingLogStreamPushDO> getPushDetailList(StreamDetailReqDTO reqDTO) {
        return pushDetailMapper.getLogDetails(reqDTO);
    }

    @Override
    public List<OdsBillingLogStreamPushDO> getPushLogList(LiveCdnLogReqDTO reqDTO) {
        return pushDetailMapper.getPushLogList(reqDTO);
    }

    @Override
    public List<OdsBillingLogStreamPushDO> getDelayedPushLogList(List<Long> logIds) {
        return pushDetailMapper.getDelayedPushLogList(logIds);
    }
}
