package com.nspace.group.module.infra.service.detail.dto;

import java.time.LocalDateTime;

/**
 * 直播cdn请求日志查询参数DTO
 *
 * <AUTHOR>
 */
public class LiveCdnLogReqDTO {

    // 域名
    private final String domain;

    // 单次投递数量限制
    private final Integer logLimit;

    // 日志查询结束时间
    private final LocalDateTime logEndTime;

    // 日志延迟偏移量（秒）
    private final LocalDateTime timeUpperLimit;

    public LiveCdnLogReqDTO(String domain,
                            Integer logLimit, LocalDateTime logEndTime, LocalDateTime timeUpperLimit) {
        this.domain = domain;
        this.logLimit = logLimit;
        this.logEndTime = logEndTime;
        this.timeUpperLimit = timeUpperLimit;
    }

    public String getDomain() {
        return domain;
    }

    public Integer getLogLimit() {
        return logLimit;
    }

    public LocalDateTime getLogEndTime() {
        return logEndTime;
    }

    public LocalDateTime getTimeUpperLimit() {
        return timeUpperLimit;
    }
}
