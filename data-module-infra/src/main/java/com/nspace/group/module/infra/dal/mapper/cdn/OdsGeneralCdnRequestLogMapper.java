package com.nspace.group.module.infra.dal.mapper.cdn;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.nspace.group.module.infra.dal.dataobject.cdn.OdsGeneralCdnRequestLogDO;
import com.nspace.group.module.infra.service.cdn.dto.GeneralCdnRequestLogReqDTO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 原始ods数据记录-通用cdn请求日志数据库操作Mapper
 *
 * <AUTHOR>
 * @since 2025-03-19 09:38:23
 */
@Mapper
@DS("nspace_analysis")
public interface OdsGeneralCdnRequestLogMapper extends BaseMapperX<OdsGeneralCdnRequestLogDO> {

    default List<OdsGeneralCdnRequestLogDO> getRequestLogList(GeneralCdnRequestLogReqDTO reqDTO) {
        String domain = reqDTO.getDomain();
        LocalDateTime logEndTime = reqDTO.getLogEndTime();
        Integer logLimit = reqDTO.getLogLimit();
        LambdaQueryWrapperX<OdsGeneralCdnRequestLogDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(OdsGeneralCdnRequestLogDO::getInternal, 0)
                .eq(OdsGeneralCdnRequestLogDO::getDomain, domain)
                .gt(OdsGeneralCdnRequestLogDO::getLogTime, logEndTime)
                .le(OdsGeneralCdnRequestLogDO::getLogTime, reqDTO.getTimeUpperLimit())
                .orderByAsc(OdsGeneralCdnRequestLogDO::getLogTime);
        Page<OdsGeneralCdnRequestLogDO> page = new Page<>(1, logLimit);
        return selectPage(page.setSearchCount(false), queryWrapper).getRecords();
    }

    default List<OdsGeneralCdnRequestLogDO> getDelayedRequestLogList(List<Long> logIds) {
        LambdaQueryWrapperX<OdsGeneralCdnRequestLogDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(OdsGeneralCdnRequestLogDO::getId, logIds);
        return selectList(queryWrapper);
    }
}




