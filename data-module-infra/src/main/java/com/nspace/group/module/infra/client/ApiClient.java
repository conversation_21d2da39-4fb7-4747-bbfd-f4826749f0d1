package com.nspace.group.module.infra.client;

import com.nspace.group.module.infra.client.auth.Authentication;
import okhttp3.*;
import okhttp3.internal.http.HttpMethod;
import okhttp3.internal.tls.OkHostnameVerifier;
import okhttp3.logging.HttpLoggingInterceptor;
import okio.BufferedSink;
import okio.Okio;

import javax.net.ssl.*;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ApiClient {

    private final JSON json;
    private final String basePath;
    private final Map<String, String> defaultHeaderMap = new HashMap<>();
    private final Map<String, String> defaultCookieMap = new HashMap<>();
    private Authentication authentication;

    private boolean debugging = false;
    private String tempFolderPath = null;

    private InputStream sslCaCert;
    private boolean verifyingSsl;
    private KeyManager[] keyManagers;

    private OkHttpClient httpClient;

    private HttpLoggingInterceptor loggingInterceptor;

    private static final OkHttpClient DEFAULT_HTTP_CLIENT = new OkHttpClient.Builder()
            .addNetworkInterceptor(getProgressInterceptor()).build();

    //不带鉴权配置的构造器
    public ApiClient(String apiBasePath) {
        init();
        httpClient = DEFAULT_HTTP_CLIENT;

        json = new JSON();
        basePath = apiBasePath;
    }

    /*
     * 带鉴权配置的构造器
     */
    public ApiClient(String apiBasePath, Authentication auth) {
        init();

        httpClient = DEFAULT_HTTP_CLIENT;
        json = new JSON();


        // 配置 鉴权
        authentication = auth;
        basePath = apiBasePath;
    }

    private void init() {
        verifyingSsl = true;
        // 设置默认 User-Agent.
        setUserAgent("API-Client/1.0.0/java");

    }

    /**
     * 获取basePath
     *
     * @return basePath
     */
    public String getBasePath() {
        return basePath;
    }

    /**
     * 如果配置了isVerifySsl则返回true
     *
     * @return 如果配置了isVerifySsl则返回true
     */
    public boolean isVerifyingSsl() {
        return verifyingSsl;
    }

    /**
     * 配置发出https请求时是否校验certificate和hostname
     * 默认为true
     *
     * @param verifyingSsl 设置为True以校验 TLS/SSL 连接
     * @return ApiClient
     */
    public ApiClient setVerifyingSsl(boolean verifyingSsl) {
        this.verifyingSsl = verifyingSsl;
        applySslSettings();
        return this;
    }

    /**
     * 获取 SSL CA cert.
     *
     * @return Input stream to the SSL CA cert
     */
    public InputStream getSslCaCert() {
        return sslCaCert;
    }

    /**
     * 配置发出https请求时的CA certificate
     * 值为 null 则使用默认值
     *
     * @param sslCaCert input stream for SSL CA cert
     * @return ApiClient
     */
    public ApiClient setSslCaCert(InputStream sslCaCert) {
        this.sslCaCert = sslCaCert;
        applySslSettings();
        return this;
    }

    public KeyManager[] getKeyManagers() {
        return keyManagers;
    }

    /**
     * 配置SSL连接认证时使用的 client keys
     * 值为 null 则使用默认值
     *
     * @param managers The KeyManagers to use
     * @return ApiClient
     */
    public ApiClient setKeyManagers(KeyManager[] managers) {
        this.keyManagers = managers;
        applySslSettings();
        return this;
    }

    public Authentication getAuthentication() {
        return authentication;
    }

    /**
     * 设置User-Agent 请求头 (通过加入默认 header map).
     *
     * @param userAgent HTTP request's user agent
     * @return ApiClient
     */
    public ApiClient setUserAgent(String userAgent) {
        addDefaultHeader("User-Agent", userAgent);
        return this;
    }

    /**
     * 添加一个默认请求头
     *
     * @param key   header key
     * @param value header value
     * @return ApiClient
     */
    public ApiClient addDefaultHeader(String key, String value) {
        defaultHeaderMap.put(key, value);
        return this;
    }

    /**
     * 添加一个默认 cookie.
     *
     * @param key   cookie key
     * @param value cookie value
     * @return ApiClient
     */
    public ApiClient addDefaultCookie(String key, String value) {
        defaultCookieMap.put(key, value);
        return this;
    }

    /**
     * 查看是否开启debugging模式
     *
     * @return boolean
     */
    public boolean isDebugging() {
        return debugging;
    }

    /**
     * 启用/暂停 debugging
     *
     * @param debugging To enable (true) or disable (false) debugging
     * @return ApiClient
     */
    public ApiClient setDebugging(boolean debugging) {
        if (debugging != this.debugging) {
            if (debugging) {
                loggingInterceptor = new HttpLoggingInterceptor();
                loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
                httpClient = httpClient.newBuilder().addInterceptor(loggingInterceptor).build();
            } else {
                final OkHttpClient.Builder builder = httpClient.newBuilder();
                builder.interceptors().remove(loggingInterceptor);
                httpClient = builder.build();
                loggingInterceptor = null;
            }
        }
        this.debugging = debugging;
        return this;
    }

    /**
     * 获取从端点下载的文件的临时文件夹的路径，默认值为<code>null</code>，即使用
     * 系统默认的临时文件夹。
     *
     * @return Temporary folder path
     * @see <a href="https://docs.oracle.com/javase/7/docs/api/java/nio/file/Files.html#createTempFile(java.lang.String,%20java.lang.String,%20java.nio.file.attribute.FileAttribute...)">createTempFile</a>
     */
    public String getTempFolderPath() {
        return tempFolderPath;
    }

    /**
     * 设置从端点下载的文件的临时文件夹的路径
     *
     * @param tempFolderPath Temporary folder path
     * @return ApiClient
     */
    public ApiClient setTempFolderPath(String tempFolderPath) {
        this.tempFolderPath = tempFolderPath;
        return this;
    }

    /**
     * 获取连接超时（以毫秒为单位）
     *
     * @return Timeout
     */
    public int getConnectTimeout() {
        return httpClient.connectTimeoutMillis();
    }

    /**
     * 设置连接超时（以毫秒为单位）。
     * 值为 0 表示没有超时，否则值必须介于 1 和
     * {@link Integer#MAX_VALUE}。
     *
     * @param connectionTimeout 连接超时时间（以毫秒为单位）
     * @return API客户端
     */
    public ApiClient setConnectTimeout(int connectionTimeout) {
        httpClient = httpClient.newBuilder().connectTimeout(connectionTimeout, TimeUnit.MILLISECONDS).build();
        return this;
    }

    /**
     * 获取读取超时（以毫秒为单位）。
     *
     * @return 超时时间（以毫秒为单位）
     */
    public int getReadTimeout() {
        return httpClient.readTimeoutMillis();
    }

    /**
     * 设置读取超时（以毫秒为单位）。
     * 值为 0 表示没有超时，否则值必须介于 1 和
     * {@link Integer#MAX_VALUE}。
     *
     * @param readTimeout 读取超时时间（以毫秒为单位）
     * @return API客户端
     */
    public ApiClient setReadTimeout(int readTimeout) {
        httpClient = httpClient.newBuilder().readTimeout(readTimeout, TimeUnit.MILLISECONDS).build();
        return this;
    }

    /**
     * 获取写入超时（以毫秒为单位）。
     *
     * @return 超时时间（以毫秒为单位）
     */
    public int getWriteTimeout() {
        return httpClient.writeTimeoutMillis();
    }

    /**
     * 设置写入超时（以毫秒为单位）。
     * 值为 0 表示没有超时，否则值必须介于 1 和
     * {@link Integer#MAX_VALUE}。
     *
     * @param writeTimeout 连接超时时间（以毫秒为单位）
     * @return API客户端
     */
    public ApiClient setWriteTimeout(int writeTimeout) {
        httpClient = httpClient.newBuilder().writeTimeout(writeTimeout, TimeUnit.MILLISECONDS).build();
        return this;
    }


    /**
     * 将给定的参数对象格式化为字符串。
     *
     * @param param 参数
     * @return 参数的字符串表示
     */
    public String parameterToString(Object param) {
        if (param == null) {
            return "";
        } else if (param instanceof Date || param instanceof OffsetDateTime || param instanceof LocalDate) {
            //Serialize to json string and remove the " enclosing characters
            String jsonStr = json.serialize(param);
            return jsonStr.substring(1, jsonStr.length() - 1);
        } else if (param instanceof Collection) {
            StringBuilder b = new StringBuilder();
            for (Object o : (Collection) param) {
                if (b.length() > 0) {
                    b.append(",");
                }
                b.append(o);
            }
            return b.toString();
        } else {
            return String.valueOf(param);
        }
    }

    /**
     * 将指定的查询参数格式化为包含单个 {@code Pair} 对象的列表。
     * <p>
     * 请注意，{@code value} 不能是集合。
     *
     * @param name 参数的名称。
     * @param value 参数的值。
     * @return 包含单个 {@code Pair} 对象的列表。
     */
    public List<Pair> parameterToPair(String name, Object value) {
        List<Pair> params = new ArrayList<Pair>();

        // preconditions
        if (name == null || name.isEmpty() || value == null || value instanceof Collection) {
            return params;
        }

        params.add(new Pair(name, parameterToString(value)));
        return params;
    }

    /**
     * 将指定的集合查询参数格式化为{@code Pair}对象的列表。
     * <p>
     * 请注意，每个返回的 Pair 对象的值都是百分比编码的。
     *
     * @param collectionFormat 参数的集合格式。
     * @param name 参数的名称。
     * @param value 参数的值。
     * @return {@code Pair} 对象的列表。
     */
    public List<Pair> parameterToPairs(String collectionFormat, String name, Collection value) {
        List<Pair> params = new ArrayList<Pair>();

        // preconditions
        if (name == null || name.isEmpty() || value == null || value.isEmpty()) {
            return params;
        }

        // create the params based on the collection format
        if ("multi".equals(collectionFormat)) {
            for (Object item : value) {
                params.add(new Pair(name, escapeString(parameterToString(item))));
            }
            return params;
        }

        // collectionFormat is assumed to be "csv" by default
        String delimiter = ",";

        // escape all delimiters except commas, which are URI reserved
        // characters
        if ("ssv".equals(collectionFormat)) {
            delimiter = escapeString(" ");
        } else if ("tsv".equals(collectionFormat)) {
            delimiter = escapeString("\t");
        } else if ("pipes".equals(collectionFormat)) {
            delimiter = escapeString("|");
        }

        StringBuilder sb = new StringBuilder();
        for (Object item : value) {
            sb.append(delimiter);
            sb.append(escapeString(parameterToString(item)));
        }

        params.add(new Pair(name, sb.substring(delimiter.length())));

        return params;
    }

    /**
     * 将指定的集合路径参数格式化为字符串值。
     *
     * @param collectionFormat 参数的集合格式。
     * @param value 参数的值。
     * @return 参数的字符串表示
     */
    public String collectionPathParameterToString(String collectionFormat, Collection value) {
        // create the value based on the collection format
        if ("multi".equals(collectionFormat)) {
            // not valid for path params
            return parameterToString(value);
        }

        // collectionFormat is assumed to be "csv" by default
        String delimiter = ",";

        if ("ssv".equals(collectionFormat)) {
            delimiter = " ";
        } else if ("tsv".equals(collectionFormat)) {
            delimiter = "\t";
        } else if ("pipes".equals(collectionFormat)) {
            delimiter = "|";
        }

        StringBuilder sb = new StringBuilder();
        for (Object item : value) {
            sb.append(delimiter);
            sb.append(parameterToString(item));
        }

        return sb.substring(delimiter.length());
    }

    /**
     * 通过删除路径来清理文件名。
     * 例如../../sun.gif 变为 sun.gif
     *
     * @param filename 要清理的文件名
     * @return 清理后的文件名
     */
    public String sanitizeFilename(String filename) {
        return filename.replaceAll(".*[/\\\\]", "");
    }

    /**
     * 检查给定的 MIME 是否是 JSON MIME。
     * JSON MIME 示例：
     * 应用程序/json
     * 应用程序/json；字符集=UTF8
     * 应用程序/JSON
     * 应用程序/vnd.company+json
     * "* / *" 也默认为 JSON
     *
     * @param mime MIME（多用途互联网邮件扩展）
     * @return 如果给定的 MIME 是 JSON，则返回 true，否则返回 false。
     */
    public boolean isJsonMime(String mime) {
        String jsonMime = "(?i)^(application/json|[^;/ \t]+/[^;/ \t]+[+]json)[ \t]*(;.*)?$";
        return mime != null && (mime.matches(jsonMime) || mime.equals("*/*"));
    }

    /**
     * 从给定的接受数组中选择接受标头的值：
     * 如果 JSON 存在于给定数组中，则使用它；
     * 否则使用所有它们（连接成一个字符串）
     *
     * @param accepts accepts 数组
     * @return 要使用的 Accept 标头。如果给定的数组为空，
     * 将返回 null（不显式设置 Accept 标头）。
     */
    public String selectHeaderAccept(String[] accepts) {
        if (accepts.length == 0) {
            return null;
        }
        for (String accept : accepts) {
            if (isJsonMime(accept)) {
                return accept;
            }
        }
        return String.join(",",  accepts);
    }

    /**
     * 从给定数组中选择 Content-Type 标头的值：
     * 如果给定数组中存在 JSON，则使用它；
     * 否则使用数组的第一个。
     *
     * @param contentTypes 要从中选择的 Content-Type 数组
     * @return 要使用的 Content-Type 标头。如果给定的数组为空，
     * 或匹配“any”，将使用 JSON。
     */
    public String selectHeaderContentType(String[] contentTypes) {
        if (contentTypes.length == 0 || contentTypes[0].equals("*/*")) {
            return "application/json";
        }
        for (String contentType : contentTypes) {
            if (isJsonMime(contentType)) {
                return contentType;
            }
        }
        return contentTypes[0];
    }

    /**
     * 转义给定字符串以用作 URL 查询值。
     *
     * @param str 要转义的字符串
     * @return 转义字符串
     */
    public String escapeString(String str) {
        return URLEncoder.encode(str, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
    }

    /**
     * 根据返回类型和将响应体反序列化为Java对象
     * Content-Type 响应头。
     *
     * @param <T> 类型
     * @param response HTTP响应
     * @param returnType Java对象的类型
     * @return 反序列化的Java对象
     * @throws ApiException 如果反序列化响应体失败，即无法读取响应体
     * 或不支持响应的 Content-Type。
     */
    @SuppressWarnings("unchecked")
    public <T> T deserialize(Response response, Type returnType) throws ApiException {
        if (response == null || returnType == null) {
            return null;
        }
        if ("byte[]".equals(returnType.getTypeName())) {
            // Handle binary response (byte array).
            try {
                return (T) response.body().bytes();
            } catch (IOException e) {
                throw new ApiException(e);
            }
        } else if (returnType.equals(File.class)) {
            // Handle file downloading.
            return (T) downloadFileFromResponse(response);
        }

        String respBody;
        try {
            if (response.body() != null) {
                respBody = response.body().string();
            }
            else
                respBody = null;
        } catch (IOException e) {
            throw new ApiException(e);
        }

        if (respBody == null || "".equals(respBody)) {
            return null;
        }

        String contentType = response.headers().get("Content-Type");
        if (contentType == null) {
            // ensuring a default content type
            contentType = "application/json";
        }
        if (isJsonMime(contentType)) {
            return json.deserialize(respBody, returnType);
        } else if (returnType.equals(String.class)) {
            // Expecting string, return the raw response body.
            return (T) respBody;
        } else {
            throw new ApiException(
                    "Content type \"" + contentType + "\" is not supported for type: " + returnType,
                    response.code(),
                    response.headers().toMultimap(),
                    respBody);
        }
    }

    /**
     * 根据给定的Java对象序列化到请求体中
     * 类和请求的Content-Type。
     *
     * @param obj Java对象
     * @param contentType 请求的Content-Type
     * @return 序列化的请求体
     * @throws ApiException 如果未能序列化给定对象
     */
    public RequestBody serialize(Object obj, String contentType) throws ApiException {
        if (obj instanceof byte[]) {
            // Binary (byte array) body parameter support.
            return RequestBody.Companion.create((byte[]) obj, MediaType.Companion.parse(contentType));
        } else if (obj instanceof File) {
            // File body parameter support.
            return RequestBody.Companion.create((File) obj, MediaType.Companion.parse(contentType));
        } else if (isJsonMime(contentType)) {
            String content;
            if (obj != null) {
                content = json.serialize(obj);
            } else {
                content = null;
            }
            return RequestBody.Companion.create(content, MediaType.Companion.parse(contentType));
        } else {
            throw new ApiException("Content type \"" + contentType + "\" is not supported");
        }
    }

    /**
     * 从给定的响应下载文件。
     *
     * @param response Response 对象的实例
     * @return 下载的文件
     * @throws ApiException 如果无法从响应中读取文件内容并写入磁盘
     */
    public File downloadFileFromResponse(Response response) throws ApiException {
        try {
            File file = prepareDownloadFile(response);
            BufferedSink sink = Okio.buffer(Okio.sink(file));
            sink.writeAll(response.body().source());
            sink.close();
            return file;
        } catch (IOException e) {
            throw new ApiException(e);
        }
    }

    /**
     * 准备文件供下载
     *
     * @param response Response 对象的实例
     * @return 准备好的下载文件
     * @throws IOException 如果无法准备文件下载
     */
    public File prepareDownloadFile(Response response) throws IOException {
        String filename = null;
        String contentDisposition = response.header("Content-Disposition");
        if (contentDisposition != null && !"".equals(contentDisposition)) {
            // Get filename from the Content-Disposition header.
            Pattern pattern = Pattern.compile("filename=['\"]?([^'\"\\s]+)['\"]?");
            Matcher matcher = pattern.matcher(contentDisposition);
            if (matcher.find()) {
                filename = sanitizeFilename(matcher.group(1));
            }
        }

        String prefix = null;
        String suffix = null;
        if (filename == null) {
            prefix = "download-";
            suffix = "";
        } else {
            int pos = filename.lastIndexOf(".");
            if (pos == -1) {
                prefix = filename + "-";
            } else {
                prefix = filename.substring(0, pos) + "-";
                suffix = filename.substring(pos);
            }
            // Files.createTempFile requires the prefix to be at least three characters long
            if (prefix.length() < 3)
                prefix = "download-";
        }

        if (tempFolderPath == null)
            return Files.createTempFile(prefix, suffix).toFile();
        else
            return Files.createTempFile(Paths.get(tempFolderPath), prefix, suffix).toFile();
    }

    /**
     * {@link #execute(Call, Type)}
     *
     * @param <T> 类型
     * @param call Call对象的实例
     * @return ApiResponse<T>;
     * @throws ApiException 如果执行调用失败
     */
    public <T> ApiResponse<T> execute(Call call) throws ApiException {
        return execute(call, null);
    }

    /**
     * 执行HTTP调用并将HTTP响应体反序列化为给定的返回类型。
     *
     * @param returnType 用于反序列化HTTP响应体的返回类型
     * @param <T> returnType对应（相同）的返回类型
     * @param call 调用
     * @return ApiResponse 对象包含响应状态、标头和
     * data，它是从响应主体反序列化的 Java 对象，将为 null
     * 当返回类型为空时。
     * @throws ApiException 如果执行调用失败
     */
    public <T> ApiResponse<T> execute(Call call, Type returnType) throws ApiException {
        try {
            Response response = call.execute();
            T data = handleResponse(response, returnType);
            return new ApiResponse<T>(response.code(), response.headers().toMultimap(), data);
        } catch (IOException e) {
            throw new ApiException(e);
        }
    }

    /**
     * {@link #executeAsync(Call, Type, ApiCallback)}
     *
     * @param <T> 类型
     * @param call Call对象的实例
     * @param callback ApiCallback&lt;T&gt;
     */
    public <T> void executeAsync(Call call, ApiCallback<T> callback) {
        executeAsync(call, null, callback);
    }

    /**
     * 异步执行HTTP 调用。
     *
     * @param <T> 类型
     * @param call API调用完成时执行的回调
     * @param returnType Return type
     * @param callback   ApiCallback
     * @see #execute(Call, Type)
     */
    @SuppressWarnings("unchecked")
    public <T> void executeAsync(Call call, final Type returnType, final ApiCallback<T> callback) {
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                callback.onFailure(new ApiException(e), 0, null);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                T result;
                try {
                    result = (T) handleResponse(response, returnType);
                } catch (ApiException e) {
                    callback.onFailure(e, response.code(), response.headers().toMultimap());
                    return;
                } catch (Exception e) {
                    callback.onFailure(new ApiException(e), response.code(), response.headers().toMultimap());
                    return;
                }
                callback.onSuccess(result, response.code(), response.headers().toMultimap());
            }
        });
    }

    /**
     * 处理给定的响应，响应成功时返回反序列化的对象。
     *
     * @param <T>        Type
     * @param response   Response
     * @param returnType Return type
     * @return Type
     * @throws ApiException 如果响应有不成功的状态代码或无法反序列化响应体
     */
    public <T> T handleResponse(Response response, Type returnType) throws ApiException {
        if (response.isSuccessful()) {
            if (returnType == null || response.code() == 204) {
                // returning null if the returnType is not defined,
                // or the status code is 204 (No Content)
                if (response.body() != null) {
                    try {
                        response.body().close();
                    } catch (Exception e) {
                        throw new ApiException(response.message(), e, response.code(), response.headers().toMultimap());
                    }
                }
                return null;
            } else {
                return deserialize(response, returnType);
            }
        } else {
            String respBody = null;
            if (response.body() != null) {
                try {
                    respBody = response.body().string();
                } catch (IOException e) {
                    throw new ApiException(response.message(), e, response.code(), response.headers().toMultimap());
                }
            }
            throw new ApiException(response.message(), response.code(), response.headers().toMultimap(), respBody);
        }
    }

    /**
     * 使用给定选项构建 HTTP 调用。
     *
     * @param path HTTP URL的子路径
     * @param method 请求方法，“GET”、“HEAD”、“OPTIONS”、“POST”、“PUT”、“PATCH”和“DELETE”之一
     * @param queryParams 查询参数
     * @param collectionQueryParams 集合查询参数
     * @param body 请求体对象
     * @param headerParams 标头参数
     * @param cookieParams cookie参数
     * @param formParams 表单参数
     * @param callback 上传/下载进度回调
     * @return The HTTP call
     * @throws ApiException 如果无法序列化请求体对象
     */
    public Call buildCall(String path, String method, List<Pair> queryParams, List<Pair> collectionQueryParams, Object body, Map<String, String> headerParams, Map<String, String> cookieParams, Map<String, Object> formParams, ApiCallback callback) throws ApiException {
        Request request = buildRequest(path, method, queryParams, collectionQueryParams, body, headerParams, cookieParams, formParams, callback);

        return httpClient.newCall(request);
    }

    /**
     * 使用给定选项构建 HTTP 请求。
     *
     * @param path HTTP URL的子路径
     * @param method 请求方法，“GET”、“HEAD”、“OPTIONS”、“POST”、“PUT”、“PATCH”和“DELETE”之一
     * @param queryParams 查询参数
     * @param collectionQueryParams 集合查询参数
     * @param body 请求体对象
     * @param headerParams 标头参数
     * @param cookieParams cookie参数
     * @param formParams 表单参数
     * @param callback 上传/下载进度回调
     * @return HTTP请求
     * @throws ApiException 如果序列化请求体对象失败
     */
    public Request buildRequest(String path, String method, List<Pair> queryParams, List<Pair> collectionQueryParams, Object body, Map<String, String> headerParams, Map<String, String> cookieParams, Map<String, Object> formParams, ApiCallback callback) throws ApiException {
        final String url = buildUrl(path, queryParams, collectionQueryParams);
        final Request.Builder reqBuilder = new Request.Builder().url(url);
        processHeaderParams(headerParams, reqBuilder);
        processCookieParams(cookieParams, reqBuilder);

        String contentType = headerParams.get("Content-Type");
        // ensuring a default content type
        if (contentType == null) {
            contentType = "application/json";
        }

        RequestBody reqBody;
        if (!HttpMethod.permitsRequestBody(method)) {
            reqBody = null;
        } else if ("application/x-www-form-urlencoded".equals(contentType)) {
            reqBody = buildRequestBodyFormEncoding(formParams);
        } else if ("multipart/form-data".equals(contentType)) {
            reqBody = buildRequestBodyMultipart(formParams);
        } else if (body == null) {
            if ("DELETE".equals(method)) {
                // allow calling DELETE without sending a request body
                reqBody = null;
            } else {
                // use an empty request body (for POST, PUT and PATCH)
                reqBody = RequestBody.create("", MediaType.parse(contentType));
            }
        } else {
            reqBody = serialize(body, contentType);
        }

        // Associate callback with request (if not null) so interceptor can
        // access it when creating ProgressResponseBody
        reqBuilder.tag(callback);

        Request request = null;

        if (callback != null && reqBody != null) {
            ProgressRequestBody progressRequestBody = new ProgressRequestBody(reqBody, callback);
            request = reqBuilder.method(method, progressRequestBody).build();
        } else {
            request = reqBuilder.method(method, reqBody).build();
        }

        return request;
    }

    /**
     * 通过连接基本路径、给定的子路径和查询参数来构建完整的 URL。
     *
     * @param path 子路径
     * @param queryParams 查询参数
     * @param collectionQueryParams 集合查询参数
     * @return 完整的URL
     */
    public String buildUrl(String path, List<Pair> queryParams, List<Pair> collectionQueryParams) {
        final StringBuilder url = new StringBuilder();
        url.append(basePath).append(path);

        if (queryParams != null && !queryParams.isEmpty()) {
            // support (constant) query string in `path`, e.g. "/posts?draft=1"
            String prefix = path.contains("?") ? "&" : "?";
            for (Pair param : queryParams) {
                if (param.getValue() != null) {
                    if (prefix != null) {
                        url.append(prefix);
                        prefix = null;
                    } else {
                        url.append("&");
                    }
                    String value = parameterToString(param.getValue());
                    url.append(escapeString(param.getName())).append("=").append(escapeString(value));
                }
            }
        }

        if (collectionQueryParams != null && !collectionQueryParams.isEmpty()) {
            String prefix = url.toString().contains("?") ? "&" : "?";
            for (Pair param : collectionQueryParams) {
                if (param.getValue() != null) {
                    if (prefix != null) {
                        url.append(prefix);
                        prefix = null;
                    } else {
                        url.append("&");
                    }
                    String value = parameterToString(param.getValue());
                    // collection query parameter value already escaped as part of parameterToPairs
                    url.append(escapeString(param.getName())).append("=").append(value);
                }
            }
        }

        return url.toString();
    }

    /**
     * 将标头参数设置为请求构建器，包括默认标头。
     *
     * @param headerParams Map形式的头部参数
     * @param reqBuilder 请求.Builder
     */
    public void processHeaderParams(Map<String, String> headerParams, Request.Builder reqBuilder) {
        for (Entry<String, String> param : headerParams.entrySet()) {
            reqBuilder.header(param.getKey(), parameterToString(param.getValue()));
        }
        for (Entry<String, String> header : defaultHeaderMap.entrySet()) {
            if (!headerParams.containsKey(header.getKey())) {
                reqBuilder.header(header.getKey(), parameterToString(header.getValue()));
            }
        }
    }

    /**
     * 为请求构建器设置 cookie 参数，包括默认 cookie。
     *
     * @param cookieParams Map形式的Cookie参数
     * @param reqBuilder 请求.Builder
     */
    public void processCookieParams(Map<String, String> cookieParams, Request.Builder reqBuilder) {
        for (Entry<String, String> param : cookieParams.entrySet()) {
            reqBuilder.addHeader("Cookie", String.format("%s=%s", param.getKey(), param.getValue()));
        }
        for (Entry<String, String> param : defaultCookieMap.entrySet()) {
            if (!cookieParams.containsKey(param.getKey())) {
                reqBuilder.addHeader("Cookie", String.format("%s=%s", param.getKey(), param.getValue()));
            }
        }
    }

    /**
     * 使用给定的表单参数构建表单编码请求正文。
     *
     * @param formParams Map形式的表单参数
     * @return 请求体
     */
    public RequestBody buildRequestBodyFormEncoding(Map<String, Object> formParams) {
        FormBody.Builder formBuilder = new FormBody.Builder();
        for (Entry<String, Object> param : formParams.entrySet()) {
            formBuilder.add(param.getKey(), parameterToString(param.getValue()));
        }
        return formBuilder.build();
    }

    /**
     * 使用给定的表单参数构建多部分（文件上传）请求正文，
     * 可以包含文本字段和文件字段。
     *
     * @param formParams Map形式的表单参数
     * @return 请求体
     */
    public RequestBody buildRequestBodyMultipart(Map<String, Object> formParams) {
        MultipartBody.Builder mpBuilder = new MultipartBody.Builder().setType(MultipartBody.FORM);
        for (Entry<String, Object> param : formParams.entrySet()) {
            if (param.getValue() instanceof File) {
                File file = (File) param.getValue();
                Headers partHeaders = Headers.of("Content-Disposition", "form-data; name=\"" + param.getKey() + "\"; filename=\"" + file.getName() + "\"");
                MediaType mediaType = MediaType.parse(guessContentTypeFromFile(file));
                mpBuilder.addPart(partHeaders, RequestBody.create(file, mediaType));
            } else {
                Headers partHeaders = Headers.of("Content-Disposition", "form-data; name=\"" + param.getKey() + "\"");
                mpBuilder.addPart(partHeaders, RequestBody.create(parameterToString(param.getValue()), null));
            }
        }
        return mpBuilder.build();
    }

    /**
     * 从给定文件中猜测 Content-Type 标头（默认为“application/octet-stream”）。
     *
     * @param file 给定的文件
     * @return 猜测的Content-Type
     */
    public String guessContentTypeFromFile(File file) {
        String contentType = URLConnection.guessContentTypeFromName(file.getName());
        if (contentType == null) {
            return "application/octet-stream";
        } else {
            return contentType;
        }
    }

    /**
     * 根据当前的值将SSL相关设置应用到httpClient
     * 验证Ssl 和sslCaCert。
     */
    private void applySslSettings() {
        try {
            TrustManager[] trustManagers;
            HostnameVerifier hostnameVerifier;
            if (!verifyingSsl) {
                trustManagers = new TrustManager[]{
                        new X509TrustManager() {
                            @Override
                            public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                            }

                            @Override
                            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                            }

                            @Override
                            public X509Certificate[] getAcceptedIssuers() {
                                return new X509Certificate[]{};
                            }
                        }
                };
                hostnameVerifier = new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                };
            } else {
                TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());

                if (sslCaCert == null) {
                    trustManagerFactory.init((KeyStore) null);
                } else {
                    char[] password = null; // Any password will work.
                    CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
                    Collection<? extends Certificate> certificates = certificateFactory.generateCertificates(sslCaCert);
                    if (certificates.isEmpty()) {
                        throw new IllegalArgumentException("expected non-empty set of trusted certificates");
                    }
                    KeyStore caKeyStore = newEmptyKeyStore(password);
                    int index = 0;
                    for (Certificate certificate : certificates) {
                        String certificateAlias = "ca" + Integer.toString(index++);
                        caKeyStore.setCertificateEntry(certificateAlias, certificate);
                    }
                    trustManagerFactory.init(caKeyStore);
                }
                trustManagers = trustManagerFactory.getTrustManagers();
                hostnameVerifier = OkHostnameVerifier.INSTANCE;
            }

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(keyManagers, trustManagers, new SecureRandom());
            httpClient = httpClient.newBuilder()
                    .sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustManagers[0])
                    .hostnameVerifier(hostnameVerifier)
                    .build();
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }
    }

    private KeyStore newEmptyKeyStore(char[] password) throws GeneralSecurityException {
        try {
            KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null, password);
            return keyStore;
        } catch (IOException e) {
            throw new AssertionError(e);
        }
    }

    /**
     * 获取网络拦截器将其添加到httpClient以跟踪下载进度
     * 异步请求。
     */
    private static Interceptor getProgressInterceptor() {
        return new Interceptor() {
            @Override
            public Response intercept(Chain chain) throws IOException {
                final Request request = chain.request();
                final Response originalResponse = chain.proceed(request);
                if (request.tag() instanceof ApiCallback) {
                    final ApiCallback callback = (ApiCallback) request.tag();
                    return originalResponse.newBuilder()
                            .body(new ProgressResponseBody(originalResponse.body(), callback))
                            .build();
                }
                return originalResponse;
            }
        };
    }
}
