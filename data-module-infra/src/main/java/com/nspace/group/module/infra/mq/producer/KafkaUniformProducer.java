package com.nspace.group.module.infra.mq.producer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * kafka 消息生产者
 *
 * @author：ya<PERSON><PERSON><PERSON><PERSON>@gycloud.com
 * @date： 2025/1/7
 * @time：12:09
 */
@Component
@Slf4j
public class KafkaUniformProducer {

    @Resource
    private KafkaTemplate<Object, Object> kafkaTemplate;

    public void send(String topic, @NotNull String message) {
        try {
            SendResult<Object, Object> objectObjectSendResult = kafkaTemplate.send(topic, message).get();
            log.info("KafkaUniformProducer send message success,topic:{},result:{}", topic, objectObjectSendResult);
        } catch (Exception e) {
            log.error("KafkaUniformProducer send message error,topic:{},message:{},error:{}", topic, message, e.getMessage());
        }
    }
}
