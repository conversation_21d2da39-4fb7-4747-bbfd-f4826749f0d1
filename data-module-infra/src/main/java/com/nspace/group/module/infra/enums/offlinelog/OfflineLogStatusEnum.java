package com.nspace.group.module.infra.enums.offlinelog;

import cn.hutool.core.util.NumberUtil;
import com.nspace.group.framework.common.core.IntArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum OfflineLogStatusEnum implements IntArrayValuable {
    SUCCESS(0, "完成"),
    FAILED(1, "失败"),
    ONGOING(2, "进行中");//暂时不用

    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String desc;

    public boolean isSelf(String status) {
        return isOfflineLogStatus(status) && this.getStatus().equals(Integer.valueOf(status));
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(OfflineLogStatusEnum::getStatus).toArray();

    public static boolean isOfflineLogStatus(String statusString) {
        if (!NumberUtil.isInteger(statusString)) return false;
        Integer status = Integer.valueOf(statusString);
        return Arrays.stream(ARRAYS).boxed().anyMatch(value -> value.equals(status));
    }


    @Override
    public int[] array() {
        return ARRAYS;
    }

}
