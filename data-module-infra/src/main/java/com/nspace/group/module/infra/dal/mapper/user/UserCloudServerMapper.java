package com.nspace.group.module.infra.dal.mapper.user;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.nspace.group.framework.mybatis.core.mapper.BaseMapperX;
import com.nspace.group.module.infra.dal.dataobject.cloudvendor.LiveDomainDO;
import com.nspace.group.module.infra.dal.dataobject.user.UserCloudServerDO;
import com.nspace.group.module.infra.service.user.dto.LogDeliveryDomainDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;


/**
 * 用户开通的云服务相关配置
 *
 * @author：<EMAIL>
 * @date： 2024/12/16
 * @time：17:32
 */
@DS("nspace_controller")
@Mapper
public interface UserCloudServerMapper extends BaseMapperX<UserCloudServerDO> {

    @Select({
            "<script>",
            "SELECT ucs.id AS userCloudId, ld.* ",
            "FROM user_cloud_server ucs ",
            "LEFT JOIN live_domain ld ON ucs.tenant_id = ld.tenant_id ",
            "WHERE ucs.deleted = 0 ",
            "AND ld.deleted = 0 ",
            "AND ucs.code = 'gylss' ",
            "<if test='domainType != null'>",
            "AND ld.type = #{domainType} ",
            "</if>",
            "<if test='cloundType != null'>",
            "AND CAST((ucs.ext_info::json->>'${cloundType}') AS INT) = #{status,jdbcType=INTEGER}",
            "</if>",
            "</script>"
    })
    Page<LiveDomainDO> selectUserCloudServerWithDomain(
            @Param("domainType") String domainType,
            @Param("cloundType") String cloundType,
            @Param("status") Integer status,
            @Param("page") Page<LiveDomainDO> page
    );

    @Select({
            "<script>",
            "SELECT ",
            "ucs.code AS service_code,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,endpoint}' as endpoint,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,path}' as path,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,ak}' as ak,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,sk}' as sk,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,limit}' as limit,",
            "ld.tenant_id,",
            "ld.domain,",
            "ld.type",
            "FROM user_cloud_server ucs ",
            "LEFT JOIN live_domain ld ON ucs.tenant_id = ld.tenant_id ",
            "WHERE ucs.deleted = 0 ",
            "AND ucs.status = '0'",
            "AND ld.deleted = 0 ",
            "AND ld.status = 0 ",
            "AND ucs.code = #{serviceType} ",
            "AND ucs.ext_info IS NOT NULL ",
            "AND ucs.ext_info ~ '^\\s*\\{.*\\}\\s*$' ",
            "<if test='domainType != null'>",
            "AND ld.type = #{domainType} ",
            "</if>",
            "AND (ucs.ext_info::jsonb #>> '{log_delivery_config,enable}')::boolean = true",
            "AND (ucs.ext_info::jsonb #>> '{log_delivery_config,delivery_vendor}') = #{target}",
            "</script>"
    })
    Page<LogDeliveryDomainDTO> selectLiveLogDeliveryDomainList(Page<LiveDomainDO> page, @Param("serviceType") String serviceType,
                                                               @Param("domainType") String domainType, @Param("target") String target);

    @Select({
            "<script>",
            "SELECT ",
            "ucs.code AS service_code,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,endpoint}' as endpoint,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,path}' as path,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,ak}' as ak,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,sk}' as sk,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,limit}' as limit,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,project}' as project,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,logstore}' as logStore,",
            "cd.tenant_id,",
            "cd.domain,",
            "cd.type",
            "FROM user_cloud_server ucs ",
            "LEFT JOIN cdn_domain cd ON ucs.tenant_id = cd.tenant_id ",
            "WHERE ucs.deleted = 0 ",
            "AND ucs.status = '0'",
            "AND cd.deleted = 0 ",
            "AND cd.status = 0 ",
            "AND ucs.code = #{serviceType} ",
            "AND ucs.ext_info IS NOT NULL ",
            "AND ucs.ext_info ~ '^\\s*\\{.*\\}\\s*$' ",
            "<if test='domainType != null'>",
            "AND cd.type = #{domainType} ",
            "</if>",
            "AND (ucs.ext_info::jsonb #>> '{log_delivery_config,enable}')::boolean = true",
            "AND (ucs.ext_info::jsonb #>> '{log_delivery_config,delivery_vendor}') = #{target}",
            "</script>"
    })
    Page<LogDeliveryDomainDTO> selectCdnLogDeliveryDomainList(Page<Object> page, @Param("serviceType") String serviceType,
                                                              @Param("domainType") String domainType, @Param("target") String target);

    @Select({
            "<script>",
            "SELECT ",
            "ucs.code AS service_code,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,endpoint}' as endpoint,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,path}' as path,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,ak}' as ak,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,sk}' as sk,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,limit}' as limit,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,retry_count}' as retry_count,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,project}' as project,",
            "ucs.ext_info::jsonb #>> '{log_delivery_config,logstore}' as logStore ",
            "FROM user_cloud_server ucs ",
            "WHERE ucs.deleted = 0 ",
            "AND ucs.status = '0'",
            "AND ucs.code = #{serviceType} ",
            "AND ucs.ext_info IS NOT NULL ",
            "AND ucs.ext_info ~ '^\\s*\\{.*\\}\\s*$' ",
            "AND (ucs.ext_info::jsonb #>> '{log_delivery_config,enable}')::boolean = true",
            "AND (ucs.ext_info::jsonb #>> '{log_delivery_config,delivery_vendor}') = #{target}",
            "</script>"
    })
    List<LogDeliveryDomainDTO> selectLogDeliveryConfigList(@Param("serviceType") String serviceType, @Param("target") String target);
}
