package com.nspace.group.module.infra.config;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.nspace.group.module.infra.service.cloudvendor.VendorAccountService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Set;

@Configuration
public class LocalCacheConfig {

    @Bean("platformNumCaffeineCache")
    public LoadingCache<String, Integer> platformNumCaffeineCache(VendorAccountService vendorAccountService) {
        return Caffeine.newBuilder()
                .maximumSize(10_000)
                .expireAfterWrite(Duration.ofDays(1L))
                .build(CacheLoader.bulk((Set<? extends String> keys) -> vendorAccountService.getVendorPlatformNumMap()));
    }
}
