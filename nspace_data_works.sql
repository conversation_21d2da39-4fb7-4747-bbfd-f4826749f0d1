--
-- PostgreSQL database dump
--

-- Dumped from database version 14.1
-- Dumped by pg_dump version 14.1

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: root; Type: SCHEMA; Schema: -; Owner: root
--

CREATE SCHEMA root;


ALTER SCHEMA root OWNER TO root;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: infra_job; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.infra_job (
    id bigint NOT NULL,
    name character varying(32) NOT NULL,
    status smallint NOT NULL,
    handler_name character varying(64) NOT NULL,
    handler_param character varying(255),
    cron_expression character varying(32) NOT NULL,
    retry_count integer NOT NULL,
    retry_interval integer NOT NULL,
    monitor_timeout integer NOT NULL,
    creator character varying(64),
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updater character varying(64),
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted smallint DEFAULT 0 NOT NULL
);


ALTER TABLE root.infra_job OWNER TO root;

--
-- Name: TABLE infra_job; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.infra_job IS '定时任务表';


--
-- Name: COLUMN infra_job.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.id IS '任务编号';


--
-- Name: COLUMN infra_job.name; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.name IS '任务名称';


--
-- Name: COLUMN infra_job.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.status IS '任务状态';


--
-- Name: COLUMN infra_job.handler_name; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.handler_name IS '处理器的名字';


--
-- Name: COLUMN infra_job.handler_param; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.handler_param IS '处理器的参数';


--
-- Name: COLUMN infra_job.cron_expression; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.cron_expression IS 'CRON 表达式';


--
-- Name: COLUMN infra_job.retry_count; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.retry_count IS '重试次数';


--
-- Name: COLUMN infra_job.retry_interval; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.retry_interval IS '重试间隔';


--
-- Name: COLUMN infra_job.monitor_timeout; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.monitor_timeout IS '监控超时时间';


--
-- Name: COLUMN infra_job.creator; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.creator IS '创建者';


--
-- Name: COLUMN infra_job.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.create_time IS '创建时间';


--
-- Name: COLUMN infra_job.updater; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.updater IS '更新者';


--
-- Name: COLUMN infra_job.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.update_time IS '更新时间';


--
-- Name: COLUMN infra_job.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job.deleted IS '是否删除';


--
-- Name: infra_job_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE SEQUENCE root.infra_job_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE root.infra_job_id_seq OWNER TO root;

--
-- Name: infra_job_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER SEQUENCE root.infra_job_id_seq OWNED BY root.infra_job.id;


--
-- Name: infra_job_log; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.infra_job_log (
    id bigint NOT NULL,
    job_id bigint NOT NULL,
    handler_name character varying(64) NOT NULL,
    handler_param character varying(255),
    execute_index smallint NOT NULL,
    begin_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    end_time timestamp(6) without time zone,
    duration integer,
    status smallint NOT NULL,
    result character varying(4000),
    creator character varying(64),
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updater character varying(64),
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted smallint DEFAULT 0 NOT NULL
);


ALTER TABLE root.infra_job_log OWNER TO root;

--
-- Name: TABLE infra_job_log; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.infra_job_log IS '定时任务日志表';


--
-- Name: COLUMN infra_job_log.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.id IS '日志编号';


--
-- Name: COLUMN infra_job_log.job_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.job_id IS '任务编号';


--
-- Name: COLUMN infra_job_log.handler_name; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.handler_name IS '处理器的名字';


--
-- Name: COLUMN infra_job_log.handler_param; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.handler_param IS '处理器的参数';


--
-- Name: COLUMN infra_job_log.execute_index; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.execute_index IS '第几次执行';


--
-- Name: COLUMN infra_job_log.begin_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.begin_time IS '开始执行时间';


--
-- Name: COLUMN infra_job_log.end_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.end_time IS '结束执行时间';


--
-- Name: COLUMN infra_job_log.duration; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.duration IS '执行时长';


--
-- Name: COLUMN infra_job_log.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.status IS '任务状态';


--
-- Name: COLUMN infra_job_log.result; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.result IS '结果数据';


--
-- Name: COLUMN infra_job_log.creator; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.creator IS '创建者';


--
-- Name: COLUMN infra_job_log.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.create_time IS '创建时间';


--
-- Name: COLUMN infra_job_log.updater; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.updater IS '更新者';


--
-- Name: COLUMN infra_job_log.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.update_time IS '更新时间';


--
-- Name: COLUMN infra_job_log.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.infra_job_log.deleted IS '是否删除';


--
-- Name: infra_job_log_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE SEQUENCE root.infra_job_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE root.infra_job_log_id_seq OWNER TO root;

--
-- Name: infra_job_log_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER SEQUENCE root.infra_job_log_id_seq OWNED BY root.infra_job_log.id;


--
-- Name: qrtz_blob_triggers; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_blob_triggers (
    sched_name character varying(120) NOT NULL,
    trigger_name character varying(200) NOT NULL,
    trigger_group character varying(200) NOT NULL,
    blob_data bytea
);


ALTER TABLE root.qrtz_blob_triggers OWNER TO root;

--
-- Name: qrtz_calendars; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_calendars (
    sched_name character varying(120) NOT NULL,
    calendar_name character varying(200) NOT NULL,
    calendar bytea NOT NULL
);


ALTER TABLE root.qrtz_calendars OWNER TO root;

--
-- Name: qrtz_cron_triggers; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_cron_triggers (
    sched_name character varying(120) NOT NULL,
    trigger_name character varying(200) NOT NULL,
    trigger_group character varying(200) NOT NULL,
    cron_expression character varying(120) NOT NULL,
    time_zone_id character varying(80)
);


ALTER TABLE root.qrtz_cron_triggers OWNER TO root;

--
-- Name: qrtz_fired_triggers; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_fired_triggers (
    sched_name character varying(120) NOT NULL,
    entry_id character varying(95) NOT NULL,
    trigger_name character varying(200) NOT NULL,
    trigger_group character varying(200) NOT NULL,
    instance_name character varying(200) NOT NULL,
    fired_time bigint NOT NULL,
    sched_time bigint NOT NULL,
    priority integer NOT NULL,
    state character varying(16) NOT NULL,
    job_name character varying(200),
    job_group character varying(200),
    is_nonconcurrent boolean,
    requests_recovery boolean
);


ALTER TABLE root.qrtz_fired_triggers OWNER TO root;

--
-- Name: qrtz_job_details; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_job_details (
    sched_name character varying(120) NOT NULL,
    job_name character varying(200) NOT NULL,
    job_group character varying(200) NOT NULL,
    description character varying(250),
    job_class_name character varying(250) NOT NULL,
    is_durable boolean NOT NULL,
    is_nonconcurrent boolean NOT NULL,
    is_update_data boolean NOT NULL,
    requests_recovery boolean NOT NULL,
    job_data bytea
);


ALTER TABLE root.qrtz_job_details OWNER TO root;

--
-- Name: qrtz_locks; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_locks (
    sched_name character varying(120) NOT NULL,
    lock_name character varying(40) NOT NULL
);


ALTER TABLE root.qrtz_locks OWNER TO root;

--
-- Name: qrtz_paused_trigger_grps; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_paused_trigger_grps (
    sched_name character varying(120) NOT NULL,
    trigger_group character varying(200) NOT NULL
);


ALTER TABLE root.qrtz_paused_trigger_grps OWNER TO root;

--
-- Name: qrtz_scheduler_state; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_scheduler_state (
    sched_name character varying(120) NOT NULL,
    instance_name character varying(200) NOT NULL,
    last_checkin_time bigint NOT NULL,
    checkin_interval bigint NOT NULL
);


ALTER TABLE root.qrtz_scheduler_state OWNER TO root;

--
-- Name: qrtz_simple_triggers; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_simple_triggers (
    sched_name character varying(120) NOT NULL,
    trigger_name character varying(200) NOT NULL,
    trigger_group character varying(200) NOT NULL,
    repeat_count bigint NOT NULL,
    repeat_interval bigint NOT NULL,
    times_triggered bigint NOT NULL
);


ALTER TABLE root.qrtz_simple_triggers OWNER TO root;

--
-- Name: qrtz_simprop_triggers; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_simprop_triggers (
    sched_name character varying(120) NOT NULL,
    trigger_name character varying(200) NOT NULL,
    trigger_group character varying(200) NOT NULL,
    str_prop_1 character varying(512),
    str_prop_2 character varying(512),
    str_prop_3 character varying(512),
    int_prop_1 integer,
    int_prop_2 integer,
    long_prop_1 bigint,
    long_prop_2 bigint,
    dec_prop_1 numeric(13,4),
    dec_prop_2 numeric(13,4),
    bool_prop_1 boolean,
    bool_prop_2 boolean
);


ALTER TABLE root.qrtz_simprop_triggers OWNER TO root;

--
-- Name: qrtz_triggers; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.qrtz_triggers (
    sched_name character varying(120) NOT NULL,
    trigger_name character varying(200) NOT NULL,
    trigger_group character varying(200) NOT NULL,
    job_name character varying(200) NOT NULL,
    job_group character varying(200) NOT NULL,
    description character varying(250),
    next_fire_time bigint,
    prev_fire_time bigint,
    priority integer,
    trigger_state character varying(16) NOT NULL,
    trigger_type character varying(8) NOT NULL,
    start_time bigint NOT NULL,
    end_time bigint,
    calendar_name character varying(200),
    misfire_instr smallint,
    job_data bytea
);


ALTER TABLE root.qrtz_triggers OWNER TO root;

--
-- Name: infra_job id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.infra_job ALTER COLUMN id SET DEFAULT nextval('root.infra_job_id_seq'::regclass);


--
-- Name: infra_job_log id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.infra_job_log ALTER COLUMN id SET DEFAULT nextval('root.infra_job_log_id_seq'::regclass);


--
-- Data for Name: infra_job; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.infra_job (id, name, status, handler_name, handler_param, cron_expression, retry_count, retry_interval, monitor_timeout, creator, create_time, updater, update_time, deleted) FROM stdin;
10	计费用量-直播拉流带宽和流量统计	1	fusionUsagePullBandwidthFluxJob	{"timeout":7200000,"interval":15,"domain_type":"PULL"}	0 */5 * * * ?	3	1000	0	\N	2024-11-08 16:56:58.009344	\N	2024-11-08 16:56:58.009344	0
11	计费用量-直播推流带宽和流量统计	1	fusionUsagePushBandwidthFluxJob	{"timeout":7200000,"interval":15,"domain_type":"PUSH"}	0 */5 * * * ?	3	1000	0	\N	2024-11-08 16:56:58.597919	\N	2024-11-08 16:56:58.597919	0
12	实时监控-推流流量带宽/推流质量监控	1	fusionRealtimeStreamPushInfoJob	{"timeout":7200000,"interval":1,"domain_type":"PUSH"}	0 * * * * ?	3	1000	0	\N	2024-11-08 16:56:59.199845	\N	2024-11-08 16:56:59.199845	0
13	实时监控-获取在线用户数据	1	fusionRealtimePullOnlineUserJob	{"timeout":7200000,"interval":1,"domain_type":"PULL"}	0 * * * * ?	3	1000	0	\N	2024-11-08 16:56:59.769827	\N	2024-11-08 16:56:59.769827	0
14	实时监控-运营商数据	1	fusionRealtimePullIspInfoJob	{"timeout":7200000,"interval":1,"domain_type":"PULL"}	0 * * * * ?	3	1000	0	\N	2024-11-08 16:57:00.326123	\N	2024-11-08 16:57:00.326123	0
15	实时监控-播放状态码数据	1	fusionRealtimePullHttpcodeJob	{"timeout":7200000,"interval":1,"domain_type":"PULL"}	0 * * * * ?	3	1000	0	\N	2024-11-08 16:57:00.870584	\N	2024-11-08 16:57:00.870584	0
16	实时监控-直播拉流带宽和流量	1	fusionRealtimePullBandwidthFluxJob	{"timeout":7200000,"interval":1,"domain_type":"PULL"}	0 * * * * ?	3	1000	0	\N	2024-11-08 16:57:01.696868	\N	2024-11-08 16:57:01.696868	0
\.

--
-- Data for Name: qrtz_blob_triggers; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_blob_triggers (sched_name, trigger_name, trigger_group, blob_data) FROM stdin;
\.


--
-- Data for Name: qrtz_calendars; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_calendars (sched_name, calendar_name, calendar) FROM stdin;
\.


--
-- Data for Name: qrtz_cron_triggers; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_cron_triggers (sched_name, trigger_name, trigger_group, cron_expression, time_zone_id) FROM stdin;
schedulerName	fusionUsagePullBandwidthFluxJob	DEFAULT	0 */5 * * * ?	Asia/Shanghai
schedulerName	fusionUsagePushBandwidthFluxJob	DEFAULT	0 */5 * * * ?	Asia/Shanghai
schedulerName	fusionRealtimePullOnlineUserJob	DEFAULT	0 * * * * ?	Asia/Shanghai
schedulerName	fusionRealtimePullBandwidthFluxJob	DEFAULT	0 * * * * ?	Asia/Shanghai
schedulerName	fusionRealtimePullIspInfoJob	DEFAULT	0 * * * * ?	Asia/Shanghai
schedulerName	fusionRealtimeStreamPushInfoJob	DEFAULT	0 * * * * ?	Asia/Shanghai
schedulerName	fusionRealtimePullHttpcodeJob	DEFAULT	0 * * * * ?	Asia/Shanghai
\.


--
-- Data for Name: qrtz_fired_triggers; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_fired_triggers (sched_name, entry_id, trigger_name, trigger_group, instance_name, fired_time, sched_time, priority, state, job_name, job_group, is_nonconcurrent, requests_recovery) FROM stdin;
schedulerName	gwdeMacBook-Pro.local17310584186231731058418626	fusionRealtimePullOnlineUserJob	DEFAULT	gwdeMacBook-Pro.local1731058418623	1731058535844	1731058560000	5	ACQUIRED	\N	\N	f	f
\.


--
-- Data for Name: qrtz_job_details; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_job_details (sched_name, job_name, job_group, description, job_class_name, is_durable, is_nonconcurrent, is_update_data, requests_recovery, job_data) FROM stdin;
schedulerName	fusionUsagePullBandwidthFluxJob	DEFAULT	\N	com.nspace.group.framework.job.core.handler.JobHandlerInvoker	f	t	t	f	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000027400064a4f425f49447372000e6a6176612e6c616e672e4c6f6e673b8be490cc8f23df0200014a000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000000000000000a7400104a4f425f48414e444c45525f4e414d4574001f667573696f6e557361676550756c6c42616e647769647468466c75784a6f627800
schedulerName	fusionUsagePushBandwidthFluxJob	DEFAULT	\N	com.nspace.group.framework.job.core.handler.JobHandlerInvoker	f	t	t	f	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000027400064a4f425f49447372000e6a6176612e6c616e672e4c6f6e673b8be490cc8f23df0200014a000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000000000000000b7400104a4f425f48414e444c45525f4e414d4574001f667573696f6e55736167655075736842616e647769647468466c75784a6f627800
schedulerName	fusionRealtimeStreamPushInfoJob	DEFAULT	\N	com.nspace.group.framework.job.core.handler.JobHandlerInvoker	f	t	t	f	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000027400064a4f425f49447372000e6a6176612e6c616e672e4c6f6e673b8be490cc8f23df0200014a000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000000000000000c7400104a4f425f48414e444c45525f4e414d4574001f667573696f6e5265616c74696d6553747265616d50757368496e666f4a6f627800
schedulerName	fusionRealtimePullOnlineUserJob	DEFAULT	\N	com.nspace.group.framework.job.core.handler.JobHandlerInvoker	f	t	t	f	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000027400064a4f425f49447372000e6a6176612e6c616e672e4c6f6e673b8be490cc8f23df0200014a000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000000000000000d7400104a4f425f48414e444c45525f4e414d4574001f667573696f6e5265616c74696d6550756c6c4f6e6c696e65557365724a6f627800
schedulerName	fusionRealtimePullIspInfoJob	DEFAULT	\N	com.nspace.group.framework.job.core.handler.JobHandlerInvoker	f	t	t	f	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000027400064a4f425f49447372000e6a6176612e6c616e672e4c6f6e673b8be490cc8f23df0200014a000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000000000000000e7400104a4f425f48414e444c45525f4e414d4574001c667573696f6e5265616c74696d6550756c6c497370496e666f4a6f627800
schedulerName	fusionRealtimePullHttpcodeJob	DEFAULT	\N	com.nspace.group.framework.job.core.handler.JobHandlerInvoker	f	t	t	f	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000027400064a4f425f49447372000e6a6176612e6c616e672e4c6f6e673b8be490cc8f23df0200014a000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000000000000000f7400104a4f425f48414e444c45525f4e414d4574001d667573696f6e5265616c74696d6550756c6c48747470636f64654a6f627800
schedulerName	fusionRealtimePullBandwidthFluxJob	DEFAULT	\N	com.nspace.group.framework.job.core.handler.JobHandlerInvoker	f	t	t	f	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000027400064a4f425f49447372000e6a6176612e6c616e672e4c6f6e673b8be490cc8f23df0200014a000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b020000787000000000000000107400104a4f425f48414e444c45525f4e414d45740022667573696f6e5265616c74696d6550756c6c42616e647769647468466c75784a6f627800
\.


--
-- Data for Name: qrtz_locks; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_locks (sched_name, lock_name) FROM stdin;
schedulerName	STATE_ACCESS
schedulerName	TRIGGER_ACCESS
\.


--
-- Data for Name: qrtz_paused_trigger_grps; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_paused_trigger_grps (sched_name, trigger_group) FROM stdin;
\.


--
-- Data for Name: qrtz_scheduler_state; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_scheduler_state (sched_name, instance_name, last_checkin_time, checkin_interval) FROM stdin;
schedulerName	gwdeMacBook-Pro.local1731058418623	1731058539558	15000
\.


--
-- Data for Name: qrtz_simple_triggers; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_simple_triggers (sched_name, trigger_name, trigger_group, repeat_count, repeat_interval, times_triggered) FROM stdin;
\.


--
-- Data for Name: qrtz_simprop_triggers; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_simprop_triggers (sched_name, trigger_name, trigger_group, str_prop_1, str_prop_2, str_prop_3, int_prop_1, int_prop_2, long_prop_1, long_prop_2, dec_prop_1, dec_prop_2, bool_prop_1, bool_prop_2) FROM stdin;
\.


--
-- Data for Name: qrtz_triggers; Type: TABLE DATA; Schema: root; Owner: root
--

COPY root.qrtz_triggers (sched_name, trigger_name, trigger_group, job_name, job_group, description, next_fire_time, prev_fire_time, priority, trigger_state, trigger_type, start_time, end_time, calendar_name, misfire_instr, job_data) FROM stdin;
schedulerName	fusionRealtimePullOnlineUserJob	DEFAULT	fusionRealtimePullOnlineUserJob	DEFAULT	\N	1731058560000	1731058500000	5	ACQUIRED	CRON	1731056219000	0	\N	0	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000037400114a4f425f48414e444c45525f504152414d7400357b2274696d656f7574223a373230303030302c22696e74657276616c223a312c22646f6d61696e5f74797065223a2250554c4c227d7400124a4f425f52455452595f494e54455256414c737200116a6176612e6c616e672e496e746567657212e2a0a4f781873802000149000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000003e874000f4a4f425f52455452595f434f554e547371007e000a000000037800
schedulerName	fusionUsagePullBandwidthFluxJob	DEFAULT	fusionUsagePullBandwidthFluxJob	DEFAULT	\N	1731058800000	1731058500000	5	WAITING	CRON	1731056218000	0	\N	0	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000037400114a4f425f48414e444c45525f504152414d7400367b2274696d656f7574223a373230303030302c22696e74657276616c223a31352c22646f6d61696e5f74797065223a2250554c4c227d7400124a4f425f52455452595f494e54455256414c737200116a6176612e6c616e672e496e746567657212e2a0a4f781873802000149000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000003e874000f4a4f425f52455452595f434f554e547371007e000a000000037800
schedulerName	fusionUsagePushBandwidthFluxJob	DEFAULT	fusionUsagePushBandwidthFluxJob	DEFAULT	\N	1731058800000	1731058500000	5	WAITING	CRON	1731056218000	0	\N	0	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000037400114a4f425f48414e444c45525f504152414d7400367b2274696d656f7574223a373230303030302c22696e74657276616c223a31352c22646f6d61696e5f74797065223a2250555348227d7400124a4f425f52455452595f494e54455256414c737200116a6176612e6c616e672e496e746567657212e2a0a4f781873802000149000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000003e874000f4a4f425f52455452595f434f554e547371007e000a000000037800
schedulerName	fusionRealtimePullBandwidthFluxJob	DEFAULT	fusionRealtimePullBandwidthFluxJob	DEFAULT	\N	1731058560000	1731058500000	5	WAITING	CRON	1731056221000	0	\N	0	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000037400114a4f425f48414e444c45525f504152414d7400357b2274696d656f7574223a373230303030302c22696e74657276616c223a312c22646f6d61696e5f74797065223a2250554c4c227d7400124a4f425f52455452595f494e54455256414c737200116a6176612e6c616e672e496e746567657212e2a0a4f781873802000149000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000003e874000f4a4f425f52455452595f434f554e547371007e000a000000037800
schedulerName	fusionRealtimePullIspInfoJob	DEFAULT	fusionRealtimePullIspInfoJob	DEFAULT	\N	1731058560000	1731058500000	5	WAITING	CRON	1731056220000	0	\N	0	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000037400114a4f425f48414e444c45525f504152414d7400357b2274696d656f7574223a373230303030302c22696e74657276616c223a312c22646f6d61696e5f74797065223a2250554c4c227d7400124a4f425f52455452595f494e54455256414c737200116a6176612e6c616e672e496e746567657212e2a0a4f781873802000149000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000003e874000f4a4f425f52455452595f434f554e547371007e000a000000037800
schedulerName	fusionRealtimeStreamPushInfoJob	DEFAULT	fusionRealtimeStreamPushInfoJob	DEFAULT	\N	1731058560000	1731058500000	5	WAITING	CRON	1731056219000	0	\N	0	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000037400114a4f425f48414e444c45525f504152414d7400357b2274696d656f7574223a373230303030302c22696e74657276616c223a312c22646f6d61696e5f74797065223a2250555348227d7400124a4f425f52455452595f494e54455256414c737200116a6176612e6c616e672e496e746567657212e2a0a4f781873802000149000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000003e874000f4a4f425f52455452595f434f554e547371007e000a000000037800
schedulerName	fusionRealtimePullHttpcodeJob	DEFAULT	fusionRealtimePullHttpcodeJob	DEFAULT	\N	1731058560000	1731058500000	5	WAITING	CRON	1731056220000	0	\N	0	\\xaced0005737200156f72672e71756172747a2e4a6f62446174614d61709fb083e8bfa9b0cb020000787200266f72672e71756172747a2e7574696c732e537472696e674b65794469727479466c61674d61708208e8c3fbc55d280200015a0013616c6c6f77735472616e7369656e74446174617872001d6f72672e71756172747a2e7574696c732e4469727479466c61674d617013e62ead28760ace0200025a000564697274794c00036d617074000f4c6a6176612f7574696c2f4d61703b787001737200116a6176612e7574696c2e486173684d61700507dac1c31660d103000246000a6c6f6164466163746f724900097468726573686f6c6478703f4000000000000c770800000010000000037400114a4f425f48414e444c45525f504152414d7400357b2274696d656f7574223a373230303030302c22696e74657276616c223a312c22646f6d61696e5f74797065223a2250554c4c227d7400124a4f425f52455452595f494e54455256414c737200116a6176612e6c616e672e496e746567657212e2a0a4f781873802000149000576616c7565787200106a6176612e6c616e672e4e756d62657286ac951d0b94e08b0200007870000003e874000f4a4f425f52455452595f434f554e547371007e000a000000037800
\.


--
-- Name: infra_job_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.infra_job_id_seq', 16, true);


--
-- Name: infra_job_log_id_seq; Type: SEQUENCE SET; Schema: root; Owner: root
--

SELECT pg_catalog.setval('root.infra_job_log_id_seq', 2457, true);


--
-- Name: infra_job_log infra_job_log_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.infra_job_log
    ADD CONSTRAINT infra_job_log_pkey PRIMARY KEY (id);


--
-- Name: infra_job infra_job_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.infra_job
    ADD CONSTRAINT infra_job_pkey PRIMARY KEY (id);


--
-- Name: qrtz_blob_triggers qrtz_blob_triggers_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_blob_triggers
    ADD CONSTRAINT qrtz_blob_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group);


--
-- Name: qrtz_calendars qrtz_calendars_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_calendars
    ADD CONSTRAINT qrtz_calendars_pkey PRIMARY KEY (sched_name, calendar_name);


--
-- Name: qrtz_cron_triggers qrtz_cron_triggers_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_cron_triggers
    ADD CONSTRAINT qrtz_cron_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group);


--
-- Name: qrtz_fired_triggers qrtz_fired_triggers_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_fired_triggers
    ADD CONSTRAINT qrtz_fired_triggers_pkey PRIMARY KEY (sched_name, entry_id);


--
-- Name: qrtz_job_details qrtz_job_details_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_job_details
    ADD CONSTRAINT qrtz_job_details_pkey PRIMARY KEY (sched_name, job_name, job_group);


--
-- Name: qrtz_locks qrtz_locks_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_locks
    ADD CONSTRAINT qrtz_locks_pkey PRIMARY KEY (sched_name, lock_name);


--
-- Name: qrtz_paused_trigger_grps qrtz_paused_trigger_grps_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_paused_trigger_grps
    ADD CONSTRAINT qrtz_paused_trigger_grps_pkey PRIMARY KEY (sched_name, trigger_group);


--
-- Name: qrtz_scheduler_state qrtz_scheduler_state_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_scheduler_state
    ADD CONSTRAINT qrtz_scheduler_state_pkey PRIMARY KEY (sched_name, instance_name);


--
-- Name: qrtz_simple_triggers qrtz_simple_triggers_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_simple_triggers
    ADD CONSTRAINT qrtz_simple_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group);


--
-- Name: qrtz_simprop_triggers qrtz_simprop_triggers_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_simprop_triggers
    ADD CONSTRAINT qrtz_simprop_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group);


--
-- Name: qrtz_triggers qrtz_triggers_pkey; Type: CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_triggers
    ADD CONSTRAINT qrtz_triggers_pkey PRIMARY KEY (sched_name, trigger_name, trigger_group);


--
-- Name: idx_qrtz_ft_inst_job_req_rcvry; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_ft_inst_job_req_rcvry ON root.qrtz_fired_triggers USING btree (sched_name, instance_name, requests_recovery);


--
-- Name: idx_qrtz_ft_j_g; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_ft_j_g ON root.qrtz_fired_triggers USING btree (sched_name, job_name, job_group);


--
-- Name: idx_qrtz_ft_jg; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_ft_jg ON root.qrtz_fired_triggers USING btree (sched_name, job_group);


--
-- Name: idx_qrtz_ft_t_g; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_ft_t_g ON root.qrtz_fired_triggers USING btree (sched_name, trigger_name, trigger_group);


--
-- Name: idx_qrtz_ft_tg; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_ft_tg ON root.qrtz_fired_triggers USING btree (sched_name, trigger_group);


--
-- Name: idx_qrtz_ft_trig_inst_name; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_ft_trig_inst_name ON root.qrtz_fired_triggers USING btree (sched_name, instance_name);


--
-- Name: idx_qrtz_j_grp; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_j_grp ON root.qrtz_job_details USING btree (sched_name, job_group);


--
-- Name: idx_qrtz_j_req_recovery; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_j_req_recovery ON root.qrtz_job_details USING btree (sched_name, requests_recovery);


--
-- Name: idx_qrtz_t_c; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_c ON root.qrtz_triggers USING btree (sched_name, calendar_name);


--
-- Name: idx_qrtz_t_g; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_g ON root.qrtz_triggers USING btree (sched_name, trigger_group);


--
-- Name: idx_qrtz_t_j; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_j ON root.qrtz_triggers USING btree (sched_name, job_name, job_group);


--
-- Name: idx_qrtz_t_jg; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_jg ON root.qrtz_triggers USING btree (sched_name, job_group);


--
-- Name: idx_qrtz_t_n_g_state; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_n_g_state ON root.qrtz_triggers USING btree (sched_name, trigger_group, trigger_state);


--
-- Name: idx_qrtz_t_n_state; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_n_state ON root.qrtz_triggers USING btree (sched_name, trigger_name, trigger_group, trigger_state);


--
-- Name: idx_qrtz_t_next_fire_time; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_next_fire_time ON root.qrtz_triggers USING btree (sched_name, next_fire_time);


--
-- Name: idx_qrtz_t_nft_misfire; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_nft_misfire ON root.qrtz_triggers USING btree (sched_name, misfire_instr, next_fire_time);


--
-- Name: idx_qrtz_t_nft_st; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_nft_st ON root.qrtz_triggers USING btree (sched_name, trigger_state, next_fire_time);


--
-- Name: idx_qrtz_t_nft_st_misfire; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_nft_st_misfire ON root.qrtz_triggers USING btree (sched_name, misfire_instr, next_fire_time, trigger_state);


--
-- Name: idx_qrtz_t_nft_st_misfire_grp; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_nft_st_misfire_grp ON root.qrtz_triggers USING btree (sched_name, misfire_instr, next_fire_time, trigger_group, trigger_state);


--
-- Name: idx_qrtz_t_state; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX idx_qrtz_t_state ON root.qrtz_triggers USING btree (sched_name, trigger_state);


--
-- Name: qrtz_blob_triggers qrtz_blob_triggers_sched_name_trigger_name_trigger_group_fkey; Type: FK CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_blob_triggers
    ADD CONSTRAINT qrtz_blob_triggers_sched_name_trigger_name_trigger_group_fkey FOREIGN KEY (sched_name, trigger_name, trigger_group) REFERENCES root.qrtz_triggers(sched_name, trigger_name, trigger_group);


--
-- Name: qrtz_cron_triggers qrtz_cron_triggers_sched_name_trigger_name_trigger_group_fkey; Type: FK CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_cron_triggers
    ADD CONSTRAINT qrtz_cron_triggers_sched_name_trigger_name_trigger_group_fkey FOREIGN KEY (sched_name, trigger_name, trigger_group) REFERENCES root.qrtz_triggers(sched_name, trigger_name, trigger_group);


--
-- Name: qrtz_simple_triggers qrtz_simple_triggers_sched_name_trigger_name_trigger_group_fkey; Type: FK CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_simple_triggers
    ADD CONSTRAINT qrtz_simple_triggers_sched_name_trigger_name_trigger_group_fkey FOREIGN KEY (sched_name, trigger_name, trigger_group) REFERENCES root.qrtz_triggers(sched_name, trigger_name, trigger_group);


--
-- Name: qrtz_simprop_triggers qrtz_simprop_triggers_sched_name_trigger_name_trigger_grou_fkey; Type: FK CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_simprop_triggers
    ADD CONSTRAINT qrtz_simprop_triggers_sched_name_trigger_name_trigger_grou_fkey FOREIGN KEY (sched_name, trigger_name, trigger_group) REFERENCES root.qrtz_triggers(sched_name, trigger_name, trigger_group);


--
-- Name: qrtz_triggers qrtz_triggers_sched_name_job_name_job_group_fkey; Type: FK CONSTRAINT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.qrtz_triggers
    ADD CONSTRAINT qrtz_triggers_sched_name_job_name_job_group_fkey FOREIGN KEY (sched_name, job_name, job_group) REFERENCES root.qrtz_job_details(sched_name, job_name, job_group);


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: root; Owner: root
--

ALTER DEFAULT PRIVILEGES FOR ROLE root IN SCHEMA root GRANT USAGE ON SEQUENCES  TO root;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: root; Owner: root
--

ALTER DEFAULT PRIVILEGES FOR ROLE root IN SCHEMA root GRANT SELECT,INSERT,DELETE,UPDATE ON TABLES  TO root;


--
-- Name: vendor_offline_log_info; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.vendor_offline_log_info
(
    id            bigint                 NOT NULL,
    tenant_id     bigint   DEFAULT 0     NOT NULL,
    domain        character varying(128) NOT NULL,
    platform      character varying(32)  NOT NULL,
    file_url      character varying(500) NOT NULL,
    file_time     timestamp(0) without time zone,
    generate_time timestamp(6) without time zone,
    file_name     character varying(512) NOT NULL,
    file_size     bigint,
    status        smallint               NOT NULL,
    creator       character varying(64),
    create_time   timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updater       character varying(64),
    update_time   timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted       smallint DEFAULT 0     NOT NULL
);


ALTER TABLE root.vendor_offline_log_info OWNER TO root;

--
-- Name: COLUMN vendor_offline_log_info.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.id IS '编号';


--
-- Name: TABLE vendor_offline_log_info; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.vendor_offline_log_info IS '第三方离线日志文件信息表';


--
-- Name: COLUMN vendor_offline_log_info.tenant_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.tenant_id IS '租户ID';


--
-- Name: COLUMN vendor_offline_log_info.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.domain IS '域名';


--
-- Name: COLUMN vendor_offline_log_info.platform; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.platform IS '三方平台code';


--
-- Name: COLUMN vendor_offline_log_info.file_url; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.file_url IS '文件下载链接';


--
-- Name: COLUMN vendor_offline_log_info.file_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.file_time IS '文件时间';


--
-- Name: COLUMN vendor_offline_log_info.generate_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.generate_time IS '时间';


--
-- Name: COLUMN vendor_offline_log_info.file_name; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.file_name IS '文件名称';



--
-- Name: COLUMN vendor_offline_log_info.file_size; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.file_size IS '文件大小';



--
-- Name: COLUMN vendor_offline_log_info.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.status IS '文件状态';


--
-- Name: COLUMN vendor_offline_log_info.creator; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.creator IS '创建者';


--
-- Name: COLUMN vendor_offline_log_info.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.create_time IS '创建时间';


--
-- Name: COLUMN vendor_offline_log_info.updater; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.updater IS '更新者';


--
-- Name: COLUMN vendor_offline_log_info.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.update_time IS '更新时间';


--
-- Name: COLUMN vendor_offline_log_info.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_info.deleted IS '是否删除';


--
-- Name: vendor_offline_log_info_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE SEQUENCE root.vendor_offline_log_info_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE root.vendor_offline_log_info_id_seq OWNER TO root;


--
-- Name: vendor_offline_log_info id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.vendor_offline_log_info ALTER COLUMN id SET DEFAULT nextval('root.vendor_offline_log_info_id_seq'::regclass);

--
-- Name: vendor_offline_log_info_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER SEQUENCE root.vendor_offline_log_info_id_seq OWNED BY root.vendor_offline_log_info.id;


ALTER TABLE ONLY root.vendor_offline_log_info
    ADD CONSTRAINT vendor_offline_log_info_pkey PRIMARY KEY (id);

--
-- Name: vendor_offline_log_info_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX vendor_offline_log_info_idx ON root.vendor_offline_log_info USING btree (tenant_id, domain, platform);


--
-- Name: vendor_offline_log_info_file_url_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX vendor_offline_log_info_file_url_idx ON root.vendor_offline_log_info USING btree (file_url);


--
-- Name: vendor_offline_log_process_record; Type: TABLE; Schema: root; Owner: root
--

CREATE TABLE root.vendor_offline_log_process_record
(
    id          bigint                 NOT NULL,
    tenant_id   bigint   DEFAULT 0     NOT NULL,
    domain      character varying(128) NOT NULL,
    platform    character varying(32)  NOT NULL,
    start_time  timestamp(0) without time zone NOT NULL,
    status      smallint               NOT NULL,
    creator     character varying(64),
    create_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updater     character varying(64),
    update_time timestamp(6) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted     smallint DEFAULT 0     NOT NULL
);


ALTER TABLE root.vendor_offline_log_process_record OWNER TO root;

--
-- Name: TABLE vendor_offline_log_process_record; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON TABLE root.vendor_offline_log_process_record IS '第三方离线日志处理记录表';


--
-- Name: COLUMN vendor_offline_log_process_record.id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.id IS '编号';


--
-- Name: COLUMN vendor_offline_log_process_record.tenant_id; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.tenant_id IS '租户ID';


--
-- Name: COLUMN vendor_offline_log_process_record.domain; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.domain IS '域名';


--
-- Name: COLUMN vendor_offline_log_process_record.platform; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.platform IS '三方平台code';


--
-- Name: COLUMN vendor_offline_log_process_record.start_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.start_time IS '开始时间';


--
-- Name: COLUMN vendor_offline_log_process_record.status; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.status IS '处理状态';


--
-- Name: COLUMN vendor_offline_log_process_record.creator; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.creator IS '创建者';


--
-- Name: COLUMN vendor_offline_log_process_record.create_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.create_time IS '创建时间';


--
-- Name: COLUMN vendor_offline_log_process_record.updater; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.updater IS '更新者';


--
-- Name: COLUMN vendor_offline_log_process_record.update_time; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.update_time IS '更新时间';


--
-- Name: COLUMN vendor_offline_log_process_record.deleted; Type: COMMENT; Schema: root; Owner: root
--

COMMENT ON COLUMN root.vendor_offline_log_process_record.deleted IS '是否删除';


--
-- Name: vendor_offline_log_process_record_id_seq; Type: SEQUENCE; Schema: root; Owner: root
--

CREATE
SEQUENCE root.vendor_offline_log_process_record_id_seq
    START
WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE
1;


ALTER TABLE root.vendor_offline_log_process_record_id_seq OWNER TO root;


--
-- Name: vendor_offline_log_process_record id; Type: DEFAULT; Schema: root; Owner: root
--

ALTER TABLE ONLY root.vendor_offline_log_process_record ALTER COLUMN id SET DEFAULT nextval('root.vendor_offline_log_process_record_id_seq'::regclass);

--
-- Name: vendor_offline_log_process_record_id_seq; Type: SEQUENCE OWNED BY; Schema: root; Owner: root
--

ALTER
SEQUENCE root.vendor_offline_log_process_record_id_seq OWNED BY root.vendor_offline_log_process_record.id;


ALTER TABLE ONLY root.vendor_offline_log_process_record
    ADD CONSTRAINT vendor_offline_log_process_record_pkey PRIMARY KEY (id);

--
-- Name: vendor_offline_log_process_record_idx; Type: INDEX; Schema: root; Owner: root
--

CREATE INDEX vendor_offline_log_process_record_idx ON root.vendor_offline_log_process_record USING btree (tenant_id, domain, platform);

CREATE TABLE root.flink_job_execution_record
(
    id        bigserial                          NOT NULL, -- 主键ID
    job_id    varchar(1000)                      NOT NULL, -- 任务执行ID
    job_name  varchar(1000)                      NOT NULL, -- 任务名称
    job_status  varchar(50)                      NOT NULL, -- 任务状态
    create_time      timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 数据写入时间
    update_time      timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL, -- 数据更新时间
    CONSTRAINT flink_job_execution_record_pkey PRIMARY KEY (id)
);
CREATE INDEX flink_job_execution_record_idx ON root.flink_job_execution_record USING btree (job_name);
COMMENT
ON TABLE root.flink_job_execution_record IS '异步任务执行记录-flink相关任务';
ALTER TABLE root.flink_job_execution_record OWNER TO root;

-- Column comments
COMMENT
ON COLUMN root.flink_job_execution_record.id IS '主键ID';
COMMENT
ON COLUMN root.flink_job_execution_record.job_id IS '任务执行ID';
COMMENT
ON COLUMN root.flink_job_execution_record.job_name IS '任务名称';
COMMENT
ON COLUMN root.flink_job_execution_record.job_status IS '任务状态';
COMMENT
ON COLUMN root.flink_job_execution_record.create_time IS '数据写入时间';
COMMENT
ON COLUMN root.flink_job_execution_record.update_time IS '数据更新时间';